-- Task Management Database Schema
-- Create database
CREATE DATABASE IF NOT EXISTS task_management;
USE task_management;

-- Create users table with role-based access
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role ENUM('Admin', 'User') DEFAULT 'User',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role)
);

-- Create login_logs table
CREATE TABLE IF NOT EXISTS login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    success BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp)
);

-- Create tasks table with enhanced fields
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    assignee_id INT,
    status ENUM('New', 'ToDo', 'In Progress', 'Blocked', 'Closed') DEFAULT 'New',
    due_date DATETIME,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assignee_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_assignee_id (assignee_id),
    INDEX idx_created_by (created_by),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date),
    INDEX idx_created_at (created_at)
);

-- Create comments table
CREATE TABLE IF NOT EXISTS comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id),
    INDEX idx_user_id (user_id),
    INDEX idx_timestamp (timestamp)
);

-- Create linked_items table
CREATE TABLE IF NOT EXISTS linked_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    link VARCHAR(500) NOT NULL,
    title VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    INDEX idx_task_id (task_id)
);

-- Insert sample data
-- Sample users (password is 'password123' hashed with bcrypt)
INSERT IGNORE INTO users (username, email, password, name, role) VALUES
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LFvO.', 'System Administrator', 'Admin'),
('john_doe', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LFvO.', 'John Doe', 'User'),
('jane_smith', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LFvO.', 'Jane Smith', 'User'),
('mike_wilson', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LFvO.', 'Mike Wilson', 'User'),
('sarah_johnson', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LFvO.', 'Sarah Johnson', 'User');

-- Sample login logs
INSERT IGNORE INTO login_logs (user_id, timestamp, ip_address, user_agent, success) VALUES
(1, '2024-01-15 09:00:00', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', TRUE),
(2, '2024-01-15 09:15:00', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', TRUE),
(3, '2024-01-15 09:30:00', '*************', 'Mozilla/5.0 (X11; Linux x86_64)', TRUE),
(1, '2024-01-15 14:00:00', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', TRUE),
(4, '2024-01-15 10:00:00', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)', TRUE);

-- Sample tasks with new structure
INSERT IGNORE INTO tasks (title, description, assignee_id, status, due_date, created_by) VALUES
('Setup Development Environment', 'Configure development tools, IDE, and project structure for the new application', 2, 'Closed', '2024-01-20 17:00:00', 1),
('Design User Interface Mockups', 'Create wireframes and mockups for all major screens including user management and task views', 3, 'In Progress', '2024-01-25 12:00:00', 1),
('Implement User Authentication', 'Develop secure login system with JWT tokens and role-based access control', 2, 'In Progress', '2024-01-30 15:00:00', 1),
('Database Schema Design', 'Design and implement the complete database schema with all required tables and relationships', 4, 'ToDo', '2024-02-05 10:00:00', 1),
('API Development', 'Create RESTful APIs for all CRUD operations including users, tasks, comments, and linked items', 2, 'New', '2024-02-10 16:00:00', 1),
('Frontend Components Development', 'Build React/Angular components for task management, user management, and dashboard', 3, 'New', '2024-02-15 14:00:00', 1),
('Testing and Quality Assurance', 'Comprehensive testing of all features including unit tests, integration tests, and user acceptance testing', 5, 'New', '2024-02-20 18:00:00', 1),
('Documentation and Deployment', 'Create user documentation, API documentation, and deploy to production environment', 4, 'New', '2024-02-25 12:00:00', 1),
('Bug Fix: Login Issue', 'Fix the intermittent login timeout issue reported by users', 2, 'Blocked', '2024-01-22 09:00:00', 3),
('Feature Request: Dark Mode', 'Implement dark mode theme option for better user experience', 3, 'ToDo', '2024-02-01 16:00:00', 2);

-- Sample comments
INSERT IGNORE INTO comments (task_id, user_id, content) VALUES
(1, 2, 'Environment setup completed successfully. All tools are configured and working properly.'),
(1, 1, 'Great work! Please proceed with the next phase.'),
(2, 3, 'Initial mockups are ready for review. Shared the designs in the project folder.'),
(2, 1, 'Mockups look good. Please incorporate the feedback from the client meeting.'),
(3, 2, 'JWT implementation is complete. Working on role-based access control now.'),
(3, 1, 'Make sure to test with different user roles thoroughly.'),
(9, 2, 'Investigating the root cause. Seems to be related to session timeout configuration.'),
(9, 1, 'This is blocking several users. Please prioritize this fix.'),
(10, 3, 'Researching best practices for dark mode implementation.'),
(10, 2, 'Consider using CSS custom properties for theme switching.');

-- Sample linked items
INSERT IGNORE INTO linked_items (task_id, link, title) VALUES
(1, 'https://docs.company.com/dev-setup', 'Development Setup Guide'),
(1, 'https://github.com/company/project-template', 'Project Template Repository'),
(2, 'https://figma.com/project-mockups', 'UI Mockups - Figma'),
(2, 'https://docs.company.com/design-system', 'Company Design System'),
(3, 'https://jwt.io/introduction/', 'JWT Introduction Documentation'),
(3, 'https://docs.company.com/security-guidelines', 'Security Implementation Guidelines'),
(4, 'https://dbdiagram.io/project-schema', 'Database Schema Diagram'),
(9, 'https://github.com/company/project/issues/123', 'GitHub Issue #123'),
(9, 'https://logs.company.com/login-errors', 'Login Error Logs'),
(10, 'https://web.dev/prefers-color-scheme/', 'Dark Mode Best Practices');
