const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { demoUsers, demoTasks, demoComments, demoLinkedItems, demoLoginLogs } = require('../utils/demoData');

const router = express.Router();

// Demo login endpoint
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find demo user
    const user = demoUsers.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Verify password (all demo users have password 'password123')
    const isValidPassword = password === 'password123' || await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: user.id, username: user.username, email: user.email, role: user.role },
      process.env.JWT_SECRET || 'demo_secret',
      { expiresIn: '7d' }
    );

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Demo login error:', error);
    res.status(500).json({ message: 'Server error during login' });
  }
});

// Demo tasks endpoint
router.get('/tasks', (req, res) => {
  try {
    const { status, assigneeId, search, page = 1, limit = 10 } = req.query;
    
    let filteredTasks = [...demoTasks];

    // Apply filters
    if (status) {
      filteredTasks = filteredTasks.filter(task => task.status === status);
    }
    
    if (assigneeId) {
      filteredTasks = filteredTasks.filter(task => task.assignee_id === parseInt(assigneeId));
    }
    
    if (search) {
      filteredTasks = filteredTasks.filter(task => 
        task.title.toLowerCase().includes(search.toLowerCase()) ||
        task.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Add comments and linked items to each task
    const tasksWithDetails = filteredTasks.map(task => ({
      ...task,
      comments: demoComments.filter(comment => comment.task_id === task.id),
      linkedItems: demoLinkedItems.filter(item => item.task_id === task.id)
    }));

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedTasks = tasksWithDetails.slice(startIndex, endIndex);

    res.json({
      tasks: paginatedTasks,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: tasksWithDetails.length,
        hasMore: endIndex < tasksWithDetails.length
      }
    });
  } catch (error) {
    console.error('Demo tasks error:', error);
    res.status(500).json({ message: 'Server error fetching tasks' });
  }
});

// Demo task detail endpoint
router.get('/tasks/:id', (req, res) => {
  try {
    const taskId = parseInt(req.params.id);
    const task = demoTasks.find(t => t.id === taskId);
    
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const taskWithDetails = {
      ...task,
      comments: demoComments.filter(comment => comment.task_id === taskId),
      linkedItems: demoLinkedItems.filter(item => item.task_id === taskId)
    };

    res.json({ task: taskWithDetails });
  } catch (error) {
    console.error('Demo task detail error:', error);
    res.status(500).json({ message: 'Server error fetching task' });
  }
});

// Demo users endpoint
router.get('/users', (req, res) => {
  try {
    const { role, search, page = 1, limit = 10 } = req.query;
    
    let filteredUsers = demoUsers.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      created_at: user.created_at
    }));

    // Apply filters
    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }
    
    if (search) {
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.username.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    res.json({
      users: paginatedUsers,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: filteredUsers.length,
        hasMore: endIndex < filteredUsers.length
      }
    });
  } catch (error) {
    console.error('Demo users error:', error);
    res.status(500).json({ message: 'Server error fetching users' });
  }
});

// Demo user detail endpoint
router.get('/users/:id', (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const user = demoUsers.find(u => u.id === userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userDetail = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      created_at: user.created_at
    };

    res.json({ user: userDetail });
  } catch (error) {
    console.error('Demo user detail error:', error);
    res.status(500).json({ message: 'Server error fetching user' });
  }
});

// Demo login logs endpoint
router.get('/users/:id/login-logs', (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { limit = 50 } = req.query;
    
    const userLogs = demoLoginLogs
      .filter(log => log.user_id === userId)
      .slice(0, parseInt(limit))
      .map(log => ({
        id: log.id,
        timestamp: log.timestamp,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        success: log.success
      }));

    res.json({ logs: userLogs });
  } catch (error) {
    console.error('Demo login logs error:', error);
    res.status(500).json({ message: 'Server error fetching login logs' });
  }
});

// Demo task statistics endpoint
router.get('/tasks/stats', (req, res) => {
  try {
    const stats = {
      total: demoTasks.length,
      new_tasks: demoTasks.filter(t => t.status === 'New').length,
      todo: demoTasks.filter(t => t.status === 'ToDo').length,
      in_progress: demoTasks.filter(t => t.status === 'In Progress').length,
      blocked: demoTasks.filter(t => t.status === 'Blocked').length,
      closed: demoTasks.filter(t => t.status === 'Closed').length,
      overdue: demoTasks.filter(t => {
        const dueDate = new Date(t.due_date);
        const now = new Date();
        return dueDate < now && t.status !== 'Closed';
      }).length
    };

    res.json({ stats });
  } catch (error) {
    console.error('Demo stats error:', error);
    res.status(500).json({ message: 'Server error fetching statistics' });
  }
});

// Demo info endpoint
router.get('/info', (req, res) => {
  res.json({
    message: 'Task Management System - Demo Mode',
    description: 'This is a demonstration of the complete task management system with sample data.',
    features: [
      'User Management with Role-based Access Control',
      'Task Management with Status Workflow',
      'Comments and Linked Items',
      'Login Logs and Activity Tracking',
      'Advanced Filtering and Search',
      'Statistics and Reporting'
    ],
    demo_credentials: [
      {
        role: 'Admin',
        email: '<EMAIL>',
        password: 'password123',
        description: 'Full access to all features including user management'
      },
      {
        role: 'User',
        email: '<EMAIL>',
        password: 'password123',
        description: 'Standard user with task management capabilities'
      }
    ],
    note: 'All demo users use the password: password123'
  });
});

module.exports = router;
