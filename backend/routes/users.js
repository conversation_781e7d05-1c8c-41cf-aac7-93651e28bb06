const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const LoginLog = require('../models/LoginLog');
const { authenticateToken, requireAdmin, requireAdminOrSelf } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get all users (Admin only)
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { role, search, sortBy, sortOrder, page, limit } = req.query;

    const filters = {};
    if (role) filters.role = role;
    if (search) filters.search = search;
    if (sortBy) filters.sortBy = sortBy;
    if (sortOrder) filters.sortOrder = sortOrder;

    // Pagination
    const pageNum = parseInt(page) || 1;
    const limitNum = parseInt(limit) || 10;
    filters.limit = limitNum;
    filters.offset = (pageNum - 1) * limitNum;

    const users = await User.findAll(filters);

    res.json({
      users,
      pagination: {
        page: pageNum,
        limit: limitNum,
        hasMore: users.length === limitNum
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'Server error fetching users' });
  }
});

// Get specific user (Admin or self)
router.get('/:id', requireAdminOrSelf, async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ user });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Server error fetching user' });
  }
});

// Create new user (Admin only)
router.post('/', requireAdmin, [
  body('username').isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('email').isEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('name').notEmpty().withMessage('Full name is required'),
  body('role').isIn(['Admin', 'User']).withMessage('Invalid role')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, email, password, name, role } = req.body;

    // Check if user already exists
    const existingUserByEmail = await User.findByEmail(email);
    if (existingUserByEmail) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    const existingUserByUsername = await User.findByUsername(username);
    if (existingUserByUsername) {
      return res.status(400).json({ message: 'Username already taken' });
    }

    // Create user
    const userId = await User.create({ username, email, password, name, role });
    const user = await User.findById(userId);

    res.status(201).json({
      message: 'User created successfully',
      user
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ message: 'Server error creating user' });
  }
});

// Update user (Admin or self for basic info)
router.put('/:id', requireAdminOrSelf, [
  body('username').optional().isLength({ min: 3 }).withMessage('Username must be at least 3 characters'),
  body('email').isEmail().withMessage('Please provide a valid email'),
  body('name').notEmpty().withMessage('Full name is required'),
  body('role').optional().isIn(['Admin', 'User']).withMessage('Invalid role')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, email, name, role } = req.body;
    const targetUserId = parseInt(req.params.id);

    // Only admins can change roles
    if (role && req.user.role !== 'Admin') {
      return res.status(403).json({ message: 'Only admins can change user roles' });
    }

    // Check if email is already taken by another user
    if (email !== req.user.email) {
      const existingUser = await User.findByEmail(email);
      if (existingUser && existingUser.id !== targetUserId) {
        return res.status(400).json({ message: 'Email already taken by another user' });
      }
    }

    // Check if username is already taken by another user
    if (username) {
      const existingUser = await User.findByUsername(username);
      if (existingUser && existingUser.id !== targetUserId) {
        return res.status(400).json({ message: 'Username already taken by another user' });
      }
    }

    const currentUser = await User.findById(targetUserId);
    if (!currentUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    const updated = await User.update(targetUserId, {
      username: username || currentUser.username,
      email,
      name,
      role: role || currentUser.role
    });

    if (!updated) {
      return res.status(404).json({ message: 'Failed to update user' });
    }

    const user = await User.findById(targetUserId);

    res.json({
      message: 'User updated successfully',
      user
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'Server error updating user' });
  }
});

// Change password (Admin or self)
router.put('/:id/password', requireAdminOrSelf, [
  body('currentPassword').optional().notEmpty().withMessage('Current password is required'),
  body('newPassword').isLength({ min: 6 }).withMessage('New password must be at least 6 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { currentPassword, newPassword } = req.body;
    const targetUserId = parseInt(req.params.id);

    // Get user with password
    const user = await User.findByEmail(req.user.email);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // If not admin and changing own password, verify current password
    if (req.user.role !== 'Admin' && req.user.id === targetUserId) {
      if (!currentPassword) {
        return res.status(400).json({ message: 'Current password is required' });
      }

      const isValidPassword = await User.verifyPassword(currentPassword, user.password);
      if (!isValidPassword) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }
    }

    // Update password
    const updated = await User.updatePassword(targetUserId, newPassword);

    if (!updated) {
      return res.status(500).json({ message: 'Failed to update password' });
    }

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ message: 'Server error changing password' });
  }
});

// Get user login logs (Admin or self)
router.get('/:id/login-logs', requireAdminOrSelf, async (req, res) => {
  try {
    const { limit = 50 } = req.query;
    const logs = await User.getLoginLogs(req.params.id, parseInt(limit));

    res.json({
      logs: logs.map(log => ({
        id: log.id,
        timestamp: log.timestamp,
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        success: log.success
      }))
    });
  } catch (error) {
    console.error('Login logs fetch error:', error);
    res.status(500).json({ message: 'Server error fetching login logs' });
  }
});

// Delete user (Admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const targetUserId = parseInt(req.params.id);

    // Prevent admin from deleting themselves
    if (req.user.id === targetUserId) {
      return res.status(400).json({ message: 'Cannot delete your own account' });
    }

    const deleted = await User.delete(targetUserId);

    if (!deleted) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ message: 'Server error deleting user' });
  }
});

module.exports = router;
