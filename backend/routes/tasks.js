const express = require('express');
const { body, validationResult } = require('express-validator');
const Task = require('../models/Task');
const Comment = require('../models/Comment');
const LinkedItem = require('../models/LinkedItem');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get all tasks (users can see all tasks)
router.get('/', async (req, res) => {
  try {
    const { status, assigneeId, createdBy, search, sortBy, sortOrder, page, limit, dueDateFrom, dueDateTo } = req.query;

    const filters = {};
    if (status) filters.status = status;
    if (assigneeId) filters.assigneeId = assigneeId;
    if (createdBy) filters.createdBy = createdBy;
    if (search) filters.search = search;
    if (sortBy) filters.sortBy = sortBy;
    if (sortOrder) filters.sortOrder = sortOrder;
    if (dueDateFrom) filters.dueDateFrom = dueDateFrom;
    if (dueDateTo) filters.dueDateTo = dueDateTo;

    // Pagination
    const pageNum = parseInt(page) || 1;
    const limitNum = parseInt(limit) || 10;
    filters.limit = limitNum;
    filters.offset = (pageNum - 1) * limitNum;

    const tasks = await Task.findAll(filters);

    res.json({
      tasks,
      pagination: {
        page: pageNum,
        limit: limitNum,
        hasMore: tasks.length === limitNum
      }
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ message: 'Server error fetching tasks' });
  }
});

// Get task statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await Task.getStats();
    res.json({ stats });
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({ message: 'Server error fetching statistics' });
  }
});

// Get a specific task with comments and linked items
router.get('/:id', async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    // Get comments and linked items
    const comments = await Task.getComments(req.params.id);
    const linkedItems = await Task.getLinkedItems(req.params.id);

    res.json({
      task: {
        ...task,
        comments,
        linkedItems
      }
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ message: 'Server error fetching task' });
  }
});

// Create a new task
router.post('/', [
  body('title').notEmpty().withMessage('Title is required'),
  body('description').optional(),
  body('assigneeId').optional().isInt().withMessage('Invalid assignee ID'),
  body('status').optional().isIn(['New', 'ToDo', 'In Progress', 'Blocked', 'Closed']).withMessage('Invalid status'),
  body('dueDate').optional().isISO8601().withMessage('Invalid date format')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { title, description, assigneeId, status, dueDate } = req.body;

    const taskId = await Task.create({
      title,
      description,
      assigneeId,
      status,
      dueDate,
      createdBy: req.user.id
    });

    const task = await Task.findById(taskId);

    res.status(201).json({
      message: 'Task created successfully',
      task
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ message: 'Server error creating task' });
  }
});

// Update a task
router.put('/:id', [
  body('title').notEmpty().withMessage('Title is required'),
  body('description').optional(),
  body('assigneeId').optional().isInt().withMessage('Invalid assignee ID'),
  body('status').optional().isIn(['New', 'ToDo', 'In Progress', 'Blocked', 'Closed']).withMessage('Invalid status'),
  body('dueDate').optional().isISO8601().withMessage('Invalid date format')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { title, description, assigneeId, status, dueDate } = req.body;

    const updated = await Task.update(req.params.id, {
      title,
      description,
      assigneeId,
      status,
      dueDate
    });

    if (!updated) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const task = await Task.findById(req.params.id);

    res.json({
      message: 'Task updated successfully',
      task
    });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ message: 'Server error updating task' });
  }
});

// Update task status only
router.patch('/:id/status', [
  body('status').isIn(['New', 'ToDo', 'In Progress', 'Blocked', 'Closed']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { status } = req.body;

    const updated = await Task.updateStatus(req.params.id, status);

    if (!updated) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const task = await Task.findById(req.params.id);

    res.json({
      message: 'Task status updated successfully',
      task
    });
  } catch (error) {
    console.error('Update task status error:', error);
    res.status(500).json({ message: 'Server error updating task status' });
  }
});

// Delete a task (Admin only)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const deleted = await Task.delete(req.params.id);

    if (!deleted) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ message: 'Server error deleting task' });
  }
});

// Add comment to task
router.post('/:id/comments', [
  body('content').notEmpty().withMessage('Comment content is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { content } = req.body;
    const taskId = req.params.id;

    // Check if task exists
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const commentId = await Task.addComment(taskId, req.user.id, content);
    const comment = await Comment.findById(commentId);

    res.status(201).json({
      message: 'Comment added successfully',
      comment
    });
  } catch (error) {
    console.error('Add comment error:', error);
    res.status(500).json({ message: 'Server error adding comment' });
  }
});

// Add linked item to task
router.post('/:id/linked-items', [
  body('link').isURL().withMessage('Valid URL is required'),
  body('title').notEmpty().withMessage('Title is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { link, title } = req.body;
    const taskId = req.params.id;

    // Check if task exists
    const task = await Task.findById(taskId);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const itemId = await Task.addLinkedItem(taskId, link, title);
    const linkedItem = await LinkedItem.findById(itemId);

    res.status(201).json({
      message: 'Linked item added successfully',
      linkedItem
    });
  } catch (error) {
    console.error('Add linked item error:', error);
    res.status(500).json({ message: 'Server error adding linked item' });
  }
});

// Delete linked item
router.delete('/:id/linked-items/:itemId', async (req, res) => {
  try {
    const deleted = await Task.deleteLinkedItem(req.params.itemId);

    if (!deleted) {
      return res.status(404).json({ message: 'Linked item not found' });
    }

    res.json({ message: 'Linked item deleted successfully' });
  } catch (error) {
    console.error('Delete linked item error:', error);
    res.status(500).json({ message: 'Server error deleting linked item' });
  }
});

module.exports = router;
