const { pool } = require('../config/database');

class LinkedItem {
  static async create(itemData) {
    const { taskId, link, title } = itemData;
    
    const [result] = await pool.execute(
      'INSERT INTO linked_items (task_id, link, title) VALUES (?, ?, ?)',
      [taskId, link, title]
    );
    
    return result.insertId;
  }

  static async findById(id) {
    const [items] = await pool.execute(
      'SELECT * FROM linked_items WHERE id = ?',
      [id]
    );
    return items[0];
  }

  static async findByTaskId(taskId) {
    const [items] = await pool.execute(
      'SELECT * FROM linked_items WHERE task_id = ? ORDER BY created_at ASC',
      [taskId]
    );
    return items;
  }

  static async update(id, itemData) {
    const { link, title } = itemData;
    
    const [result] = await pool.execute(
      'UPDATE linked_items SET link = ?, title = ? WHERE id = ?',
      [link, title, id]
    );
    
    return result.affectedRows > 0;
  }

  static async delete(id) {
    const [result] = await pool.execute(
      'DELETE FROM linked_items WHERE id = ?',
      [id]
    );
    
    return result.affectedRows > 0;
  }

  static async findAll(filters = {}) {
    let query = `
      SELECT li.*, t.title as task_title
      FROM linked_items li
      JOIN tasks t ON li.task_id = t.id
    `;
    let params = [];
    let conditions = [];

    if (filters.taskId) {
      conditions.push('li.task_id = ?');
      params.push(filters.taskId);
    }

    if (filters.search) {
      conditions.push('(li.title LIKE ? OR li.link LIKE ?)');
      params.push(`%${filters.search}%`, `%${filters.search}%`);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY li.created_at DESC';

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));
      
      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(parseInt(filters.offset));
      }
    }

    const [items] = await pool.execute(query, params);
    return items;
  }

  static async getStats() {
    const [stats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_links,
        COUNT(DISTINCT task_id) as tasks_with_links,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h
      FROM linked_items
    `);
    
    return stats[0];
  }
}

module.exports = LinkedItem;
