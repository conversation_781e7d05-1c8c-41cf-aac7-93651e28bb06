const { pool } = require('../config/database');

class Comment {
  static async create(commentData) {
    const { taskId, userId, content } = commentData;
    
    const [result] = await pool.execute(
      'INSERT INTO comments (task_id, user_id, content) VALUES (?, ?, ?)',
      [taskId, userId, content]
    );
    
    return result.insertId;
  }

  static async findById(id) {
    const [comments] = await pool.execute(`
      SELECT c.*, u.name as author_name, u.email as author_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.id = ?
    `, [id]);
    return comments[0];
  }

  static async findByTaskId(taskId) {
    const [comments] = await pool.execute(`
      SELECT c.*, u.name as author_name, u.email as author_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.task_id = ?
      ORDER BY c.timestamp ASC
    `, [taskId]);
    return comments;
  }

  static async update(id, content) {
    const [result] = await pool.execute(
      'UPDATE comments SET content = ? WHERE id = ?',
      [content, id]
    );
    
    return result.affectedRows > 0;
  }

  static async delete(id) {
    const [result] = await pool.execute(
      'DELETE FROM comments WHERE id = ?',
      [id]
    );
    
    return result.affectedRows > 0;
  }

  static async findByUserId(userId, limit = 50) {
    const [comments] = await pool.execute(`
      SELECT c.*, t.title as task_title
      FROM comments c
      JOIN tasks t ON c.task_id = t.id
      WHERE c.user_id = ?
      ORDER BY c.timestamp DESC
      LIMIT ?
    `, [userId, limit]);
    return comments;
  }

  static async getStats() {
    const [stats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_comments,
        COUNT(DISTINCT user_id) as unique_commenters,
        COUNT(DISTINCT task_id) as tasks_with_comments,
        COUNT(CASE WHEN timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h
      FROM comments
    `);
    
    return stats[0];
  }
}

module.exports = Comment;
