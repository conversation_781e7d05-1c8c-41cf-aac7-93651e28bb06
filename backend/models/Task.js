const { pool } = require('../config/database');

class Task {
  static async create(taskData) {
    const { title, description, assigneeId, status, dueDate, createdBy } = taskData;

    // Format date for MySQL
    let formattedDueDate = null;
    if (dueDate) {
      const date = new Date(dueDate);
      formattedDueDate = date.toISOString().slice(0, 19).replace('T', ' ');
    }

    const [result] = await pool.execute(
      'INSERT INTO tasks (title, description, assignee_id, status, due_date, created_by) VALUES (?, ?, ?, ?, ?, ?)',
      [title, description, assigneeId, status || 'New', formattedDueDate, createdBy]
    );

    return result.insertId;
  }

  static async findById(id) {
    const [tasks] = await pool.execute(`
      SELECT t.*,
             u1.name as assignee_name, u1.email as assignee_email,
             u2.name as creator_name, u2.email as creator_email
      FROM tasks t
      LEFT JOIN users u1 ON t.assignee_id = u1.id
      LEFT JOIN users u2 ON t.created_by = u2.id
      WHERE t.id = ?
    `, [id]);
    return tasks[0];
  }

  static async findAll(filters = {}) {
    let query = `
      SELECT t.*,
             u1.name as assignee_name, u1.email as assignee_email,
             u2.name as creator_name, u2.email as creator_email
      FROM tasks t
      LEFT JOIN users u1 ON t.assignee_id = u1.id
      LEFT JOIN users u2 ON t.created_by = u2.id
    `;
    let params = [];
    let conditions = [];

    // Add filters
    if (filters.status) {
      conditions.push('t.status = ?');
      params.push(filters.status);
    }

    if (filters.assigneeId) {
      conditions.push('t.assignee_id = ?');
      params.push(filters.assigneeId);
    }

    if (filters.createdBy) {
      conditions.push('t.created_by = ?');
      params.push(filters.createdBy);
    }

    if (filters.search) {
      conditions.push('(t.title LIKE ? OR t.description LIKE ?)');
      params.push(`%${filters.search}%`, `%${filters.search}%`);
    }

    if (filters.dueDateFrom) {
      conditions.push('t.due_date >= ?');
      params.push(filters.dueDateFrom);
    }

    if (filters.dueDateTo) {
      conditions.push('t.due_date <= ?');
      params.push(filters.dueDateTo);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // Add sorting
    const sortBy = filters.sortBy || 't.created_at';
    const sortOrder = filters.sortOrder || 'DESC';
    query += ` ORDER BY ${sortBy} ${sortOrder}`;

    // Add pagination
    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(parseInt(filters.offset));
      }
    }

    const [tasks] = await pool.execute(query, params);
    return tasks;
  }

  static async update(id, taskData) {
    const { title, description, assigneeId, status, dueDate } = taskData;

    // Format date for MySQL
    let formattedDueDate = null;
    if (dueDate) {
      const date = new Date(dueDate);
      formattedDueDate = date.toISOString().slice(0, 19).replace('T', ' ');
    }

    const [result] = await pool.execute(
      'UPDATE tasks SET title = ?, description = ?, assignee_id = ?, status = ?, due_date = ? WHERE id = ?',
      [title, description, assigneeId, status, formattedDueDate, id]
    );

    return result.affectedRows > 0;
  }

  static async updateStatus(id, status) {
    const [result] = await pool.execute(
      'UPDATE tasks SET status = ? WHERE id = ?',
      [status, id]
    );

    return result.affectedRows > 0;
  }

  static async delete(id) {
    const [result] = await pool.execute(
      'DELETE FROM tasks WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  }

  static async getStats() {
    const [stats] = await pool.execute(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'New' THEN 1 ELSE 0 END) as new_tasks,
        SUM(CASE WHEN status = 'ToDo' THEN 1 ELSE 0 END) as todo,
        SUM(CASE WHEN status = 'In Progress' THEN 1 ELSE 0 END) as in_progress,
        SUM(CASE WHEN status = 'Blocked' THEN 1 ELSE 0 END) as blocked,
        SUM(CASE WHEN status = 'Closed' THEN 1 ELSE 0 END) as closed,
        SUM(CASE WHEN due_date < NOW() AND status NOT IN ('Closed') THEN 1 ELSE 0 END) as overdue
      FROM tasks
    `);

    return stats[0];
  }

  static async getComments(taskId) {
    const [comments] = await pool.execute(`
      SELECT c.*, u.name as author_name, u.email as author_email
      FROM comments c
      JOIN users u ON c.user_id = u.id
      WHERE c.task_id = ?
      ORDER BY c.timestamp ASC
    `, [taskId]);

    return comments;
  }

  static async addComment(taskId, userId, content) {
    const [result] = await pool.execute(
      'INSERT INTO comments (task_id, user_id, content) VALUES (?, ?, ?)',
      [taskId, userId, content]
    );

    return result.insertId;
  }

  static async getLinkedItems(taskId) {
    const [items] = await pool.execute(
      'SELECT * FROM linked_items WHERE task_id = ? ORDER BY created_at ASC',
      [taskId]
    );

    return items;
  }

  static async addLinkedItem(taskId, link, title) {
    const [result] = await pool.execute(
      'INSERT INTO linked_items (task_id, link, title) VALUES (?, ?, ?)',
      [taskId, link, title]
    );

    return result.insertId;
  }

  static async deleteLinkedItem(id) {
    const [result] = await pool.execute(
      'DELETE FROM linked_items WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  }
}

module.exports = Task;
