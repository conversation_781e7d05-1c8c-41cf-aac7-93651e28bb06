const { pool } = require('../config/database');

class LoginLog {
  static async create(logData) {
    const { userId, ipAddress, userAgent, success = true } = logData;
    
    const [result] = await pool.execute(
      'INSERT INTO login_logs (user_id, ip_address, user_agent, success) VALUES (?, ?, ?, ?)',
      [userId, ipAddress, userAgent, success]
    );
    
    return result.insertId;
  }

  static async findByUserId(userId, limit = 50) {
    const [logs] = await pool.execute(
      'SELECT * FROM login_logs WHERE user_id = ? ORDER BY timestamp DESC LIMIT ?',
      [userId, limit]
    );
    return logs;
  }

  static async findAll(filters = {}) {
    let query = `
      SELECT ll.*, u.name as user_name, u.email as user_email
      FROM login_logs ll
      JOIN users u ON ll.user_id = u.id
    `;
    let params = [];
    let conditions = [];

    if (filters.userId) {
      conditions.push('ll.user_id = ?');
      params.push(filters.userId);
    }

    if (filters.success !== undefined) {
      conditions.push('ll.success = ?');
      params.push(filters.success);
    }

    if (filters.dateFrom) {
      conditions.push('ll.timestamp >= ?');
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      conditions.push('ll.timestamp <= ?');
      params.push(filters.dateTo);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY ll.timestamp DESC';

    if (filters.limit) {
      query += ' LIMIT ?';
      params.push(parseInt(filters.limit));
      
      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(parseInt(filters.offset));
      }
    }

    const [logs] = await pool.execute(query, params);
    return logs;
  }

  static async getStats() {
    const [stats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_logins,
        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_logins,
        SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_logins,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(CASE WHEN timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h
      FROM login_logs
    `);
    
    return stats[0];
  }
}

module.exports = LoginLog;
