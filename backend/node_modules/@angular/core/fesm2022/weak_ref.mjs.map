{"version": 3, "file": "weak_ref.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/weak_ref.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function setAlternateWeakRefImpl(impl: unknown) {\n  // TODO: remove this function\n}\n"], "names": [], "mappings": ";;;;;;AAQM,SAAU,uBAAuB,CAAC,IAAa,EAAA;;AAErD;;;;"}