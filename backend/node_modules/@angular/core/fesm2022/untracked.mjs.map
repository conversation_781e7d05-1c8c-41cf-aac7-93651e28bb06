{"version": 3, "file": "untracked.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/linked_signal.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/untracked.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {COMPUTING, ERRORED, UNSET} from './computed';\nimport {defaultEquals, ValueEqualityFn} from './equality';\nimport {\n  consumerAfterComputation,\n  consumerBeforeComputation,\n  producerAccessed,\n  producerMarkClean,\n  producerUpdateValueVersion,\n  REACTIVE_NODE,\n  ReactiveNode,\n  runPostProducerCreatedFn,\n  SIGNAL,\n} from './graph';\nimport {signalSetFn, signalUpdateFn} from './signal';\n\n// Required as the signals library is in a separate package, so we need to explicitly ensure the\n// global `ngDevMode` type is defined.\ndeclare const ngDevMode: boolean | undefined;\n\nexport type ComputationFn<S, D> = (source: S, previous?: {source: S; value: D}) => D;\n\nexport interface LinkedSignalNode<S, D> extends ReactiveNode {\n  /**\n   * Value of the source signal that was used to derive the computed value.\n   */\n  sourceValue: S;\n\n  /**\n   * Current state value, or one of the sentinel values (`UNSET`, `COMPUTING`,\n   * `ERROR`).\n   */\n  value: D;\n\n  /**\n   * If `value` is `ERRORED`, the error caught from the last computation attempt which will\n   * be re-thrown.\n   */\n  error: unknown;\n\n  /**\n   * The source function represents reactive dependency based on which the linked state is reset.\n   */\n  source: () => S;\n\n  /**\n   * The computation function which will produce a new value based on the source and, optionally - previous values.\n   */\n  computation: ComputationFn<S, D>;\n\n  equal: ValueEqualityFn<D>;\n}\n\nexport type LinkedSignalGetter<S, D> = (() => D) & {\n  [SIGNAL]: LinkedSignalNode<S, D>;\n};\n\nexport function createLinkedSignal<S, D>(\n  sourceFn: () => S,\n  computationFn: ComputationFn<S, D>,\n  equalityFn?: ValueEqualityFn<D>,\n): LinkedSignalGetter<S, D> {\n  const node: LinkedSignalNode<S, D> = Object.create(LINKED_SIGNAL_NODE);\n\n  node.source = sourceFn;\n  node.computation = computationFn;\n  if (equalityFn != undefined) {\n    node.equal = equalityFn;\n  }\n\n  const linkedSignalGetter = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n\n    return node.value;\n  };\n\n  const getter = linkedSignalGetter as LinkedSignalGetter<S, D>;\n  getter[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    getter.toString = () => `[LinkedSignal${debugName}: ${node.value}]`;\n  }\n\n  runPostProducerCreatedFn(node);\n\n  return getter;\n}\n\nexport function linkedSignalSetFn<S, D>(node: LinkedSignalNode<S, D>, newValue: D) {\n  producerUpdateValueVersion(node);\n  signalSetFn(node, newValue);\n  producerMarkClean(node);\n}\n\nexport function linkedSignalUpdateFn<S, D>(\n  node: LinkedSignalNode<S, D>,\n  updater: (value: D) => D,\n): void {\n  producerUpdateValueVersion(node);\n  signalUpdateFn(node, updater);\n  producerMarkClean(node);\n}\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nexport const LINKED_SIGNAL_NODE: object = /* @__PURE__ */ (() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    kind: 'linkedSignal',\n\n    producerMustRecompute(node: LinkedSignalNode<unknown, unknown>): boolean {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n\n    producerRecomputeValue(node: LinkedSignalNode<unknown, unknown>): void {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error(\n          typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '',\n        );\n      }\n\n      const oldValue = node.value;\n      node.value = COMPUTING;\n\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue: unknown;\n      try {\n        const newSourceValue = node.source();\n        const prev =\n          oldValue === UNSET || oldValue === ERRORED\n            ? undefined\n            : {\n                source: node.sourceValue,\n                value: oldValue,\n              };\n        newValue = node.computation(newSourceValue, prev);\n        node.sourceValue = newSourceValue;\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n\n      if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n\n      node.value = newValue;\n      node.version++;\n    },\n  };\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {setActiveConsumer} from './graph';\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nexport function untracked<T>(nonReactiveReadsFn: () => T): T {\n  const prevConsumer = setActiveConsumer(null);\n  // We are not trying to catch any particular errors here, just making sure that the consumers\n  // stack is restored in case of errors.\n  try {\n    return nonReactiveReadsFn();\n  } finally {\n    setActiveConsumer(prevConsumer);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;SAgEgB,kBAAkB,CAChC,QAAiB,EACjB,aAAkC,EAClC,UAA+B,EAAA;IAE/B,MAAM,IAAI,GAA2B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAEtE,IAAA,IAAI,CAAC,MAAM,GAAG,QAAQ;AACtB,IAAA,IAAI,CAAC,WAAW,GAAG,aAAa;AAChC,IAAA,IAAI,UAAU,IAAI,SAAS,EAAE;AAC3B,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU;;IAGzB,MAAM,kBAAkB,GAAG,MAAK;;QAE9B,0BAA0B,CAAC,IAAI,CAAC;;QAGhC,gBAAgB,CAAC,IAAI,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,CAAC,KAAK;;QAGlB,OAAO,IAAI,CAAC,KAAK;AACnB,KAAC;IAED,MAAM,MAAM,GAAG,kBAA8C;AAC7D,IAAA,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI;AACrB,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE;AACnE,QAAA,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAgB,aAAA,EAAA,SAAS,CAAK,EAAA,EAAA,IAAI,CAAC,KAAK,GAAG;;IAGrE,wBAAwB,CAAC,IAAI,CAAC;AAE9B,IAAA,OAAO,MAAM;AACf;AAEgB,SAAA,iBAAiB,CAAO,IAA4B,EAAE,QAAW,EAAA;IAC/E,0BAA0B,CAAC,IAAI,CAAC;AAChC,IAAA,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;IAC3B,iBAAiB,CAAC,IAAI,CAAC;AACzB;AAEgB,SAAA,oBAAoB,CAClC,IAA4B,EAC5B,OAAwB,EAAA;IAExB,0BAA0B,CAAC,IAAI,CAAC;AAChC,IAAA,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC;IAC7B,iBAAiB,CAAC,IAAI,CAAC;AACzB;AAEA;AACA;AACA;AACO,MAAM,kBAAkB,mBAA2B,CAAC,MAAK;IAC9D,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,KAAK,EAAE,aAAa;AACpB,QAAA,IAAI,EAAE,cAAc;AAEpB,QAAA,qBAAqB,CAAC,IAAwC,EAAA;;;YAG5D,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS;SACxD;AAED,QAAA,sBAAsB,CAAC,IAAwC,EAAA;AAC7D,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;;AAE5B,gBAAA,MAAM,IAAI,KAAK,CACb,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,iCAAiC,GAAG,EAAE,CACvF;;AAGH,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK;AAC3B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;AAEtB,YAAA,MAAM,YAAY,GAAG,yBAAyB,CAAC,IAAI,CAAC;AACpD,YAAA,IAAI,QAAiB;AACrB,YAAA,IAAI;AACF,gBAAA,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,EAAE;gBACpC,MAAM,IAAI,GACR,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK;AACjC,sBAAE;AACF,sBAAE;wBACE,MAAM,EAAE,IAAI,CAAC,WAAW;AACxB,wBAAA,KAAK,EAAE,QAAQ;qBAChB;gBACP,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC;AACjD,gBAAA,IAAI,CAAC,WAAW,GAAG,cAAc;;YACjC,OAAO,GAAG,EAAE;gBACZ,QAAQ,GAAG,OAAO;AAClB,gBAAA,IAAI,CAAC,KAAK,GAAG,GAAG;;oBACR;AACR,gBAAA,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC;;AAG9C,YAAA,IAAI,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;;;AAGhF,gBAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;gBACrB;;AAGF,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;YACrB,IAAI,CAAC,OAAO,EAAE;SACf;KACF;AACH,CAAC,GAAG;;ACxKJ;;;AAGG;AACG,SAAU,SAAS,CAAI,kBAA2B,EAAA;AACtD,IAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;;;AAG5C,IAAA,IAAI;QACF,OAAO,kBAAkB,EAAE;;YACnB;QACR,iBAAiB,CAAC,YAAY,CAAC;;AAEnC;;;;"}