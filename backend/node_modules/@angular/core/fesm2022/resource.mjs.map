{"version": 3, "file": "resource.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/src/authoring/output/output_emitter_ref.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/untracked.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/computed.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/effect.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/linked_signal.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/src/resource/resource.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {setActiveConsumer} from '../../../primitives/signals';\n\nimport {inject} from '../../di/injector_compatibility';\nimport {<PERSON>rrorHandler} from '../../error_handler';\nimport {formatRuntimeError, RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {DestroyRef} from '../../linker/destroy_ref';\n\nimport {OutputRef, OutputRefSubscription} from './output_ref';\n\n/**\n * An `OutputEmitterRef` is created by the `output()` function and can be\n * used to emit values to consumers of your directive or component.\n *\n * Consumers of your directive/component can bind to the output and\n * subscribe to changes via the bound event syntax. For example:\n *\n * ```html\n * <my-comp (valueChange)=\"processNewValue($event)\" />\n * ```\n *\n * @publicAPI\n */\nexport class OutputEmitterRef<T> implements OutputRef<T> {\n  private destroyed = false;\n  private listeners: Array<(value: T) => void> | null = null;\n  private errorHandler = inject(ErrorHandler, {optional: true});\n\n  /** @internal */\n  destroyRef: DestroyRef = inject(DestroyRef);\n\n  constructor() {\n    // Clean-up all listeners and mark as destroyed upon destroy.\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n      this.listeners = null;\n    });\n  }\n\n  subscribe(callback: (value: T) => void): OutputRefSubscription {\n    if (this.destroyed) {\n      throw new RuntimeError(\n        RuntimeErrorCode.OUTPUT_REF_DESTROYED,\n        ngDevMode &&\n          'Unexpected subscription to destroyed `OutputRef`. ' +\n            'The owning directive/component is destroyed.',\n      );\n    }\n\n    (this.listeners ??= []).push(callback);\n\n    return {\n      unsubscribe: () => {\n        const idx = this.listeners?.indexOf(callback);\n        if (idx !== undefined && idx !== -1) {\n          this.listeners?.splice(idx, 1);\n        }\n      },\n    };\n  }\n\n  /** Emits a new value to the output. */\n  emit(value: T): void {\n    if (this.destroyed) {\n      console.warn(\n        formatRuntimeError(\n          RuntimeErrorCode.OUTPUT_REF_DESTROYED,\n          ngDevMode &&\n            'Unexpected emit for destroyed `OutputRef`. ' +\n              'The owning directive/component is destroyed.',\n        ),\n      );\n      return;\n    }\n\n    if (this.listeners === null) {\n      return;\n    }\n\n    const previousConsumer = setActiveConsumer(null);\n    try {\n      for (const listenerFn of this.listeners) {\n        try {\n          listenerFn(value);\n        } catch (err: unknown) {\n          this.errorHandler?.handleError(err);\n        }\n      }\n    } finally {\n      setActiveConsumer(previousConsumer);\n    }\n  }\n}\n\n/** Gets the owning `DestroyRef` for the given output. */\nexport function getOutputDestroyRef(ref: OutputRef<unknown>): DestroyRef | undefined {\n  return ref.destroyRef;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {untracked as untrackedPrimitive} from '../../../primitives/signals';\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nexport function untracked<T>(nonReactiveReadsFn: () => T): T {\n  return untrackedPrimitive(nonReactiveReadsFn);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {createComputed, SIGNAL} from '../../../primitives/signals';\n\nimport {Signal, ValueEqualityFn} from './api';\n\n/**\n * Options passed to the `computed` creation function.\n */\nexport interface CreateComputedOptions<T> {\n  /**\n   * A comparison function which defines equality for computed values.\n   */\n  equal?: ValueEqualityFn<T>;\n\n  /**\n   * A debug name for the computed signal. Used in Angular DevTools to identify the signal.\n   */\n  debugName?: string;\n}\n\n/**\n * Create a computed `Signal` which derives a reactive value from an expression.\n */\nexport function computed<T>(computation: () => T, options?: CreateComputedOptions<T>): Signal<T> {\n  const getter = createComputed(computation, options?.equal);\n\n  if (ngDevMode) {\n    getter.toString = () => `[Computed: ${getter()}]`;\n    getter[SIGNAL].debugName = options?.debugName;\n  }\n\n  return getter;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  REACTIVE_NODE,\n  ReactiveNode,\n  SIGNAL,\n  consumerAfterComputation,\n  consumerBeforeComputation,\n  consumerDestroy,\n  consumerPollProducersForChange,\n  isInNotificationPhase,\n  setActiveConsumer,\n} from '../../../primitives/signals';\nimport {FLAGS, LViewFlags, LView, EFFECTS} from '../interfaces/view';\nimport {markAncestorsForTraversal} from '../util/view_utils';\nimport {inject} from '../../di/injector_compatibility';\nimport {Injector} from '../../di/injector';\nimport {assertNotInReactiveContext} from './asserts';\nimport {assertInInjectionContext} from '../../di/contextual';\nimport {DestroyRef, NodeInjectorDestroyRef} from '../../linker/destroy_ref';\nimport {ViewContext} from '../view_context';\nimport {noop} from '../../util/noop';\nimport {\n  ChangeDetectionScheduler,\n  NotificationSource,\n} from '../../change_detection/scheduling/zoneless_scheduling';\nimport {setIsRefreshingViews} from '../state';\nimport {EffectScheduler, SchedulableEffect} from './root_effect_scheduler';\n\nimport {emitEffectCreatedEvent, setInjectorProfilerContext} from '../debug/injector_profiler';\n\n/**\n * A global reactive effect, which can be manually destroyed.\n *\n * @publicApi 20.0\n */\nexport interface EffectRef {\n  /**\n   * Shut down the effect, removing it from any upcoming scheduled executions.\n   */\n  destroy(): void;\n}\n\nexport class EffectRefImpl implements EffectRef {\n  [SIGNAL]: EffectNode;\n\n  constructor(node: EffectNode) {\n    this[SIGNAL] = node;\n  }\n\n  destroy(): void {\n    this[SIGNAL].destroy();\n  }\n}\n\n/**\n * Options passed to the `effect` function.\n *\n * @publicApi 20.0\n */\nexport interface CreateEffectOptions {\n  /**\n   * The `Injector` in which to create the effect.\n   *\n   * If this is not provided, the current [injection context](guide/di/dependency-injection-context)\n   * will be used instead (via `inject`).\n   */\n  injector?: Injector;\n\n  /**\n   * Whether the `effect` should require manual cleanup.\n   *\n   * If this is `false` (the default) the effect will automatically register itself to be cleaned up\n   * with the current `DestroyRef`.\n   *\n   * If this is `true` and you want to use the effect outside an injection context, you still\n   * need to provide an `Injector` to the effect.\n   */\n  manualCleanup?: boolean;\n\n  /**\n   * @deprecated no longer required, signal writes are allowed by default.\n   */\n  allowSignalWrites?: boolean;\n\n  /**\n   * A debug name for the effect. Used in Angular DevTools to identify the effect.\n   */\n  debugName?: string;\n}\n\n/**\n * An effect can, optionally, register a cleanup function. If registered, the cleanup is executed\n * before the next effect run. The cleanup function makes it possible to \"cancel\" any work that the\n * previous effect run might have started.\n *\n * @publicApi 20.0\n */\nexport type EffectCleanupFn = () => void;\n\n/**\n * A callback passed to the effect function that makes it possible to register cleanup logic.\n *\n * @publicApi 20.0\n */\nexport type EffectCleanupRegisterFn = (cleanupFn: EffectCleanupFn) => void;\n\n/**\n * Registers an \"effect\" that will be scheduled & executed whenever the signals that it reads\n * changes.\n *\n * Angular has two different kinds of effect: component effects and root effects. Component effects\n * are created when `effect()` is called from a component, directive, or within a service of a\n * component/directive. Root effects are created when `effect()` is called from outside the\n * component tree, such as in a root service.\n *\n * The two effect types differ in their timing. Component effects run as a component lifecycle\n * event during Angular's synchronization (change detection) process, and can safely read input\n * signals or create/destroy views that depend on component state. Root effects run as microtasks\n * and have no connection to the component tree or change detection.\n *\n * `effect()` must be run in injection context, unless the `injector` option is manually specified.\n *\n * @publicApi 20.0\n */\nexport function effect(\n  effectFn: (onCleanup: EffectCleanupRegisterFn) => void,\n  options?: CreateEffectOptions,\n): EffectRef {\n  ngDevMode &&\n    assertNotInReactiveContext(\n      effect,\n      'Call `effect` outside of a reactive context. For example, schedule the ' +\n        'effect inside the component constructor.',\n    );\n\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(effect);\n  }\n\n  if (ngDevMode && options?.allowSignalWrites !== undefined) {\n    console.warn(\n      `The 'allowSignalWrites' flag is deprecated and no longer impacts effect() (writes are always allowed)`,\n    );\n  }\n\n  const injector = options?.injector ?? inject(Injector);\n  let destroyRef = options?.manualCleanup !== true ? injector.get(DestroyRef) : null;\n\n  let node: EffectNode;\n\n  const viewContext = injector.get(ViewContext, null, {optional: true});\n  const notifier = injector.get(ChangeDetectionScheduler);\n  if (viewContext !== null) {\n    // This effect was created in the context of a view, and will be associated with the view.\n    node = createViewEffect(viewContext.view, notifier, effectFn);\n    if (destroyRef instanceof NodeInjectorDestroyRef && destroyRef._lView === viewContext.view) {\n      // The effect is being created in the same view as the `DestroyRef` references, so it will be\n      // automatically destroyed without the need for an explicit `DestroyRef` registration.\n      destroyRef = null;\n    }\n  } else {\n    // This effect was created outside the context of a view, and will be scheduled independently.\n    node = createRootEffect(effectFn, injector.get(EffectScheduler), notifier);\n  }\n  node.injector = injector;\n\n  if (destroyRef !== null) {\n    // If we need to register for cleanup, do that here.\n    node.onDestroyFn = destroyRef.onDestroy(() => node.destroy());\n  }\n\n  const effectRef = new EffectRefImpl(node);\n\n  if (ngDevMode) {\n    node.debugName = options?.debugName ?? '';\n    const prevInjectorProfilerContext = setInjectorProfilerContext({injector, token: null});\n    try {\n      emitEffectCreatedEvent(effectRef);\n    } finally {\n      setInjectorProfilerContext(prevInjectorProfilerContext);\n    }\n  }\n\n  return effectRef;\n}\n\nexport interface EffectNode extends ReactiveNode, SchedulableEffect {\n  hasRun: boolean;\n  cleanupFns: EffectCleanupFn[] | undefined;\n  injector: Injector;\n  notifier: ChangeDetectionScheduler;\n\n  onDestroyFn: () => void;\n  fn: (cleanupFn: EffectCleanupRegisterFn) => void;\n  run(): void;\n  destroy(): void;\n  maybeCleanup(): void;\n}\n\nexport interface ViewEffectNode extends EffectNode {\n  view: LView;\n}\n\nexport interface RootEffectNode extends EffectNode {\n  scheduler: EffectScheduler;\n}\n\nexport const BASE_EFFECT_NODE: Omit<EffectNode, 'fn' | 'destroy' | 'injector' | 'notifier'> =\n  /* @__PURE__ */ (() => ({\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: true,\n    dirty: true,\n    hasRun: false,\n    cleanupFns: undefined,\n    zone: null,\n    kind: 'effect',\n    onDestroyFn: noop,\n    run(this: EffectNode): void {\n      this.dirty = false;\n\n      if (ngDevMode && isInNotificationPhase()) {\n        throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n      }\n\n      if (this.hasRun && !consumerPollProducersForChange(this)) {\n        return;\n      }\n      this.hasRun = true;\n\n      const registerCleanupFn: EffectCleanupRegisterFn = (cleanupFn) =>\n        (this.cleanupFns ??= []).push(cleanupFn);\n\n      const prevNode = consumerBeforeComputation(this);\n\n      // We clear `setIsRefreshingViews` so that `markForCheck()` within the body of an effect will\n      // cause CD to reach the component in question.\n      const prevRefreshingViews = setIsRefreshingViews(false);\n      try {\n        this.maybeCleanup();\n        this.fn(registerCleanupFn);\n      } finally {\n        setIsRefreshingViews(prevRefreshingViews);\n        consumerAfterComputation(this, prevNode);\n      }\n    },\n\n    maybeCleanup(this: EffectNode): void {\n      if (!this.cleanupFns?.length) {\n        return;\n      }\n      const prevConsumer = setActiveConsumer(null);\n      try {\n        // Attempt to run the cleanup functions. Regardless of failure or success, we consider\n        // cleanup \"completed\" and clear the list for the next run of the effect. Note that an error\n        // from the cleanup function will still crash the current run of the effect.\n        while (this.cleanupFns.length) {\n          this.cleanupFns.pop()!();\n        }\n      } finally {\n        this.cleanupFns = [];\n        setActiveConsumer(prevConsumer);\n      }\n    },\n  }))();\n\nexport const ROOT_EFFECT_NODE: Omit<RootEffectNode, 'fn' | 'scheduler' | 'notifier' | 'injector'> =\n  /* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty(this: RootEffectNode) {\n      this.scheduler.schedule(this);\n      this.notifier.notify(NotificationSource.RootEffect);\n    },\n    destroy(this: RootEffectNode) {\n      consumerDestroy(this);\n      this.onDestroyFn();\n      this.maybeCleanup();\n      this.scheduler.remove(this);\n    },\n  }))();\n\nexport const VIEW_EFFECT_NODE: Omit<ViewEffectNode, 'fn' | 'view' | 'injector' | 'notifier'> =\n  /* @__PURE__ */ (() => ({\n    ...BASE_EFFECT_NODE,\n    consumerMarkedDirty(this: ViewEffectNode): void {\n      this.view[FLAGS] |= LViewFlags.HasChildViewsToRefresh;\n      markAncestorsForTraversal(this.view);\n      this.notifier.notify(NotificationSource.ViewEffect);\n    },\n    destroy(this: ViewEffectNode): void {\n      consumerDestroy(this);\n      this.onDestroyFn();\n      this.maybeCleanup();\n      this.view[EFFECTS]?.delete(this);\n    },\n  }))();\n\nexport function createViewEffect(\n  view: LView,\n  notifier: ChangeDetectionScheduler,\n  fn: (onCleanup: EffectCleanupRegisterFn) => void,\n): ViewEffectNode {\n  const node = Object.create(VIEW_EFFECT_NODE) as ViewEffectNode;\n  node.view = view;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.notifier = notifier;\n  node.fn = fn;\n\n  view[EFFECTS] ??= new Set();\n  view[EFFECTS].add(node);\n\n  node.consumerMarkedDirty(node);\n  return node;\n}\n\nexport function createRootEffect(\n  fn: (onCleanup: EffectCleanupRegisterFn) => void,\n  scheduler: EffectScheduler,\n  notifier: ChangeDetectionScheduler,\n): RootEffectNode {\n  const node = Object.create(ROOT_EFFECT_NODE) as RootEffectNode;\n  node.fn = fn;\n  node.scheduler = scheduler;\n  node.notifier = notifier;\n  node.zone = typeof Zone !== 'undefined' ? Zone.current : null;\n  node.scheduler.add(node);\n  node.notifier.notify(NotificationSource.RootEffect);\n  return node;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComputationFn,\n  createLinkedSignal,\n  LinkedSignalGetter,\n  LinkedSignalNode,\n  linkedSignalSetFn,\n  linkedSignalUpdateFn,\n  SIGNAL,\n} from '../../../primitives/signals';\nimport {Signal, ValueEqualityFn} from './api';\nimport {signalAsReadonlyFn, WritableSignal} from './signal';\n\nconst identityFn = <T>(v: T) => v;\n\n/**\n * Creates a writable signal whose value is initialized and reset by the linked, reactive computation.\n *\n * @publicApi 20.0\n */\nexport function linkedSignal<D>(\n  computation: () => D,\n  options?: {equal?: ValueEqualityFn<NoInfer<D>>},\n): WritableSignal<D>;\n\n/**\n * Creates a writable signal whose value is initialized and reset by the linked, reactive computation.\n * This is an advanced API form where the computation has access to the previous value of the signal and the computation result.\n *\n * Note: The computation is reactive, meaning the linked signal will automatically update whenever any of the signals used within the computation change.\n *\n * @publicApi 20.0\n */\nexport function linkedSignal<S, D>(options: {\n  source: () => S;\n  computation: (source: NoInfer<S>, previous?: {source: NoInfer<S>; value: NoInfer<D>}) => D;\n  equal?: ValueEqualityFn<NoInfer<D>>;\n}): WritableSignal<D>;\n\nexport function linkedSignal<S, D>(\n  optionsOrComputation:\n    | {\n        source: () => S;\n        computation: ComputationFn<S, D>;\n        equal?: ValueEqualityFn<D>;\n      }\n    | (() => D),\n  options?: {equal?: ValueEqualityFn<D>},\n): WritableSignal<D> {\n  if (typeof optionsOrComputation === 'function') {\n    const getter = createLinkedSignal<D, D>(\n      optionsOrComputation,\n      identityFn<D>,\n      options?.equal,\n    ) as LinkedSignalGetter<D, D> & WritableSignal<D>;\n    return upgradeLinkedSignalGetter(getter);\n  } else {\n    const getter = createLinkedSignal<S, D>(\n      optionsOrComputation.source,\n      optionsOrComputation.computation,\n      optionsOrComputation.equal,\n    );\n    return upgradeLinkedSignalGetter(getter);\n  }\n}\n\nfunction upgradeLinkedSignalGetter<S, D>(getter: LinkedSignalGetter<S, D>): WritableSignal<D> {\n  if (ngDevMode) {\n    getter.toString = () => `[LinkedSignal: ${getter()}]`;\n  }\n\n  const node = getter[SIGNAL] as LinkedSignalNode<S, D>;\n  const upgradedGetter = getter as LinkedSignalGetter<S, D> & WritableSignal<D>;\n\n  upgradedGetter.set = (newValue: D) => linkedSignalSetFn(node, newValue);\n  upgradedGetter.update = (updateFn: (value: D) => D) => linkedSignalUpdateFn(node, updateFn);\n  upgradedGetter.asReadonly = signalAsReadonlyFn.bind(getter as any) as () => Signal<D>;\n\n  return upgradedGetter;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {untracked} from '../render3/reactivity/untracked';\nimport {computed} from '../render3/reactivity/computed';\nimport {signal, signalAsReadonlyFn, WritableSignal} from '../render3/reactivity/signal';\nimport {Signal} from '../render3/reactivity/api';\nimport {effect, EffectRef} from '../render3/reactivity/effect';\nimport {\n  ResourceOptions,\n  ResourceStatus,\n  WritableResource,\n  Resource,\n  ResourceRef,\n  ResourceStreamingLoader,\n  StreamingResourceOptions,\n  ResourceStreamItem,\n  ResourceLoaderParams,\n} from './api';\n\nimport {ValueEqualityFn} from '../../primitives/signals';\n\nimport {Injector} from '../di/injector';\nimport {assertInInjectionContext} from '../di/contextual';\nimport {inject} from '../di/injector_compatibility';\nimport {PendingTasks} from '../pending_tasks';\nimport {linkedSignal} from '../render3/reactivity/linked_signal';\nimport {DestroyRef} from '../linker/destroy_ref';\n\n/**\n * Whether a `Resource.value()` should throw an error when the resource is in the error state.\n *\n * This internal flag is being used to gradually roll out this behavior.\n */\nconst RESOURCE_VALUE_THROWS_ERRORS_DEFAULT = true;\n\n/**\n * Constructs a `Resource` that projects a reactive request to an asynchronous operation defined by\n * a loader function, which exposes the result of the loading operation via signals.\n *\n * Note that `resource` is intended for _read_ operations, not operations which perform mutations.\n * `resource` will cancel in-progress loads via the `AbortSignal` when destroyed or when a new\n * request object becomes available, which could prematurely abort mutations.\n *\n * @experimental 19.0\n */\nexport function resource<T, R>(\n  options: ResourceOptions<T, R> & {defaultValue: NoInfer<T>},\n): ResourceRef<T>;\n\n/**\n * Constructs a `Resource` that projects a reactive request to an asynchronous operation defined by\n * a loader function, which exposes the result of the loading operation via signals.\n *\n * Note that `resource` is intended for _read_ operations, not operations which perform mutations.\n * `resource` will cancel in-progress loads via the `AbortSignal` when destroyed or when a new\n * request object becomes available, which could prematurely abort mutations.\n *\n * @experimental 19.0\n */\nexport function resource<T, R>(options: ResourceOptions<T, R>): ResourceRef<T | undefined>;\nexport function resource<T, R>(options: ResourceOptions<T, R>): ResourceRef<T | undefined> {\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(resource);\n  }\n\n  const oldNameForParams = (\n    options as ResourceOptions<T, R> & {request: ResourceOptions<T, R>['params']}\n  ).request;\n  const params = (options.params ?? oldNameForParams ?? (() => null)) as () => R;\n  return new ResourceImpl<T | undefined, R>(\n    params,\n    getLoader(options),\n    options.defaultValue,\n    options.equal ? wrapEqualityFn(options.equal) : undefined,\n    options.injector ?? inject(Injector),\n    RESOURCE_VALUE_THROWS_ERRORS_DEFAULT,\n  );\n}\n\ntype ResourceInternalStatus = 'idle' | 'loading' | 'resolved' | 'local';\n\n/**\n * Internal state of a resource.\n */\ninterface ResourceProtoState<T> {\n  extRequest: WrappedRequest;\n\n  // For simplicity, status is internally tracked as a subset of the public status enum.\n  // Reloading and Error statuses are projected from Loading and Resolved based on other state.\n  status: ResourceInternalStatus;\n}\n\ninterface ResourceState<T> extends ResourceProtoState<T> {\n  previousStatus: ResourceStatus;\n  stream: Signal<ResourceStreamItem<T>> | undefined;\n}\n\ntype WrappedRequest = {request: unknown; reload: number};\n\n/**\n * Base class which implements `.value` as a `WritableSignal` by delegating `.set` and `.update`.\n */\nabstract class BaseWritableResource<T> implements WritableResource<T> {\n  readonly value: WritableSignal<T>;\n  abstract readonly status: Signal<ResourceStatus>;\n  abstract readonly error: Signal<Error | undefined>;\n\n  abstract reload(): boolean;\n\n  constructor(value: Signal<T>) {\n    this.value = value as WritableSignal<T>;\n    this.value.set = this.set.bind(this);\n    this.value.update = this.update.bind(this);\n    this.value.asReadonly = signalAsReadonlyFn;\n  }\n\n  abstract set(value: T): void;\n\n  private readonly isError = computed(() => this.status() === 'error');\n\n  update(updateFn: (value: T) => T): void {\n    this.set(updateFn(untracked(this.value)));\n  }\n\n  readonly isLoading = computed(() => this.status() === 'loading' || this.status() === 'reloading');\n\n  hasValue(): this is ResourceRef<Exclude<T, undefined>> {\n    // Note: we specifically read `isError()` instead of `status()` here to avoid triggering\n    // reactive consumers which read `hasValue()`. This way, if `hasValue()` is used inside of an\n    // effect, it doesn't cause the effect to rerun on every status change.\n    if (this.isError()) {\n      return false;\n    }\n\n    return this.value() !== undefined;\n  }\n\n  asReadonly(): Resource<T> {\n    return this;\n  }\n}\n\n/**\n * Implementation for `resource()` which uses a `linkedSignal` to manage the resource's state.\n */\nexport class ResourceImpl<T, R> extends BaseWritableResource<T> implements ResourceRef<T> {\n  private readonly pendingTasks: PendingTasks;\n\n  /**\n   * The current state of the resource. Status, value, and error are derived from this.\n   */\n  private readonly state: WritableSignal<ResourceState<T>>;\n\n  /**\n   * Combines the current request with a reload counter which allows the resource to be reloaded on\n   * imperative command.\n   */\n  protected readonly extRequest: WritableSignal<WrappedRequest>;\n  private readonly effectRef: EffectRef;\n\n  private pendingController: AbortController | undefined;\n  private resolvePendingTask: (() => void) | undefined = undefined;\n  private destroyed = false;\n  private unregisterOnDestroy: () => void;\n\n  constructor(\n    request: () => R,\n    private readonly loaderFn: ResourceStreamingLoader<T, R>,\n    defaultValue: T,\n    private readonly equal: ValueEqualityFn<T> | undefined,\n    injector: Injector,\n    throwErrorsFromValue: boolean = RESOURCE_VALUE_THROWS_ERRORS_DEFAULT,\n  ) {\n    super(\n      // Feed a computed signal for the value to `BaseWritableResource`, which will upgrade it to a\n      // `WritableSignal` that delegates to `ResourceImpl.set`.\n      computed(\n        () => {\n          const streamValue = this.state().stream?.();\n\n          if (!streamValue) {\n            return defaultValue;\n          }\n\n          // Prevents `hasValue()` from throwing an error when a reload happened in the error state\n          if (this.state().status === 'loading' && this.error()) {\n            return defaultValue;\n          }\n\n          if (!isResolved(streamValue)) {\n            if (throwErrorsFromValue) {\n              throw new ResourceValueError(this.error()!);\n            } else {\n              return defaultValue;\n            }\n          }\n\n          return streamValue.value;\n        },\n        {equal},\n      ),\n    );\n\n    // Extend `request()` to include a writable reload signal.\n    this.extRequest = linkedSignal({\n      source: request,\n      computation: (request) => ({request, reload: 0}),\n    });\n\n    // The main resource state is managed in a `linkedSignal`, which allows the resource to change\n    // state instantaneously when the request signal changes.\n    this.state = linkedSignal<WrappedRequest, ResourceState<T>>({\n      // Whenever the request changes,\n      source: this.extRequest,\n      // Compute the state of the resource given a change in status.\n      computation: (extRequest, previous) => {\n        const status = extRequest.request === undefined ? 'idle' : 'loading';\n        if (!previous) {\n          return {\n            extRequest,\n            status,\n            previousStatus: 'idle',\n            stream: undefined,\n          };\n        } else {\n          return {\n            extRequest,\n            status,\n            previousStatus: projectStatusOfState(previous.value),\n            // If the request hasn't changed, keep the previous stream.\n            stream:\n              previous.value.extRequest.request === extRequest.request\n                ? previous.value.stream\n                : undefined,\n          };\n        }\n      },\n    });\n\n    this.effectRef = effect(this.loadEffect.bind(this), {\n      injector,\n      manualCleanup: true,\n    });\n\n    this.pendingTasks = injector.get(PendingTasks);\n\n    // Cancel any pending request when the resource itself is destroyed.\n    this.unregisterOnDestroy = injector.get(DestroyRef).onDestroy(() => this.destroy());\n  }\n\n  override readonly status = computed(() => projectStatusOfState(this.state()));\n\n  override readonly error = computed(() => {\n    const stream = this.state().stream?.();\n    return stream && !isResolved(stream) ? stream.error : undefined;\n  });\n\n  /**\n   * Called either directly via `WritableResource.set` or via `.value.set()`.\n   */\n  override set(value: T): void {\n    if (this.destroyed) {\n      return;\n    }\n\n    const error = untracked(this.error);\n    const state = untracked(this.state);\n\n    if (!error) {\n      const current = untracked(this.value);\n      if (\n        state.status === 'local' &&\n        (this.equal ? this.equal(current, value) : current === value)\n      ) {\n        return;\n      }\n    }\n\n    // Enter Local state with the user-defined value.\n    this.state.set({\n      extRequest: state.extRequest,\n      status: 'local',\n      previousStatus: 'local',\n      stream: signal({value}),\n    });\n\n    // We're departing from whatever state the resource was in previously, so cancel any in-progress\n    // loading operations.\n    this.abortInProgressLoad();\n  }\n\n  override reload(): boolean {\n    // We don't want to restart in-progress loads.\n    const {status} = untracked(this.state);\n    if (status === 'idle' || status === 'loading') {\n      return false;\n    }\n\n    // Increment the request reload to trigger the `state` linked signal to switch us to `Reload`\n    this.extRequest.update(({request, reload}) => ({request, reload: reload + 1}));\n    return true;\n  }\n\n  destroy(): void {\n    this.destroyed = true;\n    this.unregisterOnDestroy();\n    this.effectRef.destroy();\n    this.abortInProgressLoad();\n\n    // Destroyed resources enter Idle state.\n    this.state.set({\n      extRequest: {request: undefined, reload: 0},\n      status: 'idle',\n      previousStatus: 'idle',\n      stream: undefined,\n    });\n  }\n\n  private async loadEffect(): Promise<void> {\n    const extRequest = this.extRequest();\n\n    // Capture the previous status before any state transitions. Note that this is `untracked` since\n    // we do not want the effect to depend on the state of the resource, only on the request.\n    const {status: currentStatus, previousStatus} = untracked(this.state);\n\n    if (extRequest.request === undefined) {\n      // Nothing to load (and we should already be in a non-loading state).\n      return;\n    } else if (currentStatus !== 'loading') {\n      // We're not in a loading or reloading state, so this loading request is stale.\n      return;\n    }\n\n    // Cancel any previous loading attempts.\n    this.abortInProgressLoad();\n\n    // Capturing _this_ load's pending task in a local variable is important here. We may attempt to\n    // resolve it twice:\n    //\n    //  1. when the loading function promise resolves/rejects\n    //  2. when cancelling the loading operation\n    //\n    // After the loading operation is cancelled, `this.resolvePendingTask` no longer represents this\n    // particular task, but this `await` may eventually resolve/reject. Thus, when we cancel in\n    // response to (1) below, we need to cancel the locally saved task.\n    let resolvePendingTask: (() => void) | undefined = (this.resolvePendingTask =\n      this.pendingTasks.add());\n\n    const {signal: abortSignal} = (this.pendingController = new AbortController());\n\n    try {\n      // The actual loading is run through `untracked` - only the request side of `resource` is\n      // reactive. This avoids any confusion with signals tracking or not tracking depending on\n      // which side of the `await` they are.\n      const stream = await untracked(() => {\n        return this.loaderFn({\n          params: extRequest.request as Exclude<R, undefined>,\n          // TODO(alxhub): cleanup after g3 removal of `request` alias.\n          request: extRequest.request as Exclude<R, undefined>,\n          abortSignal,\n          previous: {\n            status: previousStatus,\n          },\n        } as ResourceLoaderParams<R>);\n      });\n\n      // If this request has been aborted, or the current request no longer\n      // matches this load, then we should ignore this resolution.\n      if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n        return;\n      }\n\n      this.state.set({\n        extRequest,\n        status: 'resolved',\n        previousStatus: 'resolved',\n        stream,\n      });\n    } catch (err) {\n      if (abortSignal.aborted || untracked(this.extRequest) !== extRequest) {\n        return;\n      }\n\n      this.state.set({\n        extRequest,\n        status: 'resolved',\n        previousStatus: 'error',\n        stream: signal({error: encapsulateResourceError(err)}),\n      });\n    } finally {\n      // Resolve the pending task now that the resource has a value.\n      resolvePendingTask?.();\n      resolvePendingTask = undefined;\n    }\n  }\n\n  private abortInProgressLoad(): void {\n    untracked(() => this.pendingController?.abort());\n    this.pendingController = undefined;\n\n    // Once the load is aborted, we no longer want to block stability on its resolution.\n    this.resolvePendingTask?.();\n    this.resolvePendingTask = undefined;\n  }\n}\n\n/**\n * Wraps an equality function to handle either value being `undefined`.\n */\nfunction wrapEqualityFn<T>(equal: ValueEqualityFn<T>): ValueEqualityFn<T | undefined> {\n  return (a, b) => (a === undefined || b === undefined ? a === b : equal(a, b));\n}\n\nfunction getLoader<T, R>(options: ResourceOptions<T, R>): ResourceStreamingLoader<T, R> {\n  if (isStreamingResourceOptions(options)) {\n    return options.stream;\n  }\n\n  return async (params) => {\n    try {\n      return signal({value: await options.loader(params)});\n    } catch (err) {\n      return signal({error: encapsulateResourceError(err)});\n    }\n  };\n}\n\nfunction isStreamingResourceOptions<T, R>(\n  options: ResourceOptions<T, R>,\n): options is StreamingResourceOptions<T, R> {\n  return !!(options as StreamingResourceOptions<T, R>).stream;\n}\n\n/**\n * Project from a state with `ResourceInternalStatus` to the user-facing `ResourceStatus`\n */\nfunction projectStatusOfState(state: ResourceState<unknown>): ResourceStatus {\n  switch (state.status) {\n    case 'loading':\n      return state.extRequest.reload === 0 ? 'loading' : 'reloading';\n    case 'resolved':\n      return isResolved(state.stream!()) ? 'resolved' : 'error';\n    default:\n      return state.status;\n  }\n}\n\nfunction isResolved<T>(state: ResourceStreamItem<T>): state is {value: T} {\n  return (state as {error: unknown}).error === undefined;\n}\n\nexport function encapsulateResourceError(error: unknown): Error {\n  if (error instanceof Error) {\n    return error;\n  }\n\n  return new ResourceWrappedError(error);\n}\n\nclass ResourceValueError extends Error {\n  constructor(error: Error) {\n    super(\n      ngDevMode\n        ? `Resource is currently in an error state (see Error.cause for details): ${error.message}`\n        : error.message,\n      {cause: error},\n    );\n  }\n}\n\nclass ResourceWrappedError extends Error {\n  constructor(error: unknown) {\n    super(\n      ngDevMode\n        ? `Resource returned an error that's not an Error instance: ${String(error)}. Check this error's .cause for the actual error.`\n        : String(error),\n      {cause: error},\n    );\n  }\n}\n"], "names": ["untrackedPrimitive"], "mappings": ";;;;;;;;;;AAiBA;;;;;;;;;;;;AAYG;MACU,gBAAgB,CAAA;IACnB,SAAS,GAAG,KAAK;IACjB,SAAS,GAAqC,IAAI;IAClD,YAAY,GAAG,MAAM,CAAC,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG7D,IAAA,UAAU,GAAe,MAAM,CAAC,UAAU,CAAC;AAE3C,IAAA,WAAA,GAAA;;AAEE,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAK;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC,CAAC;;AAGJ,IAAA,SAAS,CAAC,QAA4B,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,YAAY,CAAA,GAAA,8CAEpB,SAAS;gBACP,oDAAoD;AAClD,oBAAA,8CAA8C,CACnD;;QAGH,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC;QAEtC,OAAO;YACL,WAAW,EAAE,MAAK;gBAChB,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC;gBAC7C,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;oBACnC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;;aAEjC;SACF;;;AAIH,IAAA,IAAI,CAAC,KAAQ,EAAA;AACX,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,CAAC,IAAI,CACV,kBAAkB,kDAEhB,SAAS;gBACP,6CAA6C;oBAC3C,8CAA8C,CACnD,CACF;YACD;;AAGF,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC3B;;AAGF,QAAA,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAChD,QAAA,IAAI;AACF,YAAA,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;AACvC,gBAAA,IAAI;oBACF,UAAU,CAAC,KAAK,CAAC;;gBACjB,OAAO,GAAY,EAAE;AACrB,oBAAA,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,GAAG,CAAC;;;;gBAG/B;YACR,iBAAiB,CAAC,gBAAgB,CAAC;;;AAGxC;AAED;AACM,SAAU,mBAAmB,CAAC,GAAuB,EAAA;IACzD,OAAO,GAAG,CAAC,UAAU;AACvB;;AC9FA;;;AAGG;AACG,SAAU,SAAS,CAAI,kBAA2B,EAAA;AACtD,IAAA,OAAOA,WAAkB,CAAC,kBAAkB,CAAC;AAC/C;;ACWA;;AAEG;AACa,SAAA,QAAQ,CAAI,WAAoB,EAAE,OAAkC,EAAA;IAClF,MAAM,MAAM,GAAG,cAAc,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC;IAE1D,IAAI,SAAS,EAAE;QACb,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAc,WAAA,EAAA,MAAM,EAAE,CAAA,CAAA,CAAG;QACjD,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,OAAO,EAAE,SAAS;;AAG/C,IAAA,OAAO,MAAM;AACf;;MCUa,aAAa,CAAA;IACxB,CAAC,MAAM;AAEP,IAAA,WAAA,CAAY,IAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI;;IAGrB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE;;AAEzB;AAsDD;;;;;;;;;;;;;;;;;AAiBG;AACa,SAAA,MAAM,CACpB,QAAsD,EACtD,OAA6B,EAAA;IAE7B,SAAS;QACP,0BAA0B,CACxB,MAAM,EACN,yEAAyE;AACvE,YAAA,0CAA0C,CAC7C;AAEH,IAAA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnC,wBAAwB,CAAC,MAAM,CAAC;;IAGlC,IAAI,SAAS,IAAI,OAAO,EAAE,iBAAiB,KAAK,SAAS,EAAE;AACzD,QAAA,OAAO,CAAC,IAAI,CACV,CAAA,qGAAA,CAAuG,CACxG;;IAGH,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC;IACtD,IAAI,UAAU,GAAG,OAAO,EAAE,aAAa,KAAK,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,IAAI;AAElF,IAAA,IAAI,IAAgB;AAEpB,IAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IACrE,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,wBAAwB,CAAC;AACvD,IAAA,IAAI,WAAW,KAAK,IAAI,EAAE;;QAExB,IAAI,GAAG,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAC7D,QAAA,IAAI,UAAU,YAAY,sBAAsB,IAAI,UAAU,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE;;;YAG1F,UAAU,GAAG,IAAI;;;SAEd;;AAEL,QAAA,IAAI,GAAG,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,QAAQ,CAAC;;AAE5E,IAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AAExB,IAAA,IAAI,UAAU,KAAK,IAAI,EAAE;;AAEvB,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;;AAG/D,IAAA,MAAM,SAAS,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC;IAEzC,IAAI,SAAS,EAAE;QACb,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,SAAS,IAAI,EAAE;AACzC,QAAA,MAAM,2BAA2B,GAAG,0BAA0B,CAAC,EAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;AACvF,QAAA,IAAI;YACF,sBAAsB,CAAC,SAAS,CAAC;;gBACzB;YACR,0BAA0B,CAAC,2BAA2B,CAAC;;;AAI3D,IAAA,OAAO,SAAS;AAClB;AAuBO,MAAM,gBAAgB;AAC3B,gBAAgB,CAAC,OAAO;AACtB,IAAA,GAAG,aAAa;AAChB,IAAA,oBAAoB,EAAE,IAAI;AAC1B,IAAA,yBAAyB,EAAE,IAAI;AAC/B,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,MAAM,EAAE,KAAK;AACb,IAAA,UAAU,EAAE,SAAS;AACrB,IAAA,IAAI,EAAE,IAAI;AACV,IAAA,IAAI,EAAE,QAAQ;AACd,IAAA,WAAW,EAAE,IAAI;IACjB,GAAG,GAAA;AACD,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAElB,QAAA,IAAI,SAAS,IAAI,qBAAqB,EAAE,EAAE;AACxC,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,iEAAA,CAAmE,CAAC;;QAGtF,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACxD;;AAEF,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI;AAElB,QAAA,MAAM,iBAAiB,GAA4B,CAAC,SAAS,KAC3D,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC;AAE1C,QAAA,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC;;;AAIhD,QAAA,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,KAAK,CAAC;AACvD,QAAA,IAAI;YACF,IAAI,CAAC,YAAY,EAAE;AACnB,YAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC;;gBAClB;YACR,oBAAoB,CAAC,mBAAmB,CAAC;AACzC,YAAA,wBAAwB,CAAC,IAAI,EAAE,QAAQ,CAAC;;KAE3C;IAED,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE;YAC5B;;AAEF,QAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC5C,QAAA,IAAI;;;;AAIF,YAAA,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC7B,gBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,EAAG,EAAE;;;gBAElB;AACR,YAAA,IAAI,CAAC,UAAU,GAAG,EAAE;YACpB,iBAAiB,CAAC,YAAY,CAAC;;KAElC;CACF,CAAC,GAAG;AAEA,MAAM,gBAAgB;AAC3B,gBAAgB,CAAC,OAAO;AACtB,IAAA,GAAG,gBAAgB;IACnB,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC7B,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,wCAA+B;KACpD;IACD,OAAO,GAAA;QACL,eAAe,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,YAAY,EAAE;AACnB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;KAC5B;CACF,CAAC,GAAG;AAEA,MAAM,gBAAgB;AAC3B,gBAAgB,CAAC,OAAO;AACtB,IAAA,GAAG,gBAAgB;IACnB,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAChB,QAAA,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC;AACpC,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,wCAA+B;KACpD;IACD,OAAO,GAAA;QACL,eAAe,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC;KACjC;CACF,CAAC,GAAG;SAES,gBAAgB,CAC9B,IAAW,EACX,QAAkC,EAClC,EAAgD,EAAA;IAEhD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAmB;AAC9D,IAAA,IAAI,CAAC,IAAI,GAAG,IAAI;AAChB,IAAA,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI;AAC7D,IAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,IAAA,IAAI,CAAC,EAAE,GAAG,EAAE;AAEZ,IAAA,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,GAAG,EAAE;IAC3B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AAEvB,IAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAC9B,IAAA,OAAO,IAAI;AACb;SAEgB,gBAAgB,CAC9B,EAAgD,EAChD,SAA0B,EAC1B,QAAkC,EAAA;IAElC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAmB;AAC9D,IAAA,IAAI,CAAC,EAAE,GAAG,EAAE;AACZ,IAAA,IAAI,CAAC,SAAS,GAAG,SAAS;AAC1B,IAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,IAAA,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI;AAC7D,IAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AACxB,IAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,wCAA+B;AACnD,IAAA,OAAO,IAAI;AACb;;AC3TA,MAAM,UAAU,GAAG,CAAI,CAAI,KAAK,CAAC;AA0BjB,SAAA,YAAY,CAC1B,oBAMa,EACb,OAAsC,EAAA;AAEtC,IAAA,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE;AAC9C,QAAA,MAAM,MAAM,GAAG,kBAAkB,CAC/B,oBAAoB,GACpB,UAAa,GACb,OAAO,EAAE,KAAK,CACiC;AACjD,QAAA,OAAO,yBAAyB,CAAC,MAAM,CAAC;;SACnC;AACL,QAAA,MAAM,MAAM,GAAG,kBAAkB,CAC/B,oBAAoB,CAAC,MAAM,EAC3B,oBAAoB,CAAC,WAAW,EAChC,oBAAoB,CAAC,KAAK,CAC3B;AACD,QAAA,OAAO,yBAAyB,CAAC,MAAM,CAAC;;AAE5C;AAEA,SAAS,yBAAyB,CAAO,MAAgC,EAAA;IACvE,IAAI,SAAS,EAAE;QACb,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAkB,eAAA,EAAA,MAAM,EAAE,CAAA,CAAA,CAAG;;AAGvD,IAAA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAA2B;IACrD,MAAM,cAAc,GAAG,MAAsD;AAE7E,IAAA,cAAc,CAAC,GAAG,GAAG,CAAC,QAAW,KAAK,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC;AACvE,IAAA,cAAc,CAAC,MAAM,GAAG,CAAC,QAAyB,KAAK,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC;IAC3F,cAAc,CAAC,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,MAAa,CAAoB;AAErF,IAAA,OAAO,cAAc;AACvB;;ACpDA;;;;AAIG;AACH,MAAM,oCAAoC,GAAG,IAAI;AA2B3C,SAAU,QAAQ,CAAO,OAA8B,EAAA;AAC3D,IAAA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnC,wBAAwB,CAAC,QAAQ,CAAC;;AAGpC,IAAA,MAAM,gBAAgB,GACpB,OACD,CAAC,OAAO;AACT,IAAA,MAAM,MAAM,IAAI,OAAO,CAAC,MAAM,IAAI,gBAAgB,KAAK,MAAM,IAAI,CAAC,CAAY;AAC9E,IAAA,OAAO,IAAI,YAAY,CACrB,MAAM,EACN,SAAS,CAAC,OAAO,CAAC,EAClB,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,SAAS,EACzD,OAAO,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,EACpC,oCAAoC,CACrC;AACH;AAsBA;;AAEG;AACH,MAAe,oBAAoB,CAAA;AACxB,IAAA,KAAK;AAMd,IAAA,WAAA,CAAY,KAAgB,EAAA;AAC1B,QAAA,IAAI,CAAC,KAAK,GAAG,KAA0B;AACvC,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AACpC,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,kBAAkB;;AAK3B,IAAA,OAAO,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO,CAAC;AAEpE,IAAA,MAAM,CAAC,QAAyB,EAAA;AAC9B,QAAA,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;;IAGlC,SAAS,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,SAAS,IAAI,IAAI,CAAC,MAAM,EAAE,KAAK,WAAW,CAAC;IAEjG,QAAQ,GAAA;;;;AAIN,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;AAClB,YAAA,OAAO,KAAK;;AAGd,QAAA,OAAO,IAAI,CAAC,KAAK,EAAE,KAAK,SAAS;;IAGnC,UAAU,GAAA;AACR,QAAA,OAAO,IAAI;;AAEd;AAED;;AAEG;AACG,MAAO,YAAmB,SAAQ,oBAAuB,CAAA;AAsB1C,IAAA,QAAA;AAEA,IAAA,KAAA;AAvBF,IAAA,YAAY;AAE7B;;AAEG;AACc,IAAA,KAAK;AAEtB;;;AAGG;AACgB,IAAA,UAAU;AACZ,IAAA,SAAS;AAElB,IAAA,iBAAiB;IACjB,kBAAkB,GAA6B,SAAS;IACxD,SAAS,GAAG,KAAK;AACjB,IAAA,mBAAmB;IAE3B,WACE,CAAA,OAAgB,EACC,QAAuC,EACxD,YAAe,EACE,KAAqC,EACtD,QAAkB,EAClB,oBAAA,GAAgC,oCAAoC,EAAA;QAEpE,KAAK;;;QAGH,QAAQ,CACN,MAAK;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI;YAE3C,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,OAAO,YAAY;;;AAIrB,YAAA,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE,EAAE;AACrD,gBAAA,OAAO,YAAY;;AAGrB,YAAA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;gBAC5B,IAAI,oBAAoB,EAAE;oBACxB,MAAM,IAAI,kBAAkB,CAAC,IAAI,CAAC,KAAK,EAAG,CAAC;;qBACtC;AACL,oBAAA,OAAO,YAAY;;;YAIvB,OAAO,WAAW,CAAC,KAAK;AAC1B,SAAC,EACD,EAAC,KAAK,EAAC,CACR,CACF;QAlCgB,IAAQ,CAAA,QAAA,GAAR,QAAQ;QAER,IAAK,CAAA,KAAA,GAAL,KAAK;;AAmCtB,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;AAC7B,YAAA,MAAM,EAAE,OAAO;AACf,YAAA,WAAW,EAAE,CAAC,OAAO,MAAM,EAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAC,CAAC;AACjD,SAAA,CAAC;;;AAIF,QAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAmC;;YAE1D,MAAM,EAAE,IAAI,CAAC,UAAU;;AAEvB,YAAA,WAAW,EAAE,CAAC,UAAU,EAAE,QAAQ,KAAI;AACpC,gBAAA,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,KAAK,SAAS,GAAG,MAAM,GAAG,SAAS;gBACpE,IAAI,CAAC,QAAQ,EAAE;oBACb,OAAO;wBACL,UAAU;wBACV,MAAM;AACN,wBAAA,cAAc,EAAE,MAAM;AACtB,wBAAA,MAAM,EAAE,SAAS;qBAClB;;qBACI;oBACL,OAAO;wBACL,UAAU;wBACV,MAAM;AACN,wBAAA,cAAc,EAAE,oBAAoB,CAAC,QAAQ,CAAC,KAAK,CAAC;;wBAEpD,MAAM,EACJ,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC;AAC/C,8BAAE,QAAQ,CAAC,KAAK,CAAC;AACjB,8BAAE,SAAS;qBAChB;;aAEJ;AACF,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAClD,QAAQ;AACR,YAAA,aAAa,EAAE,IAAI;AACpB,SAAA,CAAC;QAEF,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC;;QAG9C,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;;AAGnE,IAAA,MAAM,GAAG,QAAQ,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;AAE3D,IAAA,KAAK,GAAG,QAAQ,CAAC,MAAK;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,MAAM,IAAI;AACtC,QAAA,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,SAAS;AACjE,KAAC,CAAC;AAEF;;AAEG;AACM,IAAA,GAAG,CAAC,KAAQ,EAAA;AACnB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB;;QAGF,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;QACnC,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;QAEnC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AACrC,YAAA,IACE,KAAK,CAAC,MAAM,KAAK,OAAO;iBACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,EAC7D;gBACA;;;;AAKJ,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACb,UAAU,EAAE,KAAK,CAAC,UAAU;AAC5B,YAAA,MAAM,EAAE,OAAO;AACf,YAAA,cAAc,EAAE,OAAO;AACvB,YAAA,MAAM,EAAE,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC;AACxB,SAAA,CAAC;;;QAIF,IAAI,CAAC,mBAAmB,EAAE;;IAGnB,MAAM,GAAA;;QAEb,MAAM,EAAC,MAAM,EAAC,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;QACtC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE;AAC7C,YAAA,OAAO,KAAK;;;QAId,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAC,OAAO,EAAE,MAAM,EAAC,MAAM,EAAC,OAAO,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAC,CAAC,CAAC;AAC9E,QAAA,OAAO,IAAI;;IAGb,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,mBAAmB,EAAE;AAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;QACxB,IAAI,CAAC,mBAAmB,EAAE;;AAG1B,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACb,UAAU,EAAE,EAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,EAAC;AAC3C,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,cAAc,EAAE,MAAM;AACtB,YAAA,MAAM,EAAE,SAAS;AAClB,SAAA,CAAC;;AAGI,IAAA,MAAM,UAAU,GAAA;AACtB,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE;;;AAIpC,QAAA,MAAM,EAAC,MAAM,EAAE,aAAa,EAAE,cAAc,EAAC,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AAErE,QAAA,IAAI,UAAU,CAAC,OAAO,KAAK,SAAS,EAAE;;YAEpC;;AACK,aAAA,IAAI,aAAa,KAAK,SAAS,EAAE;;YAEtC;;;QAIF,IAAI,CAAC,mBAAmB,EAAE;;;;;;;;;;AAW1B,QAAA,IAAI,kBAAkB,IAA8B,IAAI,CAAC,kBAAkB;AACzE,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AAE1B,QAAA,MAAM,EAAC,MAAM,EAAE,WAAW,EAAC,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,eAAe,EAAE,CAAC;AAE9E,QAAA,IAAI;;;;AAIF,YAAA,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,MAAK;gBAClC,OAAO,IAAI,CAAC,QAAQ,CAAC;oBACnB,MAAM,EAAE,UAAU,CAAC,OAAgC;;oBAEnD,OAAO,EAAE,UAAU,CAAC,OAAgC;oBACpD,WAAW;AACX,oBAAA,QAAQ,EAAE;AACR,wBAAA,MAAM,EAAE,cAAc;AACvB,qBAAA;AACyB,iBAAA,CAAC;AAC/B,aAAC,CAAC;;;AAIF,YAAA,IAAI,WAAW,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE;gBACpE;;AAGF,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBACb,UAAU;AACV,gBAAA,MAAM,EAAE,UAAU;AAClB,gBAAA,cAAc,EAAE,UAAU;gBAC1B,MAAM;AACP,aAAA,CAAC;;QACF,OAAO,GAAG,EAAE;AACZ,YAAA,IAAI,WAAW,CAAC,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE;gBACpE;;AAGF,YAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;gBACb,UAAU;AACV,gBAAA,MAAM,EAAE,UAAU;AAClB,gBAAA,cAAc,EAAE,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,EAAC,KAAK,EAAE,wBAAwB,CAAC,GAAG,CAAC,EAAC,CAAC;AACvD,aAAA,CAAC;;gBACM;;YAER,kBAAkB,IAAI;YACtB,kBAAkB,GAAG,SAAS;;;IAI1B,mBAAmB,GAAA;QACzB,SAAS,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,CAAC;AAChD,QAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS;;AAGlC,QAAA,IAAI,CAAC,kBAAkB,IAAI;AAC3B,QAAA,IAAI,CAAC,kBAAkB,GAAG,SAAS;;AAEtC;AAED;;AAEG;AACH,SAAS,cAAc,CAAI,KAAyB,EAAA;AAClD,IAAA,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/E;AAEA,SAAS,SAAS,CAAO,OAA8B,EAAA;AACrD,IAAA,IAAI,0BAA0B,CAAC,OAAO,CAAC,EAAE;QACvC,OAAO,OAAO,CAAC,MAAM;;AAGvB,IAAA,OAAO,OAAO,MAAM,KAAI;AACtB,QAAA,IAAI;AACF,YAAA,OAAO,MAAM,CAAC,EAAC,KAAK,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAC,CAAC;;QACpD,OAAO,GAAG,EAAE;YACZ,OAAO,MAAM,CAAC,EAAC,KAAK,EAAE,wBAAwB,CAAC,GAAG,CAAC,EAAC,CAAC;;AAEzD,KAAC;AACH;AAEA,SAAS,0BAA0B,CACjC,OAA8B,EAAA;AAE9B,IAAA,OAAO,CAAC,CAAE,OAA0C,CAAC,MAAM;AAC7D;AAEA;;AAEG;AACH,SAAS,oBAAoB,CAAC,KAA6B,EAAA;AACzD,IAAA,QAAQ,KAAK,CAAC,MAAM;AAClB,QAAA,KAAK,SAAS;AACZ,YAAA,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG,WAAW;AAChE,QAAA,KAAK,UAAU;AACb,YAAA,OAAO,UAAU,CAAC,KAAK,CAAC,MAAO,EAAE,CAAC,GAAG,UAAU,GAAG,OAAO;AAC3D,QAAA;YACE,OAAO,KAAK,CAAC,MAAM;;AAEzB;AAEA,SAAS,UAAU,CAAI,KAA4B,EAAA;AACjD,IAAA,OAAQ,KAA0B,CAAC,KAAK,KAAK,SAAS;AACxD;AAEM,SAAU,wBAAwB,CAAC,KAAc,EAAA;AACrD,IAAA,IAAI,KAAK,YAAY,KAAK,EAAE;AAC1B,QAAA,OAAO,KAAK;;AAGd,IAAA,OAAO,IAAI,oBAAoB,CAAC,KAAK,CAAC;AACxC;AAEA,MAAM,kBAAmB,SAAQ,KAAK,CAAA;AACpC,IAAA,WAAA,CAAY,KAAY,EAAA;AACtB,QAAA,KAAK,CACH;AACE,cAAE,CAAA,uEAAA,EAA0E,KAAK,CAAC,OAAO,CAAE;cACzF,KAAK,CAAC,OAAO,EACjB,EAAC,KAAK,EAAE,KAAK,EAAC,CACf;;AAEJ;AAED,MAAM,oBAAqB,SAAQ,KAAK,CAAA;AACtC,IAAA,WAAA,CAAY,KAAc,EAAA;AACxB,QAAA,KAAK,CACH;AACE,cAAE,CAA4D,yDAAA,EAAA,MAAM,CAAC,KAAK,CAAC,CAAmD,iDAAA;AAC9H,cAAE,MAAM,CAAC,KAAK,CAAC,EACjB,EAAC,KAAK,EAAE,KAAK,EAAC,CACf;;AAEJ;;;;"}