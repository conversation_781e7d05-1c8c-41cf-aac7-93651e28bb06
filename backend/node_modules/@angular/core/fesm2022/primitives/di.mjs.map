{"version": 3, "file": "di.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/packages/core/primitives/di/src/injection_token.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from './type';\n/**\n * Information about how a type or `InjectionToken` interfaces with the DI\n * system. This describes:\n *\n * 1. *How* the type is provided\n *    The declaration must specify only one of the following:\n *    - A `value` which is a predefined instance of the type.\n *    - A `factory` which defines how to create the given type `T`, possibly\n *      requesting injection of other types if necessary.\n *    - Neither, in which case the type is expected to already be present in the\n *      injector hierarchy. This is used for internal use cases.\n *\n * 2. *Where* the type is stored (if it is stored)\n *    - The `providedIn` parameter specifies which injector the type belongs to.\n *    - The `token` is used as the key to store the type in the injector.\n */\nexport interface ɵɵInjectableDeclaration<T> {\n  /**\n   * Specifies that the given type belongs to a particular `Injector`,\n   * `NgModule`, or a special scope (e.g. `'root'`).\n   *\n   * `any` is deprecated and will be removed soon.\n   *\n   * A value of `null` indicates that the injectable does not belong to any\n   * scope, and won't be stored in any injector. For declarations with a\n   * factory, this will create a new instance of the type each time it is\n   * requested.\n   */\n  providedIn: Type<any> | 'root' | 'platform' | 'any' | null;\n\n  /**\n   * The token to which this definition belongs.\n   *\n   * Note that this may not be the same as the type that the `factory` will create.\n   */\n  token: unknown;\n\n  /**\n   * Factory method to execute to create an instance of the injectable.\n   */\n  factory?: (t?: Type<any>) => T;\n\n  /**\n   * In a case of no explicit injector, a location where the instance of the injectable is stored.\n   */\n  value?: T;\n}\n\n/**\n * A `Type` which has a `ɵprov: ɵɵInjectableDeclaration` static field.\n *\n * `InjectableType`s contain their own Dependency Injection metadata and are usable in an\n * `InjectorDef`-based `StaticInjector`.\n *\n * @publicApi\n */\nexport interface InjectionToken<T> {\n  ɵprov: ɵɵInjectableDeclaration<T>;\n}\n\nexport function defineInjectable<T>(opts: {\n  token: unknown;\n  providedIn?: Type<any> | 'root' | 'platform' | 'any' | 'environment' | null;\n  factory: () => T;\n}): ɵɵInjectableDeclaration<T> {\n  return {\n    token: opts.token,\n    providedIn: (opts.providedIn as any) || null,\n    factory: opts.factory,\n    value: undefined,\n  } as ɵɵInjectableDeclaration<T>;\n}\n\nexport type Constructor<T> = Function & {prototype: T};\n\nexport function registerInjectable<T>(\n  ctor: unknown,\n  declaration: ɵɵInjectableDeclaration<T>,\n): InjectionToken<T> {\n  (ctor as unknown as InjectionToken<T>).ɵprov = declaration;\n  return ctor as Constructor<T> & InjectionToken<T>;\n}\n"], "names": [], "mappings": ";;;;;;;;AAqEM,SAAU,gBAAgB,CAAI,IAInC,EAAA;IACC,OAAO;QACL,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,QAAA,UAAU,EAAG,IAAI,CAAC,UAAkB,IAAI,IAAI;QAC5C,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,QAAA,KAAK,EAAE,SAAS;KACa;AACjC;AAIgB,SAAA,kBAAkB,CAChC,IAAa,EACb,WAAuC,EAAA;AAEtC,IAAA,IAAqC,CAAC,KAAK,GAAG,WAAW;AAC1D,IAAA,OAAO,IAA0C;AACnD;;;;"}