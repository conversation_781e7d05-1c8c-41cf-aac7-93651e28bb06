{"version": 3, "file": "ne-IN.js", "sourceRoot": "", "sources": ["ne-IN.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,OAAO,EAAC,CAAC,CAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,WAAW,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,WAAW,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,YAAY,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,eAAe,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,gBAAgB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ne-IN\",[[\"पूर्वाह्न\",\"अपराह्न\"],u,u],u,[[\"आ\",\"सो\",\"म\",\"बु\",\"बि\",\"शु\",\"श\"],[\"आइत\",\"सोम\",\"मङ्गल\",\"बुध\",\"बिहि\",\"शुक्र\",\"शनि\"],[\"आइतबार\",\"सोमबार\",\"मङ्गलबार\",\"बुधबार\",\"बिहिबार\",\"शुक्रबार\",\"शनिबार\"],[\"आइत\",\"सोम\",\"मङ्गल\",\"बुध\",\"बिहि\",\"शुक्र\",\"शनि\"]],u,[[\"जन\",\"फेब\",\"मार्च\",\"अप्र\",\"मे\",\"जुन\",\"जुल\",\"अग\",\"सेप\",\"अक्टो\",\"नोभे\",\"डिसे\"],[\"जनवरी\",\"फेब्रुअरी\",\"मार्च\",\"अप्रिल\",\"मे\",\"जुन\",\"जुलाई\",\"अगस्ट\",\"सेप्टेम्बर\",\"अक्टोबर\",\"नोभेम्बर\",\"डिसेम्बर\"],u],[[\"जन\",\"फेेब\",\"मार्च\",\"अप्र\",\"मे\",\"जुन\",\"जुल\",\"अग\",\"सेप\",\"अक्टो\",\"नोभे\",\"डिसे\"],[\"जनवरी\",\"फेब्रुअरी\",\"मार्च\",\"अप्रिल\",\"मे\",\"जुन\",\"जुलाई\",\"अगस्ट\",\"सेप्टेम्बर\",\"अक्टोबर\",\"नोभेम्बर\",\"डिसेम्बर\"],u],[[\"ईसा पूर्व\",\"सन्\"],u,u],0,[0,0],[\"yy/M/d\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤ #,##,##0.00\",\"#E0\"],\"INR\",\"₹\",\"भारतीय रूपिँया\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"NPR\":[\"नेरू\",\"रू\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}