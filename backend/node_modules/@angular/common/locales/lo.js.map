{"version": 3, "file": "lo.js", "sourceRoot": "", "sources": ["lo.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,KAAK,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,oBAAoB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,0BAA0B,EAAC,6BAA6B,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,iBAAiB,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,sBAAsB,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,SAAS,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"lo\",[[\"ກ່ອນທ່ຽງ\",\"ຫຼັງທ່ຽງ\"],u,u],u,[[\"ອາ\",\"ຈ\",\"ອ\",\"ພ\",\"ພຫ\",\"ສຸ\",\"ສ\"],[\"ອາທິດ\",\"ຈັນ\",\"ອັງຄານ\",\"ພຸດ\",\"ພະຫັດ\",\"ສຸກ\",\"ເສົາ\"],[\"ວັນອາທິດ\",\"ວັນຈັນ\",\"ວັນອັງຄານ\",\"ວັນພຸດ\",\"ວັນພະຫັດ\",\"ວັນສຸກ\",\"ວັນເສົາ\"],[\"ອາ.\",\"ຈ.\",\"ອ.\",\"ພ.\",\"ພຫ.\",\"ສຸ.\",\"ສ.\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"ມ.ກ.\",\"ກ.ພ.\",\"ມ.ນ.\",\"ມ.ສ.\",\"ພ.ພ.\",\"ມິ.ຖ.\",\"ກ.ລ.\",\"ສ.ຫ.\",\"ກ.ຍ.\",\"ຕ.ລ.\",\"ພ.ຈ.\",\"ທ.ວ.\"],[\"ມັງກອນ\",\"ກຸມພາ\",\"ມີນາ\",\"ເມສາ\",\"ພຶດສະພາ\",\"ມິຖຸນາ\",\"ກໍລະກົດ\",\"ສິງຫາ\",\"ກັນຍາ\",\"ຕຸລາ\",\"ພະຈິກ\",\"ທັນວາ\"]],u,[[\"ກ່ອນ ຄ.ສ.\",\"ຄ.ສ.\"],u,[\"ກ່ອນຄຣິດສັກກະລາດ\",\"ຄຣິດສັກກະລາດ\"]],0,[6,0],[\"d/M/y\",\"d MMM y\",\"d MMMM y\",\"EEEE ທີ d MMMM G y\"],[\"H:mm\",\"H:mm:ss\",\"H ໂມງ m ນາທີ ss ວິນາທີ z\",\"H ໂມງ m ນາທີ ss ວິນາທີ zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"ບໍ່​ແມ່ນ​ໂຕ​ເລກ\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00;¤-#,##0.00\",\"#\"],\"LAK\",\"₭\",\"ລາວ ກີບ\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"LAK\":[\"₭\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}