{"version": 3, "file": "mg.js", "sourceRoot": "", "sources": ["mg.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,aAAa,EAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,MAAM,EAAC,UAAU,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,WAAW,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,IAAI,EAAC,QAAQ,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === Math.floor(n) && (n >= 0 && n <= 1))\n    return 1;\nreturn 5;\n}\n\nexport default [\"mg\",[[\"AM\",\"PM\"],u,u],u,[[\"A\",\"A\",\"T\",\"A\",\"A\",\"Z\",\"A\"],[\"Alah\",\"Al<PERSON>\",\"<PERSON>l\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>om\",\"<PERSON><PERSON>\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON>ats<PERSON>in<PERSON>\",\"<PERSON>lata\",\"Alarobia\",\"Alakamisy\",\"Zoma\",\"<PERSON>ab<PERSON><PERSON>\"],[\"<PERSON><PERSON>\",\"<PERSON>ats\",\"<PERSON>l\",\"<PERSON>ar\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"A\",\"M\",\"J\",\"J\",\"A\",\"<PERSON>\",\"O\",\"N\",\"D\"],[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>y\",\"<PERSON>\",\"<PERSON>l\",\"<PERSON><PERSON>\",\"Sep\",\"<PERSON>t\",\"Nov\",\"<PERSON>\"],[\"<PERSON>oary\",\"<PERSON>roary\",\"<PERSON><PERSON>\",\"Aprily\",\"<PERSON>y\",\"<PERSON>a\",\"<PERSON><PERSON>\",\"A<PERSON><PERSON><PERSON>\",\"Septam<PERSON>\",\"<PERSON>to<PERSON>\",\"<PERSON>mbra\",\"<PERSON>am<PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\"],u,[\"Alohan’i JK\",\"Aorian’i JK\"]],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"MGA\",\"Ar\",\"Ariary\",{\"JPY\":[\"JP¥\",\"¥\"],\"MGA\":[\"Ar\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}