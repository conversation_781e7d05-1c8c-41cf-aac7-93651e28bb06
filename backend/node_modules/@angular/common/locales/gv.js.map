{"version": 3, "file": "gv.js", "sourceRoot": "", "sources": ["gv.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;QACvB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC;QACvB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QACxG,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACV,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,CAAC,EAAC,CAAC,eAAe,EAAC,iBAAiB,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,aAAa,EAAC,eAAe,EAAC,WAAW,EAAC,aAAa,EAAC,eAAe,EAAC,YAAY,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n\nif (v === 0 && i % 10 === 1)\n    return 1;\nif (v === 0 && i % 10 === 2)\n    return 2;\nif (v === 0 && (i % 100 === 0 || (i % 100 === 20 || (i % 100 === 40 || (i % 100 === 60 || i % 100 === 80)))))\n    return 3;\nif (!(v === 0))\n    return 4;\nreturn 5;\n}\n\nexport default [\"gv\",[[\"a.m.\",\"p.m.\"],u,u],u,[[\"<PERSON>\",\"<PERSON>\",\"T\",\"W\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON>\",\"Je<PERSON>\",\"Je<PERSON>\",\"<PERSON><PERSON>\",\"Jerd\",\"Je<PERSON>\",\"Je<PERSON>\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>yrt\",\"<PERSON>rc<PERSON>\",\"<PERSON>rdein\",\"<PERSON><PERSON>ey\",\"<PERSON><PERSON>n\"],[\"<PERSON>\",\"<PERSON>l\",\"<PERSON>m\",\"Jerc\",\"Jerd\",\"Jeh\",\"Jes\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"<PERSON>-guer\",\"T-arree\",\"Mayrnt\",\"Avrril\",\"Boaldyn\",\"M-souree\",\"J-souree\",\"Luanistyn\",\"M-fouyir\",\"J-fouyir\",\"M-Houney\",\"M-Nollick\"],[\"Jerrey-geuree\",\"Toshiaght-arree\",\"Mayrnt\",\"Averil\",\"Boaldyn\",\"Mean-souree\",\"Jerrey-souree\",\"Luanistyn\",\"Mean-fouyir\",\"Jerrey-fouyir\",\"Mee Houney\",\"Mee ny Nollick\"]],u,[[\"RC\",\"AD\"],u,u],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"GBP\",\"£\",\"GBP\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}