{"version": 3, "file": "ha.js", "sourceRoot": "", "sources": ["ha.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,WAAW,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,sBAAsB,EAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,cAAc,EAAC,SAAS,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,iBAAiB,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ha\",[[\"SF\",\"YM\"],u,[\"<PERSON><PERSON><PERSON>\",\"Yamma\"]],[[\"SF\",\"YM\"],u,u],[[\"L\",\"L\",\"T\",\"L\",\"A\",\"J\",\"A\"],[\"Lah\",\"Lit\",\"Tal\",\"Lar\",\"<PERSON>h\",\"Ju<PERSON>\",\"<PERSON>a\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>a\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>ʼa\",\"As<PERSON>\"],[\"Lh\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>r\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"M\",\"A\",\"M\",\"Y\",\"Y\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"<PERSON>\",\"Fab\",\"<PERSON>\",\"<PERSON>fi\",\"May\",\"<PERSON>\",\"<PERSON>l\",\"<PERSON>gu\",\"<PERSON>t\",\"<PERSON>t\",\"Nuw\",\"<PERSON>s\"],[\"<PERSON>iru\",\"<PERSON>ab<PERSON>ru\",\"<PERSON>\",\"<PERSON>firilu\",\"<PERSON>u\",\"<PERSON>i\",\"<PERSON>li\",\"<PERSON><PERSON>ta\",\"<PERSON><PERSON>ba\",\"<PERSON>toba\",\"Nuwa<PERSON>\",\"<PERSON><PERSON><PERSON>\"]],u,[[\"<PERSON>.<PERSON>\",\"BHAI\"],u,[\"Kafin haihuwar annab\",\"Bayan haihuwar annab\"]],1,[6,0],[\"d/M/yy\",\"d MMM, y\",\"d MMMM, y\",\"EEEE d MMMM, y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,\"{1} 'da' {0}\",\"{1} {0}\"],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"NGN\",\"₦\",\"Nairar Najeriya\",{\"NGN\":[\"₦\"]},\"ltr\", plural];\n"]}