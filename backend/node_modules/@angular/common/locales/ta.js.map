{"version": 3, "file": "ta.js", "sourceRoot": "", "sources": ["ta.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,EAAC,SAAS,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,qBAAqB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,eAAe,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,eAAe,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ta\",[[\"மு.ப\",\"பி.ப\"],[\"முற்பகல்\",\"பிற்பகல்\"],u],u,[[\"ஞா\",\"தி\",\"செ\",\"பு\",\"வி\",\"வெ\",\"ச\"],[\"ஞாயி.\",\"திங்.\",\"செவ்.\",\"புத.\",\"வியா.\",\"வெள்.\",\"சனி\"],[\"ஞாயிறு\",\"திங்கள்\",\"செவ்வாய்\",\"புதன்\",\"வியாழன்\",\"வெள்ளி\",\"சனி\"],[\"ஞா\",\"தி\",\"செ\",\"பு\",\"வி\",\"வெ\",\"ச\"]],u,[[\"ஜ\",\"பி\",\"மா\",\"ஏ\",\"மே\",\"ஜூ\",\"ஜூ\",\"ஆ\",\"செ\",\"அ\",\"ந\",\"டி\"],[\"ஜன.\",\"பிப்.\",\"மார்.\",\"ஏப்.\",\"மே\",\"ஜூன்\",\"ஜூலை\",\"ஆக.\",\"செப்.\",\"அக்.\",\"நவ.\",\"டிச.\"],[\"ஜனவரி\",\"பிப்ரவரி\",\"மார்ச்\",\"ஏப்ரல்\",\"மே\",\"ஜூன்\",\"ஜூலை\",\"ஆகஸ்ட்\",\"செப்டம்பர்\",\"அக்டோபர்\",\"நவம்பர்\",\"டிசம்பர்\"]],u,[[\"கி.மு.\",\"கி.பி.\"],u,[\"கிறிஸ்துவுக்கு முன்\",\"அன்னோ டோமினி\"]],0,[0,0],[\"d/M/yy\",\"d MMM, y\",\"d MMMM, y\",\"EEEE, d MMMM, y\"],[\"a h:mm\",\"a h:mm:ss\",\"a h:mm:ss z\",\"a h:mm:ss zzzz\"],[\"{1}, {0}\",u,\"{1} அன்று {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤ #,##,##0.00\",\"#E0\"],\"INR\",\"₹\",\"இந்திய ரூபாய்\",{\"BYN\":[u,\"р.\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}