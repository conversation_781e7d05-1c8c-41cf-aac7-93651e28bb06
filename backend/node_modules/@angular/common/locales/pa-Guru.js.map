{"version": 3, "file": "pa-Guru.js", "sourceRoot": "", "sources": ["pa-Guru.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,SAAS,EAAC,CAAC,CAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,WAAW,EAAC,YAAY,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,eAAe,EAAC,OAAO,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === Math.floor(n) && (n >= 0 && n <= 1))\n    return 1;\nreturn 5;\n}\n\nexport default [\"pa-Guru\",[[\"ਸ.\",\"ਸ਼.\"],[\"ਪੂ.ਦੁ.\",\"ਬਾ.ਦੁ.\"],u],[[\"ਪੂ.ਦੁ.\",\"ਬਾ.ਦੁ.\"],u,u],[[\"ਐ\",\"ਸੋ\",\"ਮੰ\",\"ਬੁੱ\",\"ਵੀ\",\"ਸ਼ੁੱ\",\"ਸ਼\"],[\"ਐਤ\",\"ਸੋਮ\",\"ਮੰਗਲ\",\"ਬੁੱਧ\",\"ਵੀਰ\",\"ਸ਼ੁੱਕਰ\",\"ਸ਼ਨਿੱਚਰ\"],[\"ਐਤਵਾਰ\",\"ਸੋਮਵਾਰ\",\"ਮੰਗਲਵਾਰ\",\"ਬੁੱਧਵਾਰ\",\"ਵੀਰਵਾਰ\",\"ਸ਼ੁੱਕਰਵਾਰ\",\"ਸ਼ਨਿੱਚਰਵਾਰ\"],[\"ਐਤ\",\"ਸੋਮ\",\"ਮੰਗ\",\"ਬੁੱਧ\",\"ਵੀਰ\",\"ਸ਼ੁੱਕ\",\"ਸ਼ਨਿੱ\"]],u,[[\"ਜ\",\"ਫ਼\",\"ਮਾ\",\"ਅ\",\"ਮ\",\"ਜੂ\",\"ਜੁ\",\"ਅ\",\"ਸ\",\"ਅ\",\"ਨ\",\"ਦ\"],[\"ਜਨ\",\"ਫ਼ਰ\",\"ਮਾਰਚ\",\"ਅਪ੍ਰੈ\",\"ਮਈ\",\"ਜੂਨ\",\"ਜੁਲਾ\",\"ਅਗ\",\"ਸਤੰ\",\"ਅਕਤੂ\",\"ਨਵੰ\",\"ਦਸੰ\"],[\"ਜਨਵਰੀ\",\"ਫ਼ਰਵਰੀ\",\"ਮਾਰਚ\",\"ਅਪ੍ਰੈਲ\",\"ਮਈ\",\"ਜੂਨ\",\"ਜੁਲਾਈ\",\"ਅਗਸਤ\",\"ਸਤੰਬਰ\",\"ਅਕਤੂਬਰ\",\"ਨਵੰਬਰ\",\"ਦਸੰਬਰ\"]],u,[[\"ਈ.ਪੂ.\",\"ਸੰਨ\"],[\"ਈ. ਪੂ.\",\"ਸੰਨ\"],[\"ਈਸਵੀ ਪੂਰਵ\",\"ਈਸਵੀ ਸੰਨ\"]],0,[0,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤ #,##,##0.00\",\"[#E0]\"],\"INR\",\"₹\",\"ਭਾਰਤੀ ਰੁਪਇਆ\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"],\"XXX\":[]},\"ltr\", plural];\n"]}