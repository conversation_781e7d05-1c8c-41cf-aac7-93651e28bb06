{"version": 3, "file": "ml.js", "sourceRoot": "", "sources": ["ml.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,aAAa,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,cAAc,EAAC,WAAW,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,EAAC,YAAY,EAAC,cAAc,EAAC,WAAW,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,IAAI,EAAC,UAAU,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,UAAU,EAAC,YAAY,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,sBAAsB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ml\",[[\"AM\",\"PM\"],u,u],u,[[\"ഞ\",\"തി\",\"ചൊ\",\"ബു\",\"വ്യാ\",\"വെ\",\"ശ\"],[\"ഞായർ\",\"തിങ്കൾ\",\"ചൊവ്വ\",\"ബുധൻ\",\"വ്യാഴം\",\"വെള്ളി\",\"ശനി\"],[\"ഞായറാഴ്‌ച\",\"തിങ്കളാഴ്‌ച\",\"ചൊവ്വാഴ്ച\",\"ബുധനാഴ്‌ച\",\"വ്യാഴാഴ്‌ച\",\"വെള്ളിയാഴ്‌ച\",\"ശനിയാഴ്‌ച\"],[\"ഞാ\",\"തി\",\"ചൊ\",\"ബു\",\"വ്യാ\",\"വെ\",\"ശ\"]],[[\"ഞാ\",\"തി\",\"ചൊ\",\"ബു\",\"വ്യാ\",\"വെ\",\"ശ\"],[\"ഞായർ\",\"തിങ്കൾ\",\"ചൊവ്വ\",\"ബുധൻ\",\"വ്യാഴം\",\"വെള്ളി\",\"ശനി\"],[\"ഞായറാഴ്‌ച\",\"തിങ്കളാഴ്‌ച\",\"ചൊവ്വാഴ്‌ച\",\"ബുധനാഴ്‌ച\",\"വ്യാഴാഴ്‌ച\",\"വെള്ളിയാഴ്‌ച\",\"ശനിയാഴ്‌ച\"],[\"ഞാ\",\"തി\",\"ചൊ\",\"ബു\",\"വ്യാ\",\"വെ\",\"ശ\"]],[[\"ജ\",\"ഫെ\",\"മാ\",\"ഏ\",\"മെ\",\"ജൂൺ\",\"ജൂ\",\"ഓ\",\"സെ\",\"ഒ\",\"ന\",\"ഡി\"],[\"ജനു\",\"ഫെബ്രു\",\"മാർ\",\"ഏപ്രി\",\"മേയ്\",\"ജൂൺ\",\"ജൂലൈ\",\"ഓഗ\",\"സെപ്റ്റം\",\"ഒക്ടോ\",\"നവം\",\"ഡിസം\"],[\"ജനുവരി\",\"ഫെബ്രുവരി\",\"മാർച്ച്\",\"ഏപ്രിൽ\",\"മേയ്\",\"ജൂൺ\",\"ജൂലൈ\",\"ഓഗസ്റ്റ്\",\"സെപ്റ്റംബർ\",\"ഒക്‌ടോബർ\",\"നവംബർ\",\"ഡിസംബർ\"]],u,[[\"ക്രി.മു.\",\"എഡി\"],u,[\"ക്രിസ്‌തുവിന് മുമ്പ്\",\"ആന്നോ ഡൊമിനി\"]],0,[0,0],[\"d/M/yy\",\"y, MMM d\",\"y, MMMM d\",\"y, MMMM d, EEEE\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"INR\",\"₹\",\"ഇന്ത്യൻ രൂപ\",{\"BYN\":[u,\"р.\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}