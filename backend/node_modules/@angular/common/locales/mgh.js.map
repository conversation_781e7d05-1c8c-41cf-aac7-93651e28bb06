{"version": 3, "file": "mgh.js", "sourceRoot": "", "sources": ["mgh.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,UAAU,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,QAAQ,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,iBAAiB,EAAC,kBAAkB,EAAC,kBAAkB,EAAC,oBAAoB,EAAC,mBAAmB,EAAC,yBAAyB,EAAC,eAAe,EAAC,eAAe,EAAC,eAAe,EAAC,eAAe,EAAC,uBAAuB,EAAC,yBAAyB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"mgh\",[[\"wichishu\",\"mchochil’l\"],u,u],u,[[\"S\",\"J\",\"J\",\"J\",\"A\",\"I\",\"J\"],[\"Sab\",\"Jtt\",\"Jnn\",\"Jtn\",\"Ara\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON>ju<PERSON><PERSON>\",\"Ju<PERSON><PERSON><PERSON>\"],[\"Sab\",\"Jtt\",\"Jnn\",\"Jtn\",\"Ara\",\"Iju\",\"J<PERSON>\"]],u,[[\"K\",\"U\",\"R\",\"<PERSON>\",\"T\",\"M\",\"S\",\"<PERSON>\",\"T\",\"<PERSON>\",\"<PERSON>\",\"Y\"],[\"<PERSON>wa\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>ha\",\"<PERSON><PERSON>\",\"<PERSON>b\",\"<PERSON>\",\"Tis\",\"Kum\",\"Moj\",\"Yel\"],[\"Mweri wo kwanza\",\"Mweri wo unayeli\",\"Mweri wo uneraru\",\"Mweri wo unecheshe\",\"Mweri wo unethanu\",\"Mweri wo thanu na mocha\",\"Mweri wo saba\",\"Mweri wo nane\",\"Mweri wo tisa\",\"Mweri wo kumi\",\"Mweri wo kumi na moja\",\"Mweri wo kumi na yel’li\"]],u,[[\"HY\",\"YY\"],u,[\"Hinapiya yesu\",\"Yopia yesu\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"MZN\",\"MTn\",\"MZN\",{\"JPY\":[\"JP¥\",\"¥\"],\"MZN\":[\"MTn\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}