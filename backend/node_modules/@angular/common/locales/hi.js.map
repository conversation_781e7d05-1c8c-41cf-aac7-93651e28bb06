{"version": 3, "file": "hi.js", "sourceRoot": "", "sources": ["hi.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,YAAY,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,cAAc,EAAC,OAAO,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"hi\",[[\"am\",\"pm\"],u,u],u,[[\"र\",\"सो\",\"मं\",\"बु\",\"गु\",\"शु\",\"श\"],[\"रवि\",\"सोम\",\"मंगल\",\"बुध\",\"गुरु\",\"शुक्र\",\"शनि\"],[\"रविवार\",\"सोमवार\",\"मंगलवार\",\"बुधवार\",\"गुरुवार\",\"शुक्रवार\",\"शनिवार\"],[\"र\",\"सो\",\"मं\",\"बु\",\"गु\",\"शु\",\"श\"]],u,[[\"ज\",\"फ़\",\"मा\",\"अ\",\"म\",\"जू\",\"जु\",\"अ\",\"सि\",\"अ\",\"न\",\"दि\"],[\"जन॰\",\"फ़र॰\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुल॰\",\"अग॰\",\"सित॰\",\"अक्तू॰\",\"नव॰\",\"दिस॰\"],[\"जनवरी\",\"फ़रवरी\",\"मार्च\",\"अप्रैल\",\"मई\",\"जून\",\"जुलाई\",\"अगस्त\",\"सितंबर\",\"अक्तूबर\",\"नवंबर\",\"दिसंबर\"]],u,[[\"ईसा-पूर्व\",\"ईस्वी\"],u,[\"ईसा-पूर्व\",\"ईसवी सन\"]],0,[0,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} को {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤#,##,##0.00\",\"[#E0]\"],\"INR\",\"₹\",\"भारतीय रुपया\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"RON\":[u,\"लेई\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}