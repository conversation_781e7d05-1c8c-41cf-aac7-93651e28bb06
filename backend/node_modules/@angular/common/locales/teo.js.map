{"version": 3, "file": "teo.js", "sourceRoot": "", "sources": ["teo.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,WAAW,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,aAAa,EAAC,SAAS,EAAC,SAAS,EAAC,YAAY,EAAC,UAAU,EAAC,YAAY,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,OAAO,EAAC,MAAM,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,gBAAgB,EAAC,OAAO,EAAC,QAAQ,EAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,uBAAuB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"teo\",[[\"Taparachu\",\"Ebongi\"],u,u],u,[[\"J\",\"B\",\"A\",\"U\",\"U\",\"K\",\"S\"],[\"Jum\",\"Bar\",\"Aar\",\"Uni\",\"Ung\",\"Kan\",\"Sab\"],[\"Naka<PERSON>uma\",\"<PERSON><PERSON>eb<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>’on\",\"Nakakany\",\"<PERSON><PERSON><PERSON><PERSON>\"],[\"Ju<PERSON>\",\"<PERSON>\",\"A<PERSON>\",\"Uni\",\"Ung\",\"Kan\",\"Sab\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"T\",\"L\",\"<PERSON>\"],[\"Rar\",\"Mu<PERSON>\",\"<PERSON>wa\",\"Dun\",\"<PERSON>\",\"<PERSON>d\",\"<PERSON>l\",\"<PERSON>ed\",\"<PERSON>k\",\"Tib\",\"Lab\",\"<PERSON>o\"],[\"Orara\",\"Omuk\",\"<PERSON>wamg’\",\"<PERSON>dung’el\",\"<PERSON><PERSON>\",\"<PERSON>modok’king’ol\",\"<PERSON>jola\",\"<PERSON><PERSON>l\",\"<PERSON>so<PERSON><PERSON>ma\",\"<PERSON>tibar\",\"<PERSON>labor\",\"<PERSON>oo\"]],u,[[\"<PERSON><PERSON>\",\"BK\"],u,[\"Kabla ya Christo\",\"Baada ya Christo\"]],1,[0,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"UGX\",\"USh\",\"Ango’otol lok’ Uganda\",{\"JPY\":[\"JP¥\",\"¥\"],\"UGX\":[\"USh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}