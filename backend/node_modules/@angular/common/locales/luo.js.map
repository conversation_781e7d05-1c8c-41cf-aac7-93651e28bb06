{"version": 3, "file": "luo.js", "sourceRoot": "", "sources": ["luo.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,WAAW,EAAC,YAAY,EAAC,WAAW,EAAC,cAAc,EAAC,YAAY,EAAC,OAAO,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,gBAAgB,EAAC,eAAe,EAAC,cAAc,EAAC,iBAAiB,EAAC,eAAe,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,eAAe,EAAC,gBAAgB,EAAC,cAAc,EAAC,mBAAmB,EAAC,uBAAuB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,kBAAkB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"luo\",[[\"OD\",\"OT\"],u,u],u,[[\"J\",\"W\",\"T\",\"T\",\"T\",\"T\",\"N\"],[\"JMP\",\"WUT\",\"TAR\",\"TAD\",\"TAN\",\"TAB\",\"NGS\"],[\"Ju<PERSON>pi<PERSON>\",\"Wuok Tich\",\"Tich Ariyo\",\"Tich Adek\",\"Tich Ang’wen\",\"Tich Abich\",\"Ngeso\"],[\"JMP\",\"WUT\",\"TAR\",\"TAD\",\"TAN\",\"TAB\",\"NGS\"]],u,[[\"C\",\"R\",\"D\",\"N\",\"B\",\"U\",\"B\",\"B\",\"C\",\"P\",\"C\",\"P\"],[\"DAC\",\"DAR\",\"DAD\",\"DAN\",\"DAH\",\"DAU\",\"DAO\",\"DAB\",\"DOC\",\"DAP\",\"DGI\",\"DAG\"],[\"Dwe mar Achiel\",\"Dwe mar Ariyo\",\"Dwe mar Adek\",\"Dwe mar Ang’wen\",\"Dwe mar Abich\",\"Dwe mar Auchiel\",\"Dwe mar Abiriyo\",\"Dwe mar Aboro\",\"Dwe mar Ochiko\",\"Dwe mar Apar\",\"Dwe mar gi achiel\",\"Dwe mar Apar gi ariyo\"]],u,[[\"BC\",\"AD\"],u,[\"Kapok Kristo obiro\",\"Ka Kristo osebiro\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00¤\",\"#E0\"],\"KES\",\"Ksh\",\"Siling mar Kenya\",{\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}