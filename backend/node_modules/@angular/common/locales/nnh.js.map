{"version": 3, "file": "nnh.js", "sourceRoot": "", "sources": ["nnh.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,WAAW,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,eAAe,EAAC,YAAY,EAAC,qBAAqB,EAAC,gBAAgB,EAAC,yBAAyB,EAAC,iBAAiB,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,kBAAkB,EAAC,eAAe,EAAC,gBAAgB,EAAC,SAAS,EAAC,eAAe,EAAC,aAAa,EAAC,uBAAuB,EAAC,WAAW,EAAC,iBAAiB,EAAC,kBAAkB,EAAC,aAAa,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,wBAAwB,EAAC,+BAA+B,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,WAAW,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"nnh\",[[\"mbaʼámbaʼ\",\"ncwònzém\"],u,u],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"lyɛʼɛ́ sẅíŋtè\",\"mvfò lyɛ̌ʼ\",\"mbɔ́ɔntè mvfò lyɛ̌ʼ\",\"tsètsɛ̀ɛ lyɛ̌ʼ\",\"mbɔ́ɔntè tsetsɛ̀ɛ lyɛ̌ʼ\",\"mvfò màga lyɛ̌ʼ\",\"màga lyɛ̌ʼ\"],u,u],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"saŋ tsetsɛ̀ɛ lùm\",\"saŋ kàg ngwóŋ\",\"saŋ lepyè shúm\",\"saŋ cÿó\",\"saŋ tsɛ̀ɛ cÿó\",\"saŋ njÿoláʼ\",\"saŋ tyɛ̀b tyɛ̀b mbʉ̀ŋ\",\"saŋ mbʉ̀ŋ\",\"saŋ ngwɔ̀ʼ mbÿɛ\",\"saŋ tàŋa tsetsáʼ\",\"saŋ mejwoŋó\",\"saŋ lùm\"],u],u,[[\"m.z.Y.\",\"m.g.n.Y.\"],u,[\"mé zyé Yěsô\",\"mé gÿo ńzyé Yěsô\"]],1,[6,0],[\"dd/MM/yy\",\"d MMM, y\",\"'lyɛ'̌ʼ d 'na' MMMM, y\",\"EEEE , 'lyɛ'̌ʼ d 'na' MMMM, y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,\"{1}, {0}\",\"{1},{0}\"],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"XAF\",\"FCFA\",\"feláŋ CFA\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}