{"version": 3, "file": "kn.js", "sourceRoot": "", "sources": ["kn.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,IAAI,EAAC,SAAS,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,UAAU,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,cAAc,EAAC,iBAAiB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"kn\",[[\"ಪೂ\",\"ಅ\"],[\"ಪೂರ್ವಾಹ್ನ\",\"ಅಪರಾಹ್ನ\"],u],[[\"ಪೂರ್ವಾಹ್ನ\",\"ಅಪರಾಹ್ನ\"],u,u],[[\"ಭಾ\",\"ಸೋ\",\"ಮಂ\",\"ಬು\",\"ಗು\",\"ಶು\",\"ಶ\"],[\"ಭಾನು\",\"ಸೋಮ\",\"ಮಂಗಳ\",\"ಬುಧ\",\"ಗುರು\",\"ಶುಕ್ರ\",\"ಶನಿ\"],[\"ಭಾನುವಾರ\",\"ಸೋಮವಾರ\",\"ಮಂಗಳವಾರ\",\"ಬುಧವಾರ\",\"ಗುರುವಾರ\",\"ಶುಕ್ರವಾರ\",\"ಶನಿವಾರ\"],[\"ಭಾನು\",\"ಸೋಮ\",\"ಮಂಗಳ\",\"ಬುಧ\",\"ಗುರು\",\"ಶುಕ್ರ\",\"ಶನಿ\"]],u,[[\"ಜ\",\"ಫೆ\",\"ಮಾ\",\"ಏ\",\"ಮೇ\",\"ಜೂ\",\"ಜು\",\"ಆ\",\"ಸೆ\",\"ಅ\",\"ನ\",\"ಡಿ\"],[\"ಜನವರಿ\",\"ಫೆಬ್ರವರಿ\",\"ಮಾರ್ಚ್\",\"ಏಪ್ರಿ\",\"ಮೇ\",\"ಜೂನ್\",\"ಜುಲೈ\",\"ಆಗ\",\"ಸೆಪ್ಟೆಂ\",\"ಅಕ್ಟೋ\",\"ನವೆಂ\",\"ಡಿಸೆಂ\"],[\"ಜನವರಿ\",\"ಫೆಬ್ರವರಿ\",\"ಮಾರ್ಚ್\",\"ಏಪ್ರಿಲ್\",\"ಮೇ\",\"ಜೂನ್\",\"ಜುಲೈ\",\"ಆಗಸ್ಟ್\",\"ಸೆಪ್ಟೆಂಬರ್\",\"ಅಕ್ಟೋಬರ್\",\"ನವೆಂಬರ್\",\"ಡಿಸೆಂಬರ್\"]],[[\"ಜ\",\"ಫೆ\",\"ಮಾ\",\"ಏ\",\"ಮೇ\",\"ಜೂ\",\"ಜು\",\"ಆ\",\"ಸೆ\",\"ಅ\",\"ನ\",\"ಡಿ\"],[\"ಜನ\",\"ಫೆಬ್ರ\",\"ಮಾರ್ಚ್\",\"ಏಪ್ರಿ\",\"ಮೇ\",\"ಜೂನ್\",\"ಜುಲೈ\",\"ಆಗ\",\"ಸೆಪ್ಟೆಂ\",\"ಅಕ್ಟೋ\",\"ನವೆಂ\",\"ಡಿಸೆಂ\"],[\"ಜನವರಿ\",\"ಫೆಬ್ರವರಿ\",\"ಮಾರ್ಚ್\",\"ಏಪ್ರಿಲ್\",\"ಮೇ\",\"ಜೂನ್\",\"ಜುಲೈ\",\"ಆಗಸ್ಟ್\",\"ಸೆಪ್ಟೆಂಬರ್\",\"ಅಕ್ಟೋಬರ್\",\"ನವೆಂಬರ್\",\"ಡಿಸೆಂಬರ್\"]],[[\"ಕ್ರಿ.ಪೂ\",\"ಕ್ರಿ.ಶ\"],u,[\"ಕ್ರಿಸ್ತ ಪೂರ್ವ\",\"ಕ್ರಿಸ್ತ ಶಕ\"]],0,[0,0],[\"d/M/yy\",\"MMM d, y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"hh:mm a\",\"hh:mm:ss a\",\"hh:mm:ss a z\",\"hh:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"INR\",\"₹\",\"ಭಾರತೀಯ ರೂಪಾಯಿ\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"RON\":[u,\"ಲೀ\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}