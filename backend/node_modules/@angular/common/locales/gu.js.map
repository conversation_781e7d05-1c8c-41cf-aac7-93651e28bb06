{"version": 3, "file": "gu.js", "sourceRoot": "", "sources": ["gu.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,WAAW,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,IAAI,CAAC,EAAC,CAAC,YAAY,EAAC,MAAM,CAAC,EAAC,CAAC,eAAe,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,cAAc,EAAC,iBAAiB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,kBAAkB,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,cAAc,EAAC,OAAO,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"gu\",[[\"AM\",\"PM\"],u,u],u,[[\"ર\",\"સો\",\"મં\",\"બુ\",\"ગુ\",\"શુ\",\"શ\"],[\"રવિ\",\"સોમ\",\"મંગળ\",\"બુધ\",\"ગુરુ\",\"શુક્ર\",\"શનિ\"],[\"રવિવાર\",\"સોમવાર\",\"મંગળવાર\",\"બુધવાર\",\"ગુરુવાર\",\"શુક્રવાર\",\"શનિવાર\"],[\"ર\",\"સો\",\"મં\",\"બુ\",\"ગુ\",\"શુ\",\"શ\"]],u,[[\"જા\",\"ફે\",\"મા\",\"એ\",\"મે\",\"જૂ\",\"જુ\",\"ઑ\",\"સ\",\"ઑ\",\"ન\",\"ડિ\"],[\"જાન્યુ\",\"ફેબ્રુ\",\"માર્ચ\",\"એપ્રિલ\",\"મે\",\"જૂન\",\"જુલાઈ\",\"ઑગસ્ટ\",\"સપ્ટે\",\"ઑક્ટો\",\"નવે\",\"ડિસે\"],[\"જાન્યુઆરી\",\"ફેબ્રુઆરી\",\"માર્ચ\",\"એપ્રિલ\",\"મે\",\"જૂન\",\"જુલાઈ\",\"ઑગસ્ટ\",\"સપ્ટેમ્બર\",\"ઑક્ટોબર\",\"નવેમ્બર\",\"ડિસેમ્બર\"]],u,[[\"ઇ સ પુ\",\"ઇસ\"],[\"ઈ.સ.પૂર્વે\",\"ઈ.સ.\"],[\"ઈસવીસન પૂર્વે\",\"ઇસવીસન\"]],0,[0,0],[\"d/M/yy\",\"d MMM, y\",\"d MMMM, y\",\"EEEE, d MMMM, y\"],[\"hh:mm a\",\"hh:mm:ss a\",\"hh:mm:ss a z\",\"hh:mm:ss a zzzz\"],[\"{1} {0}\",u,\"{1} એ {0} વાગ્યે\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤#,##,##0.00\",\"[#E0]\"],\"INR\",\"₹\",\"ભારતીય રૂપિયા\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"MUR\":[u,\"રૂ.\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}