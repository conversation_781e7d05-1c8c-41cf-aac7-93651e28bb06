{"version": 3, "file": "naq.js", "sourceRoot": "", "sources": ["naq.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,SAAS,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,cAAc,EAAC,gBAAgB,EAAC,cAAc,EAAC,eAAe,CAAC,EAAC,CAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,YAAY,EAAC,YAAY,EAAC,YAAY,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,gBAAgB,EAAC,kBAAkB,EAAC,YAAY,EAAC,WAAW,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,qBAAqB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nif (n === 2)\n    return 2;\nreturn 5;\n}\n\nexport default [\"naq\",[[\"ǁgoagas\",\"ǃuias\"],u,u],u,[[\"S\",\"M\",\"E\",\"W\",\"D\",\"F\",\"A\"],[\"Son\",\"Ma\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Sat\"],[\"Sontaxtsees\",\"Mantaxtsees\",\"Denstaxtsees\",\"Wunstaxtsees\",\"Dondertaxtsees\",\"Fraitaxtsees\",\"Satertaxtsees\"],[\"Son\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"Sa<PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"J\",\"J\",\"A\",\"<PERSON>\",\"O\",\"N\",\"D\"],[\"<PERSON>\",\"Feb\",\"<PERSON>\",\"<PERSON>\",\"May\",\"<PERSON>\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"],[\"ǃKhanni\",\"ǃKhanǀgôab\",\"ǀKhuuǁkhâb\",\"ǃHôaǂkhaib\",\"ǃKhaitsâb\",\"Gamaǀaeb\",\"ǂKhoesaob\",\"Aoǁkhuumûǁkhâb\",\"Taraǀkhuumûǁkhâb\",\"ǂNûǁnâiseb\",\"ǀHooǂgaeb\",\"Hôasoreǁkhâb\"]],u,[[\"<PERSON>\",\"AD\"],u,[\"Xristub aiǃâ\",\"Xristub khaoǃgâ\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"ZAR\",\"ZAR\",\"South African Randi\",{\"JPY\":[\"JP¥\",\"¥\"],\"NAD\":[\"$\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}