{"version": 3, "file": "mn.js", "sourceRoot": "", "sources": ["mn.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,KAAK,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,eAAe,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,eAAe,EAAC,kBAAkB,EAAC,iBAAiB,EAAC,gBAAgB,EAAC,cAAc,EAAC,gBAAgB,EAAC,qBAAqB,EAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,eAAe,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,iBAAiB,EAAC,eAAe,EAAC,kBAAkB,EAAC,iBAAiB,EAAC,gBAAgB,EAAC,cAAc,EAAC,gBAAgB,EAAC,qBAAqB,EAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,mBAAmB,EAAC,oBAAoB,EAAC,kCAAkC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,cAAc,EAAC,iBAAiB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"mn\",[[\"ү.ө.\",\"ү.х.\"],u,u],u,[[\"Ня\",\"Да\",\"Мя\",\"Лх\",\"Пү\",\"Ба\",\"Бя\"],u,[\"ням\",\"даваа\",\"мягмар\",\"лхагва\",\"пүрэв\",\"баасан\",\"бямба\"],[\"Ня\",\"Да\",\"Мя\",\"Лх\",\"Пү\",\"Ба\",\"Бя\"]],[[\"Ня\",\"Да\",\"Мя\",\"Лх\",\"Пү\",\"Ба\",\"Бя\"],u,[\"Ням\",\"Даваа\",\"<PERSON><PERSON>гм<PERSON>р\",\"Лхагва\",\"Пүрэв\",\"Баа<PERSON>ан\",\"Бямба\"],[\"Ня\",\"<PERSON>а\",\"Мя\",\"Лх\",\"Пү\",\"Ба\",\"Бя\"]],[[\"I\",\"II\",\"III\",\"IV\",\"V\",\"VI\",\"VII\",\"VIII\",\"IX\",\"X\",\"XI\",\"XII\"],[\"1-р сар\",\"2-р сар\",\"3-р сар\",\"4-р сар\",\"5-р сар\",\"6-р сар\",\"7-р сар\",\"8-р сар\",\"9-р сар\",\"10-р сар\",\"11-р сар\",\"12-р сар\"],[\"нэгдүгээр сар\",\"хоёрдугаар сар\",\"гуравдугаар сар\",\"дөрөвдүгээр сар\",\"тавдугаар сар\",\"зургаадугаар сар\",\"долоодугаар сар\",\"наймдугаар сар\",\"есдүгээр сар\",\"аравдугаар сар\",\"арван нэгдүгээр сар\",\"арван хоёрдугаар сар\"]],[[\"I\",\"II\",\"III\",\"IV\",\"V\",\"VI\",\"VII\",\"VIII\",\"IX\",\"X\",\"XI\",\"XII\"],[\"1-р сар\",\"2-р сар\",\"3-р сар\",\"4-р сар\",\"5-р сар\",\"6-р сар\",\"7-р сар\",\"8-р сар\",\"9-р сар\",\"10-р сар\",\"11-р сар\",\"12-р сар\"],[\"Нэгдүгээр сар\",\"Хоёрдугаар сар\",\"Гуравдугаар сар\",\"Дөрөвдүгээр сар\",\"Тавдугаар сар\",\"Зургаадугаар сар\",\"Долоодугаар сар\",\"Наймдугаар сар\",\"Есдүгээр сар\",\"Аравдугаар сар\",\"Арван нэгдүгээр сар\",\"Арван хоёрдугаар сар\"]],[[\"МЭӨ\",\"МЭ\"],u,[\"манай эриний өмнөх\",\"манай эриний\"]],1,[6,0],[\"y.MM.dd\",\"y 'оны' MMM'ын' d\",\"y 'оны' MMMM'ын' d\",\"y 'оны' MMMM'ын' d, EEEE 'гараг'\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss (z)\",\"HH:mm:ss (zzzz)\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"MNT\",\"₮\",\"Монгол төгрөг\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"MNT\":[\"₮\"],\"PHP\":[u,\"₱\"],\"SEK\":[u,\"кр\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}