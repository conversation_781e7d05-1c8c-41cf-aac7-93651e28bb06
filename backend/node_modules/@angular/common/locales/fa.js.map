{"version": 3, "file": "fa.js", "sourceRoot": "", "sources": ["fa.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,SAAS,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,IAAI,CAAC,EAAC,CAAC,cAAc,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,WAAW,EAAC,CAAC,EAAC,eAAe,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,OAAO,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,aAAa,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,YAAY,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,WAAW,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"fa\",[[\"ق\",\"ب\"],[\"ق.ظ.\",\"ب.ظ.\"],[\"قبل‌ازظهر\",\"بعدازظهر\"]],u,[[\"ی\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"],[\"یکشنبه\",\"دوشنبه\",\"سه‌شنبه\",\"چهارشنبه\",\"پنجشنبه\",\"جمعه\",\"شنبه\"],u,[\"۱ش\",\"۲ش\",\"۳ش\",\"۴ش\",\"۵ش\",\"ج\",\"ش\"]],u,[[\"ژ\",\"ف\",\"م\",\"آ\",\"م\",\"ژ\",\"ژ\",\"ا\",\"س\",\"ا\",\"ن\",\"د\"],[\"ژانویه\",\"فوریه\",\"مارس\",\"آوریل\",\"مه\",\"ژوئن\",\"ژوئیه\",\"اوت\",\"سپتامبر\",\"اکتبر\",\"نوامبر\",\"دسامبر\"],[\"ژانویهٔ\",\"فوریهٔ\",\"مارس\",\"آوریل\",\"مهٔ\",\"ژوئن\",\"ژوئیهٔ\",\"اوت\",\"سپتامبر\",\"اکتبر\",\"نوامبر\",\"دسامبر\"]],[[\"ژ\",\"ف\",\"م\",\"آ\",\"م\",\"ژ\",\"ژ\",\"ا\",\"س\",\"ا\",\"ن\",\"د\"],[\"ژانویه\",\"فوریه\",\"مارس\",\"آوریل\",\"مه\",\"ژوئن\",\"ژوئیه\",\"اوت\",\"سپتامبر\",\"اکتبر\",\"نوامبر\",\"دسامبر\"],u],[[\"ق\",\"م\"],[\"ق.م.\",\"م.\"],[\"قبل از میلاد\",\"میلادی\"]],6,[5,5],[\"y/M/d\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"H:mm\",\"H:mm:ss\",\"H:mm:ss (z)\",\"H:mm:ss (zzzz)\"],[\"{1}،‏ {0}\",u,\"{1}، ساعت {0}\",u],[\".\",\",\",\";\",\"%\",\"‎+\",\"‎−\",\"E\",\"×\",\"‰\",\"∞\",\"ناعدد\",\":\"],[\"#,##0.###\",\"#,##0%\",\"‎¤ #,##0.00\",\"#E0\"],\"IRR\",\"ریال\",\"ریال ایران\",{\"AFN\":[\"؋\"],\"BYN\":[u,\"р.\"],\"CAD\":[\"$CA\",\"$\"],\"CNY\":[\"¥CN\",\"¥\"],\"HKD\":[\"$HK\",\"$\"],\"IRR\":[\"ریال\"],\"MXN\":[\"$MX\",\"$\"],\"NZD\":[\"$NZ\",\"$\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"XCD\":[\"$EC\",\"$\"],\"XOF\":[\"فرانک CFA\"]},\"rtl\", plural];\n"]}