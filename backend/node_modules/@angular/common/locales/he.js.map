{"version": 3, "file": "he.js", "sourceRoot": "", "sources": ["he.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjD,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,aAAa,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,WAAW,EAAC,cAAc,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,0BAA0B,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,SAAS,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,OAAO,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n\nif (i === 1 && v === 0)\n    return 1;\nif (i === 2 && v === 0)\n    return 2;\nif (v === 0 && (!(n >= 0 && n <= 10) && n % 10 === 0))\n    return 4;\nreturn 5;\n}\n\nexport default [\"he\",[[\"לפנה״צ\",\"אחה״צ\"],u,u],[[\"לפנה״צ\",\"אחה״צ\"],[\"AM\",\"PM\"],u],[[\"א׳\",\"ב׳\",\"ג׳\",\"ד׳\",\"ה׳\",\"ו׳\",\"ש׳\"],[\"יום א׳\",\"יום ב׳\",\"יום ג׳\",\"יום ד׳\",\"יום ה׳\",\"יום ו׳\",\"שבת\"],[\"יום ראשון\",\"יום שני\",\"יום שלישי\",\"יום רביעי\",\"יום חמישי\",\"יום שישי\",\"יום שבת\"],[\"א׳\",\"ב׳\",\"ג׳\",\"ד׳\",\"ה׳\",\"ו׳\",\"ש׳\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"ינו׳\",\"פבר׳\",\"מרץ\",\"אפר׳\",\"מאי\",\"יוני\",\"יולי\",\"אוג׳\",\"ספט׳\",\"אוק׳\",\"נוב׳\",\"דצמ׳\"],[\"ינואר\",\"פברואר\",\"מרץ\",\"אפריל\",\"מאי\",\"יוני\",\"יולי\",\"אוגוסט\",\"ספטמבר\",\"אוקטובר\",\"נובמבר\",\"דצמבר\"]],u,[[\"לפני\",\"אחריי\"],[\"לפנה״ס\",\"לספירה\"],[\"לפני הספירה\",\"לספירה\"]],0,[5,6],[\"d.M.y\",\"d בMMM y\",\"d בMMMM y\",\"EEEE, d בMMMM y\"],[\"H:mm\",\"H:mm:ss\",\"H:mm:ss z\",\"H:mm:ss zzzz\"],[\"{1}, {0}\",u,\"{1} בשעה {0}\",u],[\".\",\",\",\";\",\"%\",\"‎+\",\"‎-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"‏#,##0.00 ¤;‏-#,##0.00 ¤\",\"#E0\"],\"ILS\",\"₪\",\"שקל חדש\",{\"BYN\":[u,\"р\"],\"CNY\":[\"‎CN¥‎\",\"¥\"],\"ILP\":[\"ל״י\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"rtl\", plural];\n"]}