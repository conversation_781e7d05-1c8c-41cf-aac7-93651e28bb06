{"version": 3, "file": "sc.js", "sourceRoot": "", "sources": ["sc.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,YAAY,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,UAAU,EAAC,SAAS,EAAC,OAAO,EAAC,WAAW,EAAC,YAAY,EAAC,YAAY,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,oBAAoB,EAAC,oBAAoB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,cAAc,EAAC,yBAAyB,EAAC,oCAAoC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,mBAAmB,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\\.?/, '').length;\n\nif (i === 1 && v === 0)\n    return 1;\nreturn 5;\n}\n\nexport default [\"sc\",[[\"m.\",\"b.\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"D\",\"L\",\"M\",\"M\",\"G\",\"C\",\"S\"],[\"dom\",\"lun\",\"mar\",\"mèr\",\"giò\",\"che\",\"sàb\"],[\"dom<PERSON><PERSON><PERSON>\",\"lunis\",\"martis\",\"m<PERSON><PERSON><PERSON>s\",\"gi<PERSON>bia\",\"chen<PERSON><PERSON>a\",\"sàbadu\"],[\"dom\",\"lun\",\"mar\",\"mèr\",\"giò\",\"che\",\"sàb\"]],u,[[\"G\",\"F\",\"M\",\"A\",\"M\",\"L\",\"T\",\"A\",\"C\",\"S\",\"S\",\"N\"],[\"ghe\",\"fre\",\"mar\",\"abr\",\"maj\",\"làm\",\"trì\",\"aus\",\"cab\",\"stG\",\"stA\",\"nad\"],[\"ghennàrgiu\",\"freàrgiu\",\"martzu\",\"abrile\",\"maju\",\"làmpadas\",\"trìulas\",\"austu\",\"cabudanni\",\"santugaine\",\"santandria\",\"nadale\"]],u,[[\"a.C.\",\"p.C.\"],u,[\"in antis de Cristu\",\"a pustis de Cristu\"]],1,[6,0],[\"dd/MM/y\",\"d 'de' MMM y\",\"d 'de' MMMM 'de' 'su' y\",\"d 'de' MMMM 'de' 'su' y, 'de' EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,\"{1} 'a' 'sas' {0}\",u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"èuro\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"],\"XDR\":[\"DIP\"]},\"ltr\", plural];\n"]}