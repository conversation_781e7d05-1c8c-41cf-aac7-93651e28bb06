{"version": 3, "file": "kln.js", "sourceRoot": "", "sources": ["kln.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,KAAK,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,YAAY,EAAC,UAAU,EAAC,WAAW,EAAC,QAAQ,EAAC,OAAO,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,EAAC,QAAQ,EAAC,mBAAmB,EAAC,sBAAsB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,mBAAmB,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,qBAAqB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"kln\",[[\"krn\",\"koosk\"],u,[\"karoon\",\"kooskoliny\"]],[[\"krn\",\"koosk\"],u,u],[[\"T\",\"T\",\"O\",\"S\",\"A\",\"M\",\"L\"],[\"Kts\",\"Kot\",\"Koo\",\"Ko<PERSON>\",\"Koa\",\"Kom\",\"Kol\"],[\"<PERSON><PERSON><PERSON>\",\"Kota<PERSON>\",\"Koaeng’\",\"Kosomok\",\"Koang’wan\",\"<PERSON>muut\",\"Ko<PERSON>\"],[\"Kts\",\"Ko<PERSON>\",\"Ko<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"Kol\"]],u,[[\"<PERSON>\",\"N\",\"T\",\"I\",\"M\",\"P\",\"N\",\"R\",\"B\",\"E\",\"K\",\"K\"],[\"Mul\",\"Ngat\",\"Taa\",\"Iwo\",\"Mam\",\"Paa\",\"Nge\",\"Roo\",\"<PERSON>ur\",\"<PERSON>pe\",\"Kpt\",\"Kpa\"],[\"Mulgul\",\"Ng’atyaato\",\"Kiptaamo\",\"Iwootkuut\",\"Mamuut\",\"Paagi\",\"Ng’eiyeet\",\"Rooptui\",\"Bureet\",\"<PERSON>peeso\",\"Kipsuunde ne taai\",\"Kipsuunde nebo aeng’\"]],u,[[\"AM\",\"KO\"],u,[\"Amait kesich Jesu\",\"Kokakesich Jesu\"]],0,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"KES\",\"Ksh\",\"Silingitab ya Kenya\",{\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}