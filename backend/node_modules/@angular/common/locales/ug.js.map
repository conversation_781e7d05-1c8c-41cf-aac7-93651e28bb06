{"version": 3, "file": "ug.js", "sourceRoot": "", "sources": ["ug.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,cAAc,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,QAAQ,EAAC,KAAK,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,EAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,mBAAmB,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,SAAS,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,aAAa,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ug\",[[\"ب\",\"ك\"],[\"چ.ب\",\"چ.ك\"],[\"چۈشتىن بۇرۇن\",\"چۈشتىن كېيىن\"]],[[\"چ.ب\",\"چ.ك\"],u,u],[[\"ي\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"],[\"يە\",\"دۈ\",\"سە\",\"چا\",\"پە\",\"جۈ\",\"شە\"],[\"يەكشەنبە\",\"دۈشەنبە\",\"سەيشەنبە\",\"چارشەنبە\",\"پەيشەنبە\",\"جۈمە\",\"شەنبە\"],[\"ي\",\"د\",\"س\",\"چ\",\"پ\",\"ج\",\"ش\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"يانۋار\",\"فېۋرال\",\"مارت\",\"ئاپرېل\",\"ماي\",\"ئىيۇن\",\"ئىيۇل\",\"ئاۋغۇست\",\"سېنتەبىر\",\"ئۆكتەبىر\",\"نويابىر\",\"دېكابىر\"],u],u,[[\"BCE\",\"مىلادىيە\"],u,[\"مىلادىيەدىن بۇرۇن\",\"مىلادىيە\"]],0,[6,0],[\"y-MM-dd\",\"d-MMM، y\",\"d-MMMM، y\",\"y d-MMMM، EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}، {0}\",u,\"{1} {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"CNY\",\"￥\",\"جۇڭگو يۈەنى\",{\"CNY\":[\"￥\",\"¥\"],\"JPY\":[\"JP¥\",\"¥\"]},\"rtl\", plural];\n"]}