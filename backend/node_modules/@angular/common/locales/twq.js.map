{"version": 3, "file": "twq.js", "sourceRoot": "", "sources": ["twq.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,UAAU,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,WAAW,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,OAAO,EAAC,kBAAkB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"twq\",[[\"<PERSON>ba<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON> b\"],u,u],u,[[\"H\",\"T\",\"T\",\"L\",\"L\",\"L\",\"S\"],[\"Alh\",\"Ati\",\"Ata\",\"Ala\",\"Alm\",\"<PERSON>z\",\"<PERSON>i\"],[\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"],[\"Alh\",\"Ati\",\"Ata\",\"Ala\",\"Alm\",\"<PERSON><PERSON>\",\"<PERSON>i\"]],u,[[\"<PERSON>\",\"F\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"O\",\"N\",\"<PERSON>\"],[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>u<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>t\",\"<PERSON>k\",\"<PERSON>t\",\"<PERSON>o\",\"<PERSON>\"],[\"<PERSON>anwiye\",\"<PERSON>ewiriye\",\"<PERSON>i\",\"Awiril\",\"Me\",\"Žuweŋ\",\"Žuyye\",\"Ut\",\"<PERSON>ktanbur\",\"<PERSON>toobur\",\"Noowanbur\",\"<PERSON>sanbur\"]],u,[[\"I<PERSON>\",\"I<PERSON>\"],u,[\"<PERSON>a jine\",\"<PERSON>a zamanoo\"]],1,[6,0],[\"d/M/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00¤\",\"#E0\"],\"XOF\",\"F CFA\",\"CFA Fraŋ (BCEAO)\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}