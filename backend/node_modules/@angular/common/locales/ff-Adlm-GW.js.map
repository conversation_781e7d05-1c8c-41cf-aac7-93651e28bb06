{"version": 3, "file": "ff-Adlm-GW.js", "sourceRoot": "", "sources": ["ff-Adlm-GW.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,YAAY,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,MAAM,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,kBAAkB,EAAC,kBAAkB,EAAC,kBAAkB,EAAC,oBAAoB,EAAC,oBAAoB,EAAC,cAAc,EAAC,kBAAkB,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,YAAY,EAAC,YAAY,EAAC,cAAc,EAAC,YAAY,EAAC,YAAY,EAAC,YAAY,EAAC,YAAY,EAAC,UAAU,EAAC,YAAY,EAAC,YAAY,EAAC,UAAU,EAAC,YAAY,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,UAAU,EAAC,QAAQ,EAAC,YAAY,EAAC,UAAU,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,YAAY,EAAC,YAAY,EAAC,cAAc,EAAC,YAAY,EAAC,YAAY,EAAC,YAAY,EAAC,YAAY,EAAC,UAAU,EAAC,YAAY,EAAC,YAAY,EAAC,UAAU,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,gCAAgC,EAAC,oCAAoC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,WAAW,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,YAAY,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,QAAQ,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,QAAQ,EAAC,qDAAqD,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,MAAM,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,QAAQ,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,QAAQ,CAAC,EAAC,KAAK,EAAC,CAAC,QAAQ,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,UAAU,CAAC,EAAC,KAAK,EAAC,CAAC,QAAQ,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"ff-Adlm-GW\",[[\"𞤢\",\"𞤩\"],[\"𞤀𞤎\",\"𞤇𞤎\"],u],[[\"𞤀𞤎\",\"𞤇𞤎\"],u,u],[[\"𞤈\",\"𞤀𞥄\",\"𞤃\",\"𞤔\",\"𞤐\",\"𞤃\",\"𞤖\"],[\"𞤈𞤫𞤬\",\"𞤀𞥄𞤩𞤵\",\"𞤃𞤢𞤦\",\"𞤔𞤫𞤧\",\"𞤐𞤢𞥄𞤧\",\"𞤃𞤢𞤣\",\"𞤖𞤮𞤪\"],[\"𞤈𞤫𞤬𞤦𞤭𞤪𞥆𞤫\",\"𞤀𞥄𞤩𞤵𞤲𞥋𞤣𞤫\",\"𞤃𞤢𞤱𞤦𞤢𞥄𞤪𞤫\",\"𞤐𞤶𞤫𞤧𞤤𞤢𞥄𞤪𞤫\",\"𞤐𞤢𞥄𞤧𞤢𞥄𞤲𞤣𞤫\",\"𞤃𞤢𞤱𞤲𞤣𞤫\",\"𞤖𞤮𞤪𞤦𞤭𞤪𞥆𞤫\"],[\"𞤈𞤫𞤬\",\"𞤀𞥄𞤩𞤵\",\"𞤃𞤢𞤦\",\"𞤔𞤫𞤧\",\"𞤐𞤢𞥄𞤧\",\"𞤃𞤢𞤣\",\"𞤖𞤮𞤪\"]],u,[[\"𞤅\",\"𞤕\",\"𞤄\",\"𞤅\",\"𞤁\",\"𞤑\",\"𞤃\",\"𞤔\",\"𞤅\",\"𞤒\",\"𞤔\",\"𞤄\"],[\"𞤅𞤭𞥅𞤤𞤮\",\"𞤕𞤮𞤤𞤼𞤮\",\"𞤐𞤦𞤮𞥅𞤴𞤮\",\"𞤅𞤫𞥅𞤼𞤮\",\"𞤁𞤵𞥅𞤶𞤮\",\"𞤑𞤮𞤪𞤧𞤮\",\"𞤃𞤮𞤪𞤧𞤮\",\"𞤔𞤵𞤳𞤮\",\"𞤅𞤭𞤤𞤼𞤮\",\"𞤒𞤢𞤪𞤳𞤮\",\"𞤔𞤮𞤤𞤮\",\"𞤄𞤮𞤱𞤼𞤮\"],u],[[\"𞤅\",\"𞤕\",\"𞤄\",\"𞤅\",\"𞤁\",\"𞤑\",\"𞤃\",\"𞤔\",\"𞤅\",\"𞤒\",\"𞤔\",\"𞤄\"],[\"𞤅𞤭𞥅𞤤\",\"𞤕𞤮𞤤\",\"𞤐𞤦𞤮𞥅𞤴\",\"𞤅𞤫𞥅𞤼\",\"𞤁𞤵𞥅𞤶\",\"𞤑𞤮𞤪\",\"𞤃𞤮𞤪\",\"𞤔𞤵𞤳\",\"𞤅𞤭𞤤\",\"𞤒𞤢𞤪\",\"𞤔𞤮𞤤\",\"𞤄𞤮𞤱\"],[\"𞤅𞤭𞥅𞤤𞤮\",\"𞤕𞤮𞤤𞤼𞤮\",\"𞤐𞤦𞤮𞥅𞤴𞤮\",\"𞤅𞤫𞥅𞤼𞤮\",\"𞤁𞤵𞥅𞤶𞤮\",\"𞤑𞤮𞤪𞤧𞤮\",\"𞤃𞤮𞤪𞤧𞤮\",\"𞤔𞤵𞤳𞤮\",\"𞤅𞤭𞤤𞤼𞤮\",\"𞤒𞤢𞤪𞤳𞤮\",\"𞤔𞤮𞤤𞤮\",\"𞤄𞤮𞤱𞤼𞤮\"]],[[\"𞤀𞤀𞤋\",\"𞤇𞤀𞤋\"],u,[\"𞤀𞤣𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄\",\"𞤇𞤢𞥄𞤱𞤮 𞤀𞤲𞥆𞤢𞤦𞤭 𞤋𞥅𞤧𞤢𞥄\"]],1,[6,0],[\"d-M-y\",\"d MMM⹁ y\",\"d MMMM⹁ y\",\"EEEE d MMMM⹁ y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,\"{1} 𞤉 {0}\",u],[\".\",\"⹁\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"𞤏𞤮𞤈\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"XOF\",\"𞤅𞤊𞤀\",\"𞤊𞤢𞤪𞤢𞤲 𞤅𞤊𞤀 𞤖𞤭𞥅𞤪𞤲𞤢𞥄𞤲𞤺𞤫 𞤀𞤬𞤪𞤭𞤳𞤢\",{\"BYN\":[u,\"р.\"],\"GNF\":[u,\"𞤊𞤘\"],\"JPY\":[\"JP¥\",\"¥\"],\"NGN\":[\"𞤐𞤐𞤘\",\"₦\"],\"PGK\":[\"𞤑𞤆𞤘\"],\"PHP\":[\"𞤆𞤆𞤖\",\"₱\"],\"USD\":[\"US$\",\"$\"],\"XAF\":[\"𞤊𞤅𞤊𞤀\"],\"XOF\":[\"𞤅𞤊𞤀\"]},\"rtl\", plural];\n"]}