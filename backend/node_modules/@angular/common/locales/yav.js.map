{"version": 3, "file": "yav.js", "sourceRoot": "", "sources": ["yav.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,SAAS,EAAC,QAAQ,EAAC,eAAe,EAAC,YAAY,EAAC,kBAAkB,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,4BAA4B,EAAC,uBAAuB,EAAC,0BAA0B,EAAC,qBAAqB,EAAC,uBAAuB,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,aAAa,EAAC,2BAA2B,EAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,MAAM,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,mBAAmB,EAAC,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"yav\",[[\"kiɛmɛ́ɛm\",\"kisɛ́ndɛ\"],u,u],u,[[\"s\",\"m\",\"m\",\"e\",\"k\",\"f\",\"s\"],[\"sd\",\"md\",\"mw\",\"et\",\"kl\",\"fl\",\"ss\"],[\"sɔ́ndiɛ\",\"móndie\",\"muányáŋmóndie\",\"metúkpíápɛ\",\"kúpélimetúkpiapɛ\",\"feléte\",\"séselé\"],[\"sd\",\"md\",\"mw\",\"et\",\"kl\",\"fl\",\"ss\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"o.1\",\"o.2\",\"o.3\",\"o.4\",\"o.5\",\"o.6\",\"o.7\",\"o.8\",\"o.9\",\"o.10\",\"o.11\",\"o.12\"],[\"pikítíkítie, oólí ú kutúan\",\"siɛyɛ́, oóli ú kándíɛ\",\"ɔnsúmbɔl, oóli ú kátátúɛ\",\"mesiŋ, oóli ú kénie\",\"ensil, oóli ú kátánuɛ\",\"ɔsɔn\",\"efute\",\"pisuyú\",\"imɛŋ i puɔs\",\"imɛŋ i putúk,oóli ú kátíɛ\",\"makandikɛ\",\"pilɔndɔ́\"]],u,[[\"k.Y.\",\"+J.C.\"],u,[\"katikupíen Yésuse\",\"ékélémkúnupíén n\"]],1,[6,0],[\"d/M/y\",\"d MMM y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"XAF\",\"FCFA\",\"XAF\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}