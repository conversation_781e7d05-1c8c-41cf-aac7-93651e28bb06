{"version": 3, "file": "rm.js", "sourceRoot": "", "sources": ["rm.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,OAAO,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,YAAY,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,EAAC,cAAc,EAAC,YAAY,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,aAAa,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,SAAS,EAAC,QAAQ,EAAC,MAAM,EAAC,QAAQ,EAAC,MAAM,EAAC,WAAW,EAAC,SAAS,EAAC,OAAO,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,eAAe,EAAC,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,UAAU,EAAC,sBAAsB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"rm\",[[\"AM\",\"PM\"],u,u],u,[[\"D\",\"G\",\"M\",\"M\",\"G\",\"V\",\"S\"],[\"du\",\"gli\",\"ma\",\"me\",\"gie\",\"ve\",\"so\"],[\"dumengia\",\"glindesdi\",\"mardi\",\"mesemna\",\"gievgia\",\"venderdi\",\"sonda\"],[\"du\",\"gli\",\"ma\",\"me\",\"gie\",\"ve\",\"so\"]],u,[[\"S\",\"F\",\"M\",\"A\",\"M\",\"Z\",\"F\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"schan.\",\"favr.\",\"mars\",\"avr.\",\"matg\",\"zercl.\",\"fan.\",\"avust\",\"sett.\",\"oct.\",\"nov.\",\"dec.\"],[\"da schaner\",\"da favrer\",\"da mars\",\"d’avrigl\",\"da matg\",\"da zercladur\",\"da fanadur\",\"d’avust\",\"da settember\",\"d’october\",\"da november\",\"da december\"]],[[\"S\",\"F\",\"M\",\"A\",\"M\",\"Z\",\"F\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"schan.\",\"favr.\",\"mars\",\"avr.\",\"matg\",\"zercl.\",\"fan.\",\"avust\",\"sett.\",\"oct.\",\"nov.\",\"dec.\"],[\"schaner\",\"favrer\",\"mars\",\"avrigl\",\"matg\",\"zercladur\",\"fanadur\",\"avust\",\"settember\",\"october\",\"november\",\"december\"]],[[\"av. Cr.\",\"s. Cr.\"],u,[\"avant Cristus\",\"suenter Cristus\"]],1,[6,0],[\"dd-MM-yy\",\"dd-MM-y\",\"d MMMM y\",\"EEEE, 'ils' d MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\"’\",\";\",\"%\",\"+\",\"−\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"CHF\",\"CHF\",\"franc svizzer\",{\"JPY\":[\"JP¥\",\"¥\"]},\"ltr\", plural];\n"]}