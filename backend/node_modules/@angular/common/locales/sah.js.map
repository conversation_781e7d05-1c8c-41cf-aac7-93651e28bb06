{"version": 3, "file": "sah.js", "sourceRoot": "", "sources": ["sah.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,cAAc,EAAC,cAAc,EAAC,cAAc,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,SAAS,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,aAAa,EAAC,YAAY,EAAC,UAAU,EAAC,UAAU,EAAC,SAAS,EAAC,eAAe,EAAC,cAAc,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,aAAa,EAAC,YAAY,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,cAAc,EAAC,aAAa,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,6BAA6B,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,gBAAgB,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,sBAAsB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"sah\",[[\"ЭИ\",\"ЭК\"],u,u],u,[[\"Б\",\"Б\",\"О\",\"С\",\"Ч\",\"Б\",\"С\"],[\"бс\",\"бн\",\"оп\",\"сэ\",\"чп\",\"бэ\",\"сб\"],[\"баскыһыанньа\",\"бэнидиэнньик\",\"оптуорунньук\",\"сэрэдэ\",\"чэппиэр\",\"Бээтиҥсэ\",\"субуота\"],[\"бс\",\"бн\",\"оп\",\"сэ\",\"чп\",\"бэ\",\"сб\"]],u,[[\"Т\",\"О\",\"К\",\"М\",\"Ы\",\"Б\",\"О\",\"А\",\"Б\",\"А\",\"С\",\"А\"],[\"Тохс\",\"Олун\",\"Клн\",\"Мсу\",\"Ыам\",\"Бэс\",\"Отй\",\"Атр\",\"Блҕ\",\"Алт\",\"Сэт\",\"Ахс\"],[\"Тохсунньу\",\"Олунньу\",\"Кулун тутар\",\"Муус устар\",\"Ыам ыйын\",\"Бэс ыйын\",\"От ыйын\",\"Атырдьых ыйын\",\"Балаҕан ыйын\",\"Алтынньы\",\"Сэтинньи\",\"ахсынньы\"]],[[\"Т\",\"О\",\"К\",\"М\",\"Ы\",\"Б\",\"О\",\"А\",\"Б\",\"А\",\"С\",\"А\"],[\"Тохс\",\"Олун\",\"Клн\",\"Мсу\",\"Ыам\",\"Бэс\",\"Отй\",\"Атр\",\"Блҕ\",\"Алт\",\"Сэт\",\"Ахс\"],[\"тохсунньу\",\"олунньу\",\"кулун тутар\",\"муус устар\",\"ыам ыйа\",\"бэс ыйа\",\"от ыйа\",\"атырдьых ыйа\",\"балаҕан ыйа\",\"алтынньы\",\"сэтинньи\",\"ахсынньы\"]],[[\"б. э. и.\",\"б. э\"],u,u],1,[6,0],[\"yy/M/d\",\"y, MMM d\",\"y, MMMM d\",\"y 'сыл' MMMM d 'күнэ', EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"чыыһыла буотах\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"RUB\",\"₽\",\"Арассыыйа солкуобайа\",{\"JPY\":[\"JP¥\",\"¥\"],\"RUB\":[\"₽\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}