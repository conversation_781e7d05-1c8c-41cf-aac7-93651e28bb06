{"version": 3, "file": "kok.js", "sourceRoot": "", "sources": ["kok.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,UAAU,EAAC,YAAY,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,QAAQ,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,YAAY,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,cAAc,EAAC,YAAY,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,eAAe,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"kok\",[[\"a\",\"p\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"आ\",\"सो\",\"मं\",\"बु\",\"बि\",\"शु\",\"शे\"],[\"आयतार\",\"सोमार\",\"मंगळार\",\"बुधवार\",\"बिरेस्तार\",\"शुक्रार\",\"शेनवार\"],u,[\"आय\",\"सोम\",\"मंगळ\",\"बुध\",\"बिरे\",\"शुक्र\",\"शेन\"]],[[\"आ\",\"सो\",\"मं\",\"बु\",\"ब\",\"शु\",\"शे\"],[\"आयतार\",\"सोमार\",\"मंगळार\",\"बुधवार\",\"बिरेस्तार\",\"शुक्रार\",\"शेनवार\"],u,[\"आय\",\"सोम\",\"मंगळ\",\"बुध\",\"बिरे\",\"शुक्र\",\"शेन\"]],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"जानेवारी\",\"फेब्रुवारी\",\"मार्च\",\"एप्रील\",\"मे\",\"जून\",\"जुलय\",\"ऑगस्ट\",\"सप्टेंबर\",\"ऑक्टोबर\",\"नोव्हेंबर\",\"डिसेंबर\"],u],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"जाने\",\"फेब्रु\",\"मार्च\",\"एप्री\",\"मे\",\"जून\",\"जुल\",\"ऑग\",\"सप्टें\",\"ऑक्टो\",\"नो\",\"डिसे\"],[\"जानेवारी\",\"फेब्रुवारी\",\"मार्च\",\"एप्रील\",\"मे\",\"जून\",\"जुलय\",\"ऑगस्ट\",\"सप्टेंबर\",\"ऑक्टोबर\",\"नोव्हेंबर\",\"डिसेंबर\"]],[[\"क्रिस्तपूर्व\",\"क्रिस्तशखा\"],u,u],0,[0,0],[\"d-M-yy\",\"d-MMM-y\",\"d MMMM y\",\"EEEE d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"INR\",\"₹\",\"भारतीय रुपया\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"RON\":[\"रॉन\",\"लेई\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}