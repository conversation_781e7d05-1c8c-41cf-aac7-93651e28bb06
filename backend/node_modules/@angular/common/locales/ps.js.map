{"version": 3, "file": "ps.js", "sourceRoot": "", "sources": ["ps.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,SAAS,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,OAAO,EAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,CAAC,iBAAiB,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,qBAAqB,EAAC,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,SAAS,EAAC,cAAc,EAAC,mBAAmB,CAAC,EAAC,CAAC,MAAM,EAAC,SAAS,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,QAAQ,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"ps\",[[\"غ.م.\",\"غ.و.\"],u,u],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"يونۍ\",\"دونۍ\",\"درېنۍ\",\"څلرنۍ\",\"پينځنۍ\",\"جمعه\",\"اونۍ\"],u,u],u,[[\"ج\",\"ف\",\"م\",\"ا\",\"م\",\"ج\",\"ج\",\"ا\",\"س\",\"ا\",\"ن\",\"د\"],[\"جنوري\",\"فبروري\",\"مارچ\",\"اپریل\",\"مۍ\",\"جون\",\"جولای\",\"اګست\",\"سېپتمبر\",\"اکتو<PERSON>ر\",\"نومبر\",\"د<PERSON>مبر\"],u],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"جنوري\",\"فبروري\",\"مارچ\",\"اپریل\",\"مۍ\",\"جون\",\"جولای\",\"اګست\",\"سپتمبر\",\"اکتوبر\",\"نومبر\",\"دسمبر\"],[\"جنوري\",\"فېبروري\",\"مارچ\",\"اپریل\",\"مۍ\",\"جون\",\"جولای\",\"اګست\",\"سپتمبر\",\"اکتوبر\",\"نومبر\",\"دسمبر\"]],[[\"له میلاد وړاندې\",\"م.\"],u,[\"له میلاد څخه وړاندې\",\"له میلاد څخه وروسته\"]],6,[4,5],[\"y/M/d\",\"y MMM d\",\"د y د MMMM d\",\"EEEE د y د MMMM d\"],[\"H:mm\",\"H:mm:ss\",\"H:mm:ss (z)\",\"H:mm:ss (zzzz)\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"‎+\",\"‎−\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"AFN\",\"؋\",\"افغانۍ\",{\"AFN\":[\"؋\"],\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"]},\"rtl\", plural];\n"]}