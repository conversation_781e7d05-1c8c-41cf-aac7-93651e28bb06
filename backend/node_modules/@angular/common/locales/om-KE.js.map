{"version": 3, "file": "om-KE.js", "sourceRoot": "", "sources": ["om-KE.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,OAAO,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,aAAa,EAAC,aAAa,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,YAAY,EAAC,SAAS,EAAC,UAAU,EAAC,cAAc,EAAC,SAAS,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,aAAa,EAAC,aAAa,EAAC,MAAM,EAAC,QAAQ,EAAC,YAAY,EAAC,YAAY,EAAC,SAAS,EAAC,UAAU,EAAC,cAAc,EAAC,SAAS,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,kBAAkB,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"om-KE\",[[\"WD\",\"WB\"],u,u],u,[[\"D\",\"W\",\"Q\",\"R\",\"K\",\"J\",\"S\"],[\"Dil\",\"Wix\",\"Qib\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>xa<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>oo<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"],[\"Dil\",\"Wix\",\"Qib\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON>\"]],u,[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"A\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"Ama\",\"<PERSON><PERSON>\",\"<PERSON>\",\"Elb\",\"<PERSON>\",\"Wax\",\"Ad<PERSON>\",\"<PERSON>g\",\"<PERSON>l\",\"<PERSON>k\",\"<PERSON>\",\"<PERSON>d\"],[\"<PERSON>ajjii\",\"<PERSON><PERSON>and<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>ba\",\"<PERSON>aa<PERSON>a\",\"<PERSON>ax<PERSON>jjii\",\"<PERSON>ool<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>lo<PERSON><PERSON>\",\"<PERSON>aa<PERSON>\",\"<PERSON>ddee\"]],[[\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"C\",\"W\",\"A\",\"H\",\"F\",\"O\",\"S\",\"M\"],[\"Ama\",\"Gur\",\"Bit\",\"Elb\",\"Cam\",\"Wax\",\"Ado\",\"Hag\",\"Ful\",\"Onk\",\"Sad\",\"Mud\"],[\"Amajjii\",\"Guraandhala\",\"Bitooteessa\",\"Elba\",\"Caamsa\",\"Waxabajjii\",\"Adooleessa\",\"Hagayya\",\"Fuulbana\",\"Onkololeessa\",\"Sadaasa\",\"Muddee\"]],[[\"KD\",\"CE\"],u,[\"Dheengadda Jeesu\",\"CE\"]],0,[6,0],[\"dd/MM/yy\",\"dd-MMM-y\",\"dd MMMM y\",\"EEEE, MMMM d, y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"KES\",\"Ksh\",\"KES\",{\"ETB\":[\"Br\"],\"JPY\":[\"JP¥\",\"¥\"],\"KES\":[\"Ksh\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}