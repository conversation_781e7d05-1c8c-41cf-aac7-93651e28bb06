{"version": 3, "file": "lkt.js", "sourceRoot": "", "sources": ["lkt.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,KAAK,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,EAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,cAAc,EAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,EAAC,aAAa,EAAC,aAAa,EAAC,YAAY,EAAC,cAAc,EAAC,eAAe,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,eAAe,EAAC,iBAAiB,EAAC,mBAAmB,EAAC,aAAa,EAAC,gBAAgB,EAAC,oBAAoB,EAAC,gBAAgB,EAAC,aAAa,EAAC,eAAe,EAAC,mBAAmB,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"lkt\",[[\"AM\",\"PM\"],u,u],u,[[\"A\",\"W\",\"N\",\"Y\",\"T\",\"Z\",\"O\"],[\"Aŋpétuwakȟaŋ\",\"Aŋp<PERSON>tuwaŋži\",\"Aŋp<PERSON>tunuŋpa\",\"<PERSON>ŋp<PERSON><PERSON>yamni\",\"<PERSON>ŋ<PERSON><PERSON><PERSON>topa\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"Owáŋgyužažapi\"],u,u],[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"Aŋpétuwakȟaŋ\",\"<PERSON>ŋ<PERSON><PERSON><PERSON>waŋži\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ŋ<PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>tuzaptaŋ\",\"<PERSON>w<PERSON>ŋgyuža<PERSON>api\"],u,u],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"Wiótheȟika Wí\",\"Thiyóȟeyuŋka Wí\",\"Išt<PERSON>wičhayazaŋ Wí\",\"Pȟežítȟo Wí\",\"Čhaŋwápetȟo Wí\",\"Wípazukȟa-wašté Wí\",\"Čhaŋpȟásapa Wí\",\"Wasútȟuŋ Wí\",\"Čhaŋwápeǧi Wí\",\"Čhaŋwápe-kasná Wí\",\"Waníyetu Wí\",\"Tȟahékapšuŋ Wí\"],u],u,[[\"BCE\",\"CE\"],u,u],0,[6,0],[\"M/d/yy\",\"MMM d, y\",\"MMMM d, y\",\"EEEE, MMMM d, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤ #,##0.00\",\"#E0\"],\"USD\",\"$\",\"USD\",{\"JPY\":[\"JP¥\",\"¥\"]},\"ltr\", plural];\n"]}