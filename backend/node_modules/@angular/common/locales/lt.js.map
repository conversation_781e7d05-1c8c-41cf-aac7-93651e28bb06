{"version": 3, "file": "lt.js", "sourceRoot": "", "sources": ["lt.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAE9E,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QACjD,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QAClG,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACV,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,aAAa,EAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,gBAAgB,EAAC,cAAc,EAAC,aAAa,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,EAAC,QAAQ,EAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,OAAO,EAAC,WAAW,EAAC,UAAU,EAAC,QAAQ,EAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,oBAAoB,EAAC,0BAA0B,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,MAAM,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,OAAO,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n\nif (n % 10 === 1 && !(n % 100 >= 11 && n % 100 <= 19))\n    return 1;\nif (n % 10 === Math.floor(n % 10) && (n % 10 >= 2 && n % 10 <= 9) && !(n % 100 >= 11 && n % 100 <= 19))\n    return 3;\nif (!(f === 0))\n    return 4;\nreturn 5;\n}\n\nexport default [\"lt\",[[\"pr. p.\",\"pop.\"],[\"priešpiet\",\"popiet\"],u],u,[[\"S\",\"P\",\"A\",\"T\",\"K\",\"P\",\"Š\"],[\"sk\",\"pr\",\"an\",\"tr\",\"kt\",\"pn\",\"št\"],[\"sekmadienis\",\"pirmadienis\",\"antradienis\",\"trečiadienis\",\"ketvirtadienis\",\"penktadienis\",\"šeštadienis\"],[\"Sk\",\"Pr\",\"An\",\"Tr\",\"Kt\",\"Pn\",\"Št\"]],u,[[\"S\",\"V\",\"K\",\"B\",\"G\",\"B\",\"L\",\"R\",\"R\",\"S\",\"L\",\"G\"],[\"saus.\",\"vas.\",\"kov.\",\"bal.\",\"geg.\",\"birž.\",\"liep.\",\"rugp.\",\"rugs.\",\"spal.\",\"lapkr.\",\"gruod.\"],[\"sausio\",\"vasario\",\"kovo\",\"balandžio\",\"gegužės\",\"birželio\",\"liepos\",\"rugpjūčio\",\"rugsėjo\",\"spalio\",\"lapkričio\",\"gruodžio\"]],[[\"S\",\"V\",\"K\",\"B\",\"G\",\"B\",\"L\",\"R\",\"R\",\"S\",\"L\",\"G\"],[\"saus.\",\"vas.\",\"kov.\",\"bal.\",\"geg.\",\"birž.\",\"liep.\",\"rugp.\",\"rugs.\",\"spal.\",\"lapkr.\",\"gruod.\"],[\"sausis\",\"vasaris\",\"kovas\",\"balandis\",\"gegužė\",\"birželis\",\"liepa\",\"rugpjūtis\",\"rugsėjis\",\"spalis\",\"lapkritis\",\"gruodis\"]],[[\"pr. Kr.\",\"po Kr.\"],u,[\"prieš Kristų\",\"po Kristaus\"]],1,[6,0],[\"y-MM-dd\",u,\"y 'm'. MMMM d 'd'.\",\"y 'm'. MMMM d 'd'., EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"−\",\"×10^\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"Euras\",{\"AUD\":[u,\"$\"],\"BDT\":[],\"BRL\":[u,\"R$\"],\"BYN\":[u,\"Br\"],\"CAD\":[u,\"$\"],\"CNY\":[u,\"¥\"],\"GBP\":[u,\"£\"],\"HKD\":[u,\"$\"],\"ILS\":[],\"INR\":[],\"JPY\":[u,\"¥\"],\"KHR\":[],\"KRW\":[u,\"₩\"],\"LAK\":[],\"MNT\":[],\"MXN\":[u,\"$\"],\"NZD\":[u,\"$\"],\"PHP\":[u,\"₱\"],\"PLN\":[u,\"zl\"],\"PYG\":[u,\"Gs\"],\"RUB\":[u,\"rb\"],\"TWD\":[u,\"$\"],\"USD\":[u,\"$\"],\"VND\":[],\"XAF\":[],\"XCD\":[u,\"$\"],\"XOF\":[],\"XPF\":[]},\"ltr\", plural];\n"]}