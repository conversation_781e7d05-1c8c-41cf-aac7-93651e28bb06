{"version": 3, "file": "sa.js", "sourceRoot": "", "sources": ["sa.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,YAAY,EAAC,UAAU,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,QAAQ,EAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,aAAa,EAAC,WAAW,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,IAAI,EAAC,MAAM,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,QAAQ,EAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,aAAa,EAAC,WAAW,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,aAAa,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,WAAW,EAAC,cAAc,EAAC,OAAO,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,iBAAiB,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nreturn 5;\n}\n\nexport default [\"sa\",[[\"AM\",\"PM\"],u,[\"पूर्वाह्न\",\"अपराह्न\"]],[[\"AM\",\"PM\"],u,u],[[\"र\",\"सो\",\"मं\",\"बु\",\"गु\",\"शु\",\"श\"],[\"रवि\",\"सोम\",\"मंगल\",\"बुध\",\"गुरु\",\"शुक्र\",\"शनि\"],[\"रविवासरः\",\"सोमवासरः\",\"मंगलवासरः\",\"बुधवासरः\",\"गुरुवासर:\",\"शुक्रवासरः\",\"शनिवासरः\"],[\"Sun\",\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\"]],u,[[\"ज\",\"फ\",\"मा\",\"अ\",\"म\",\"जू\",\"जु\",\"अ\",\"सि\",\"अ\",\"न\",\"दि\"],[\"जनवरी:\",\"फरवरी:\",\"मार्च:\",\"अप्रैल:\",\"मई\",\"जून:\",\"जुलाई:\",\"अगस्त:\",\"सितंबर:\",\"अक्तूबर:\",\"नवंबर:\",\"दिसंबर:\"],[\"जनवरीमासः\",\"फरवरीमासः\",\"मार्चमासः\",\"अप्रैलमासः\",\"मईमासः\",\"जूनमासः\",\"जुलाईमासः\",\"अगस्तमासः\",\"सितंबरमासः\",\"अक्तूबरमासः\",\"नवंबरमासः\",\"दिसंबरमासः\"]],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"जनवरी:\",\"फरवरी:\",\"मार्च:\",\"अप्रैल:\",\"मई\",\"जून:\",\"जुलाई:\",\"अगस्त:\",\"सितंबर:\",\"अक्तूबर:\",\"नवंबर:\",\"दिसंबर:\"],[\"जनवरीमासः\",\"फरवरीमासः\",\"मार्चमासः\",\"अप्रैलमासः\",\"मईमासः\",\"जूनमासः\",\"जुलाईमासः\",\"अगस्तमासः\",\"सितंबरमासः\",\"अक्तूबरमासः\",\"नवंबरमासः\",\"दिसंबरमासः\"]],[[\"BCE\",\"CE\"],u,u],0,[0,0],[\"d/M/yy\",\"d MMM y\",\"d MMMM y\",\"EEEE, d MMMM y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} तदा {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##,##0%\",\"¤#,##,##0.00\",\"[#E0]\"],\"INR\",\"₹\",\"भारतीय रूप्यकम्\",{\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}