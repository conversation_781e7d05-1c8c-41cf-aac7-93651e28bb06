{"version": 3, "file": "hy.js", "sourceRoot": "", "sources": ["hy.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,YAAY,EAAC,WAAW,EAAC,YAAY,EAAC,WAAW,EAAC,QAAQ,EAAC,OAAO,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,UAAU,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,YAAY,EAAC,YAAY,EAAC,WAAW,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,cAAc,EAAC,eAAe,EAAC,mBAAmB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,eAAe,EAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, i = Math.floor(Math.abs(val));\n\nif (i === 0 || i === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"hy\",[[\"ա\",\"հ\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"Կ\",\"Ե\",\"Ե\",\"Չ\",\"Հ\",\"Ո\",\"Շ\"],[\"կիր\",\"երկ\",\"երք\",\"չրք\",\"հնգ\",\"ուր\",\"շբթ\"],[\"կիրակի\",\"երկուշաբթի\",\"երեքշաբթի\",\"չորեքշաբթի\",\"հինգշաբթի\",\"ուրբաթ\",\"շաբաթ\"],[\"կր\",\"եկ\",\"եք\",\"չք\",\"հգ\",\"ու\",\"շբ\"]],u,[[\"Հ\",\"Փ\",\"Մ\",\"Ա\",\"Մ\",\"Հ\",\"Հ\",\"Օ\",\"Ս\",\"Հ\",\"Ն\",\"Դ\"],[\"հնվ\",\"փտվ\",\"մրտ\",\"ապր\",\"մյս\",\"հնս\",\"հլս\",\"օգս\",\"սեպ\",\"հոկ\",\"նոյ\",\"դեկ\"],[\"հունվարի\",\"փետրվարի\",\"մարտի\",\"ապրիլի\",\"մայիսի\",\"հունիսի\",\"հուլիսի\",\"օգոստոսի\",\"սեպտեմբերի\",\"հոկտեմբերի\",\"նոյեմբերի\",\"դեկտեմբերի\"]],[[\"Հ\",\"Փ\",\"Մ\",\"Ա\",\"Մ\",\"Հ\",\"Հ\",\"Օ\",\"Ս\",\"Հ\",\"Ն\",\"Դ\"],[\"հնվ\",\"փտվ\",\"մրտ\",\"ապր\",\"մյս\",\"հնս\",\"հլս\",\"օգս\",\"սեպ\",\"հոկ\",\"նոյ\",\"դեկ\"],[\"հունվար\",\"փետրվար\",\"մարտ\",\"ապրիլ\",\"մայիս\",\"հունիս\",\"հուլիս\",\"օգոստոս\",\"սեպտեմբեր\",\"հոկտեմբեր\",\"նոյեմբեր\",\"դեկտեմբեր\"]],[[\"մ.թ.ա.\",\"մ.թ.\"],u,[\"Քրիստոսից առաջ\",\"Քրիստոսից հետո\"]],1,[6,0],[\"dd.MM.yy\",\"dd MMM, y թ.\",\"dd MMMM, y թ.\",\"y թ. MMMM d, EEEE\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1}, {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"ՈչԹ\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"AMD\",\"֏\",\"հայկական դրամ\",{\"AMD\":[\"֏\"],\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}