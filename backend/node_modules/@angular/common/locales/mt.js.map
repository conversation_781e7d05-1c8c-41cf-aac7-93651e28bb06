{"version": 3, "file": "mt.js", "sourceRoot": "", "sources": ["mt.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QAC7E,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC;QACnE,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,SAAS,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,EAAC,sBAAsB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nif (n === 0 || n % 100 === Math.floor(n % 100) && (n % 100 >= 2 && n % 100 <= 10))\n    return 3;\nif (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 19))\n    return 4;\nreturn 5;\n}\n\nexport default [\"mt\",[[\"am\",\"pm\"],[\"AM\",\"PM\"],u],u,[[\"Ħd\",\"T\",\"Tl\",\"Er\",\"Ħm\",\"Ġm\",\"Sb\"],[\"Ħad\",\"Tne\",\"Tli\",\"Erb\",\"Ħam\",\"Ġim\",\"Sib\"],[\"Il-Ħadd\",\"It-Tnejn\",\"It-Tlieta\",\"L-Erbgħa\",\"Il-Ħamis\",\"Il-Ġimgħa\",\"Is-Sibt\"],[\"Ħad\",\"Tne\",\"Tli\",\"Erb\",\"Ħam\",\"Ġim\",\"Sib\"]],[[\"Ħd\",\"Tn\",\"Tl\",\"Er\",\"Ħm\",\"Ġm\",\"Sb\"],[\"Ħad\",\"Tne\",\"Tli\",\"Erb\",\"Ħam\",\"Ġim\",\"Sib\"],[\"Il-Ħadd\",\"It-Tnejn\",\"It-Tlieta\",\"L-Erbgħa\",\"Il-Ħamis\",\"Il-Ġimgħa\",\"Is-Sibt\"],[\"Ħad\",\"Tne\",\"Tli\",\"Erb\",\"Ħam\",\"Ġim\",\"Sib\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"Ġ\",\"L\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"Jan\",\"Fra\",\"Mar\",\"Apr\",\"Mej\",\"Ġun\",\"Lul\",\"Aww\",\"Set\",\"Ott\",\"Nov\",\"Diċ\"],[\"Jannar\",\"Frar\",\"Marzu\",\"April\",\"Mejju\",\"Ġunju\",\"Lulju\",\"Awwissu\",\"Settembru\",\"Ottubru\",\"Novembru\",\"Diċembru\"]],[[\"Jn\",\"Fr\",\"Mz\",\"Ap\",\"Mj\",\"Ġn\",\"Lj\",\"Aw\",\"St\",\"Ob\",\"Nv\",\"Dċ\"],[\"Jan\",\"Fra\",\"Mar\",\"Apr\",\"Mej\",\"Ġun\",\"Lul\",\"Aww\",\"Set\",\"Ott\",\"Nov\",\"Diċ\"],[\"Jannar\",\"Frar\",\"Marzu\",\"April\",\"Mejju\",\"Ġunju\",\"Lulju\",\"Awwissu\",\"Settembru\",\"Ottubru\",\"Novembru\",\"Diċembru\"]],[[\"QK\",\"WK\"],u,[\"Qabel Kristu\",\"Wara Kristu\"]],0,[6,0],[\"dd/MM/y\",\"dd MMM y\",\"d 'ta'’ MMMM y\",\"EEEE, d 'ta'’ MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"EUR\",\"€\",\"ewro\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}