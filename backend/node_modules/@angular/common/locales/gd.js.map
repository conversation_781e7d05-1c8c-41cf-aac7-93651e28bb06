{"version": 3, "file": "gd.js", "sourceRoot": "", "sources": ["gd.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;QACnB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;QACnB,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;QAChE,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,aAAa,EAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,aAAa,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,kBAAkB,EAAC,eAAe,EAAC,YAAY,EAAC,eAAe,EAAC,eAAe,EAAC,cAAc,EAAC,aAAa,EAAC,eAAe,EAAC,gBAAgB,EAAC,cAAc,EAAC,gBAAgB,EAAC,gBAAgB,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,eAAe,EAAC,YAAY,EAAC,SAAS,EAAC,YAAY,EAAC,YAAY,EAAC,cAAc,EAAC,aAAa,EAAC,aAAa,EAAC,cAAc,EAAC,YAAY,EAAC,cAAc,EAAC,cAAc,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,aAAa,EAAC,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,cAAc,EAAC,oBAAoB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,iBAAiB,EAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1 || n === 11)\n    return 1;\nif (n === 2 || n === 12)\n    return 2;\nif (n === Math.floor(n) && (n >= 3 && n <= 10 || n >= 13 && n <= 19))\n    return 3;\nreturn 5;\n}\n\nexport default [\"gd\",[[\"m\",\"f\"],u,u],u,[[\"D\",\"L\",\"M\",\"C\",\"A\",\"H\",\"S\"],[\"DiD\",\"DiL\",\"DiM\",\"DiC\",\"Dia\",\"Dih\",\"DiS\"],[\"DiDòmhnaich\",\"DiL<PERSON>in\",\"DiMàirt\",\"Di<PERSON>iadain\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON>athair<PERSON>\"],[\"<PERSON><PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON>\",\"hA\",\"Sa\"]],u,[[\"F\",\"G\",\"<PERSON>\",\"G\",\"<PERSON>\",\"Ò\",\"I\",\"L\",\"S\",\"D\",\"S\",\"<PERSON>\"],[\"<PERSON>aoi\",\"<PERSON>r\",\"<PERSON><PERSON>rt\",\"<PERSON>ibl\",\"<PERSON>èit\",\"Ògmh\",\"Iuch\",\"L<PERSON>na\",\"<PERSON>t\",\"<PERSON>àmh\",\"<PERSON>h\",\"<PERSON>ùbh\"],[\"dhen <PERSON>haoilleach\",\"dhen Ghearran\",\"dhen Mhàrt\",\"dhen Ghiblean\",\"dhen Chèitean\",\"dhen Ògmhios\",\"dhen Iuchar\",\"dhen Lùnastal\",\"dhen t-Sultain\",\"dhen Dàmhair\",\"dhen t-Samhain\",\"dhen Dùbhlachd\"]],[[\"F\",\"G\",\"M\",\"G\",\"C\",\"Ò\",\"I\",\"L\",\"S\",\"D\",\"S\",\"D\"],[\"Faoi\",\"Gearr\",\"Màrt\",\"Gibl\",\"Cèit\",\"Ògmh\",\"Iuch\",\"Lùna\",\"Sult\",\"Dàmh\",\"Samh\",\"Dùbh\"],[\"Am Faoilleach\",\"An Gearran\",\"Am Màrt\",\"An Giblean\",\"An Cèitean\",\"An t-Ògmhios\",\"An t-Iuchar\",\"An Lùnastal\",\"An t-Sultain\",\"An Dàmhair\",\"An t-Samhain\",\"An Dùbhlachd\"]],[[\"R\",\"A\"],[\"RC\",\"AD\"],[\"Ro Chrìosta\",\"An dèidh Chrìosta\"]],1,[6,0],[\"dd/MM/y\",\"d MMM y\",\"d'mh' MMMM y\",\"EEEE, d'mh' MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"#E0\"],\"GBP\",\"£\",\"Punnd Sasannach\",{\"BYN\":[u,\"р.\"],\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"RON\":[u,\"leu\"],\"RUR\":[u,\"р.\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"],\"XXX\":[]},\"ltr\", plural];\n"]}