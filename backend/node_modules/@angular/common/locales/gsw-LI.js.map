{"version": 3, "file": "gsw-LI.js", "sourceRoot": "", "sources": ["gsw-LI.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,QAAQ,EAAC,CAAC,CAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,WAAW,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,SAAS,EAAC,WAAW,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,SAAS,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,EAAC,UAAU,EAAC,WAAW,EAAC,UAAU,EAAC,UAAU,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,SAAS,EAAC,SAAS,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,kBAAkB,EAAC,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"gsw-LI\",[[\"vorm.\",\"nam.\"],u,[\"am Vormittag\",\"am Namittag\"]],[[\"vorm.\",\"nam.\"],u,[\"Vormittag\",\"Namittag\"]],[[\"S\",\"M\",\"D\",\"M\",\"D\",\"F\",\"S\"],[\"Su.\",\"Mä.\",\"Zi.\",\"Mi.\",\"Du.\",\"Fr.\",\"Sa.\"],[\"Sunntig\",\"<PERSON><PERSON><PERSON>nti<PERSON>\",\"<PERSON>ii<PERSON>tig\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>iitig\",\"<PERSON><PERSON>ti<PERSON>\"],[\"<PERSON>.\",\"<PERSON><PERSON>.\",\"Zi.\",\"Mi.\",\"Du.\",\"Fr.\",\"Sa.\"]],u,[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"O\",\"<PERSON>\",\"<PERSON>\"],[\"<PERSON>\",\"<PERSON>\",\"<PERSON><PERSON>r\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>\",\"<PERSON>t\",\"<PERSON>\",\"<PERSON>z\"],[\"<PERSON>uar\",\"<PERSON>ruar\",\"<PERSON><PERSON>rz\",\"April\",\"<PERSON>\",\"<PERSON>i\",\"<PERSON>i\",\"<PERSON>uscht\",\"Septämber\",\"Oktoober\",\"Novämber\",\"Dezämber\"]],u,[[\"v. Chr.\",\"n. Chr.\"],u,u],1,[6,0],[\"dd.MM.yy\",\"dd.MM.y\",\"d. MMMM y\",\"EEEE, d. MMMM y\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\".\",\"’\",\";\",\"%\",\"+\",\"−\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"#,##0.00 ¤\",\"#E0\"],\"CHF\",\"CHF\",\"Schwiizer Franke\",{\"ATS\":[\"öS\"]},\"ltr\", plural];\n"]}