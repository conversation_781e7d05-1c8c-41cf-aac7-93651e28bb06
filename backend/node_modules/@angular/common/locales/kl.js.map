{"version": 3, "file": "kl.js", "sourceRoot": "", "sources": ["kl.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,gBAAgB,EAAC,eAAe,EAAC,iBAAiB,EAAC,gBAAgB,EAAC,iBAAiB,EAAC,gBAAgB,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,WAAW,EAAC,YAAY,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,UAAU,EAAC,aAAa,EAAC,WAAW,EAAC,YAAY,EAAC,YAAY,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,SAAS,EAAC,YAAY,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,SAAS,EAAC,SAAS,EAAC,UAAU,EAAC,gBAAgB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,SAAS,EAAC,sBAAsB,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"kl\",[[\"AM\",\"PM\"],u,u],u,[[\"S\",\"M\",\"T\",\"W\",\"T\",\"F\",\"S\"],[\"sap\",\"ata\",\"mar\",\"pin\",\"sis\",\"tal\",\"arf\"],[\"sapaat\",\"ataasinngorneq\",\"marlunngorneq\",\"pingasunngorneq\",\"sisamanngorneq\",\"tallimanngorneq\",\"arfininngorneq\"],[\"sap\",\"ata\",\"mar\",\"pin\",\"sis\",\"tal\",\"arf\"]],u,[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"jan\",\"febr\",\"mar\",\"apr\",\"maj\",\"jun\",\"jul\",\"aug\",\"sept\",\"okt\",\"nov\",\"dec\"],[\"januaarip\",\"februaarip\",\"marsip\",\"apriilip\",\"maajip\",\"juunip\",\"juulip\",\"aggustip\",\"septembarip\",\"oktobarip\",\"novembarip\",\"decembarip\"]],[[\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\"],[\"jan\",\"febr\",\"mar\",\"apr\",\"maj\",\"jun\",\"jul\",\"aug\",\"sept\",\"okt\",\"nov\",\"dec\"],[\"januaari\",\"februaari\",\"marsi\",\"apriili\",\"maaji\",\"juuni\",\"juuli\",\"aggusti\",\"septembari\",\"oktobari\",\"novembari\",\"decembari\"]],[[\"BCE\",\"CE\"],u,u],1,[6,0],[\"y-MM-dd\",\"y MMM d\",\"y MMMM d\",\"y MMMM d, EEEE\"],[\"HH.mm\",\"HH.mm.ss\",\"HH.mm.ss z\",\"HH.mm.ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\".\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##0.###\",\"#,##0 %\",\"¤#,##0.00;¤-#,##0.00\",\"#E0\"],\"DKK\",\"kr.\",\"DKK\",{\"DKK\":[\"kr.\",\"kr\"],\"JPY\":[\"JP¥\",\"¥\"],\"USD\":[\"US$\",\"$\"]},\"ltr\", plural];\n"]}