/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AFN: string[];
    BYN: (string | undefined)[];
    CAD: string[];
    CNY: string[];
    HKD: string[];
    IRR: string[];
    MXN: string[];
    NZD: string[];
    PHP: (string | undefined)[];
    THB: string[];
    XCD: string[];
    XOF: string[];
} | undefined)[];
export default _default;
