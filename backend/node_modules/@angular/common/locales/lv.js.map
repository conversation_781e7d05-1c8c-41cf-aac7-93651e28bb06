{"version": 3, "file": "lv.js", "sourceRoot": "", "sources": ["lv.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IAElI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;QACzK,OAAO,CAAC,CAAC;IACb,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnH,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,UAAU,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,UAAU,EAAC,UAAU,CAAC,EAAC,CAAC,EAAC,CAAC,gBAAgB,EAAC,aAAa,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,OAAO,EAAC,QAAQ,EAAC,UAAU,EAAC,SAAS,EAAC,QAAQ,CAAC,EAAC,CAAC,WAAW,EAAC,WAAW,EAAC,UAAU,EAAC,WAAW,EAAC,aAAa,EAAC,YAAY,EAAC,WAAW,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,CAAC,EAAC,CAAC,UAAU,EAAC,WAAW,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,YAAY,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,QAAQ,EAAC,MAAM,CAAC,EAAC,CAAC,EAAC,CAAC,iBAAiB,EAAC,UAAU,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,UAAU,EAAC,kBAAkB,EAAC,mBAAmB,EAAC,yBAAyB,CAAC,EAAC,CAAC,OAAO,EAAC,UAAU,EAAC,YAAY,EAAC,eAAe,CAAC,EAAC,CAAC,SAAS,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,WAAW,EAAC,QAAQ,EAAC,YAAY,EAAC,KAAK,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,MAAM,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,IAAI,CAAC,EAAC,KAAK,EAAC,EAAE,EAAC,KAAK,EAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val, v = val.toString().replace(/^[^.]*\\.?/, '').length, f = parseInt(val.toString().replace(/^[^.]*\\.?/, ''), 10) || 0;\n\nif (n % 10 === 0 || (n % 100 === Math.floor(n % 100) && (n % 100 >= 11 && n % 100 <= 19) || v === 2 && (f % 100 === Math.floor(f % 100) && (f % 100 >= 11 && f % 100 <= 19))))\n    return 0;\nif (n % 10 === 1 && !(n % 100 === 11) || (v === 2 && (f % 10 === 1 && !(f % 100 === 11)) || !(v === 2) && f % 10 === 1))\n    return 1;\nreturn 5;\n}\n\nexport default [\"lv\",[[\"priekšp.\",\"pēcp.\"],u,[\"priekšpusdienā\",\"pēcpusdienā\"]],[[\"priekšp.\",\"pēcpusd.\"],u,[\"priekšpusdiena\",\"pēcpusdiena\"]],[[\"S\",\"P\",\"O\",\"T\",\"C\",\"P\",\"S\"],[\"svētd.\",\"pirmd.\",\"otrd.\",\"trešd.\",\"ceturtd.\",\"piektd.\",\"sestd.\"],[\"svētdiena\",\"pirmdiena\",\"otrdiena\",\"trešdiena\",\"ceturtdiena\",\"piektdiena\",\"sestdiena\"],[\"Sv\",\"Pr\",\"Ot\",\"Tr\",\"Ce\",\"Pk\",\"Se\"]],[[\"S\",\"P\",\"O\",\"T\",\"C\",\"P\",\"S\"],[\"Svētd.\",\"Pirmd.\",\"Otrd.\",\"Trešd.\",\"Ceturtd.\",\"Piektd.\",\"Sestd.\"],[\"Svētdiena\",\"Pirmdiena\",\"Otrdiena\",\"Trešdiena\",\"Ceturtdiena\",\"Piektdiena\",\"Sestdiena\"],[\"Sv\",\"Pr\",\"Ot\",\"Tr\",\"Ce\",\"Pk\",\"Se\"]],[[\"J\",\"F\",\"M\",\"A\",\"M\",\"J\",\"J\",\"A\",\"S\",\"O\",\"N\",\"D\"],[\"janv.\",\"febr.\",\"marts\",\"apr.\",\"maijs\",\"jūn.\",\"jūl.\",\"aug.\",\"sept.\",\"okt.\",\"nov.\",\"dec.\"],[\"janvāris\",\"februāris\",\"marts\",\"aprīlis\",\"maijs\",\"jūnijs\",\"jūlijs\",\"augusts\",\"septembris\",\"oktobris\",\"novembris\",\"decembris\"]],u,[[\"p.m.ē.\",\"m.ē.\"],u,[\"pirms mūsu ēras\",\"mūsu ērā\"]],1,[6,0],[\"dd.MM.yy\",\"y. 'gada' d. MMM\",\"y. 'gada' d. MMMM\",\"EEEE, y. 'gada' d. MMMM\"],[\"HH:mm\",\"HH:mm:ss\",\"HH:mm:ss z\",\"HH:mm:ss zzzz\"],[\"{1} {0}\",u,u,u],[\",\",\" \",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NS\",\":\"],[\"#,##0.###\",\"#,##0%\",\"#,##0.00 ¤\",\"#E0\"],\"EUR\",\"€\",\"eiro\",{\"AUD\":[\"AU$\",\"$\"],\"BYN\":[u,\"р.\"],\"GHS\":[],\"LVL\":[\"Ls\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}