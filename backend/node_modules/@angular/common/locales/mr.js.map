{"version": 3, "file": "mr.js", "sourceRoot": "", "sources": ["mr.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,SAAS,MAAM,CAAC,GAAW;IAC3B,MAAM,CAAC,GAAG,GAAG,CAAC;IAEd,IAAI,CAAC,KAAK,CAAC;QACP,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;AACT,CAAC;AAED,eAAe,CAAC,IAAI,EAAC,CAAC,CAAC,GAAG,EAAC,GAAG,CAAC,EAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,EAAC,CAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,KAAK,CAAC,EAAC,CAAC,QAAQ,EAAC,QAAQ,EAAC,SAAS,EAAC,QAAQ,EAAC,SAAS,EAAC,UAAU,EAAC,QAAQ,CAAC,EAAC,CAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,CAAC,EAAC,CAAC,MAAM,EAAC,QAAQ,EAAC,OAAO,EAAC,OAAO,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,IAAI,EAAC,QAAQ,EAAC,OAAO,EAAC,SAAS,EAAC,OAAO,CAAC,EAAC,CAAC,UAAU,EAAC,YAAY,EAAC,OAAO,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,OAAO,EAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,SAAS,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,WAAW,EAAC,OAAO,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,QAAQ,EAAC,UAAU,EAAC,WAAW,EAAC,iBAAiB,CAAC,EAAC,CAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,gBAAgB,CAAC,EAAC,CAAC,UAAU,EAAC,CAAC,EAAC,cAAc,EAAC,CAAC,CAAC,EAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,KAAK,EAAC,GAAG,CAAC,EAAC,CAAC,cAAc,EAAC,QAAQ,EAAC,WAAW,EAAC,OAAO,CAAC,EAAC,KAAK,EAAC,GAAG,EAAC,cAAc,EAAC,EAAC,KAAK,EAAC,CAAC,KAAK,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,EAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,GAAG,CAAC,EAAC,KAAK,EAAC,CAAC,KAAK,CAAC,EAAC,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nfunction plural(val: number): number {\nconst n = val;\n\nif (n === 1)\n    return 1;\nreturn 5;\n}\n\nexport default [\"mr\",[[\"a\",\"p\"],[\"AM\",\"PM\"],u],[[\"AM\",\"PM\"],u,u],[[\"र\",\"सो\",\"मं\",\"बु\",\"गु\",\"शु\",\"श\"],[\"रवि\",\"सोम\",\"मंगळ\",\"बुध\",\"गुरु\",\"शुक्र\",\"शनि\"],[\"रविवार\",\"सोमवार\",\"मंगळवार\",\"बुधवार\",\"गुरुवार\",\"शुक्रवार\",\"शनिवार\"],[\"र\",\"सो\",\"मं\",\"बु\",\"गु\",\"शु\",\"श\"]],u,[[\"जा\",\"फे\",\"मा\",\"ए\",\"मे\",\"जू\",\"जु\",\"ऑ\",\"स\",\"ऑ\",\"नो\",\"डि\"],[\"जाने\",\"फेब्रु\",\"मार्च\",\"एप्रि\",\"मे\",\"जून\",\"जुलै\",\"ऑग\",\"सप्टें\",\"ऑक्टो\",\"नोव्हें\",\"डिसें\"],[\"जानेवारी\",\"फेब्रुवारी\",\"मार्च\",\"एप्रिल\",\"मे\",\"जून\",\"जुलै\",\"ऑगस्ट\",\"सप्टेंबर\",\"ऑक्टोबर\",\"नोव्हेंबर\",\"डिसेंबर\"]],u,[[\"इ. स. पू.\",\"इ. स.\"],u,[\"ईसवीसनपूर्व\",\"ईसवीसन\"]],0,[0,0],[\"d/M/yy\",\"d MMM, y\",\"d MMMM, y\",\"EEEE, d MMMM, y\"],[\"h:mm a\",\"h:mm:ss a\",\"h:mm:ss a z\",\"h:mm:ss a zzzz\"],[\"{1}, {0}\",u,\"{1} रोजी {0}\",u],[\".\",\",\",\";\",\"%\",\"+\",\"-\",\"E\",\"×\",\"‰\",\"∞\",\"NaN\",\":\"],[\"#,##,##0.###\",\"#,##0%\",\"¤#,##0.00\",\"[#E0]\"],\"INR\",\"₹\",\"भारतीय रुपया\",{\"JPY\":[\"JP¥\",\"¥\"],\"PHP\":[u,\"₱\"],\"THB\":[\"฿\"],\"TWD\":[\"NT$\"]},\"ltr\", plural];\n"]}