const bcrypt = require('bcryptjs');
const { pool } = require('../config/database');

async function resetDemoPasswords() {
  try {
    console.log('Resetting demo user passwords...');
    
    // Hash the password 'password123'
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash('password123', saltRounds);
    
    // Update all demo users with the new hashed password
    const [result] = await pool.execute(
      'UPDATE users SET password = ? WHERE email IN (?, ?, ?, ?, ?)',
      [
        hashedPassword,
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]
    );
    
    console.log(`✅ Updated ${result.affectedRows} user passwords`);
    console.log('All demo users now have password: password123');
    
    // Test the admin login
    const [users] = await pool.execute(
      'SELECT id, username, email, name, role FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (users.length > 0) {
      console.log('✅ Admin user found:', users[0]);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error resetting passwords:', error);
    process.exit(1);
  }
}

resetDemoPasswords();
