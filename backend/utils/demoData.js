// Demo data for when database is not available
const bcrypt = require('bcryptjs');

// Pre-hashed password for 'password123'
const hashedPassword = '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6ukx.LFvO.';

const demoUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: hashedPassword,
    name: 'System Administrator',
    role: 'Admin',
    created_at: '2024-01-15 09:00:00'
  },
  {
    id: 2,
    username: 'john_doe',
    email: '<EMAIL>',
    password: hashedPassword,
    name: '<PERSON>',
    role: 'User',
    created_at: '2024-01-15 09:15:00'
  },
  {
    id: 3,
    username: 'jane_smith',
    email: '<EMAIL>',
    password: hashedPassword,
    name: '<PERSON>',
    role: 'User',
    created_at: '2024-01-15 09:30:00'
  },
  {
    id: 4,
    username: 'mike_wilson',
    email: '<EMAIL>',
    password: hashedPassword,
    name: '<PERSON>',
    role: 'User',
    created_at: '2024-01-15 10:00:00'
  },
  {
    id: 5,
    username: 'sarah_johnson',
    email: '<EMAIL>',
    password: hashedPassword,
    name: 'Sarah Johnson',
    role: 'User',
    created_at: '2024-01-15 10:15:00'
  }
];

const demoTasks = [
  {
    id: 1,
    title: 'Setup Development Environment',
    description: 'Configure development tools, IDE, and project structure for the new application',
    assignee_id: 2,
    assignee_name: 'John Doe',
    assignee_email: '<EMAIL>',
    status: 'Closed',
    due_date: '2024-01-20 17:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-15 09:00:00',
    updated_at: '2024-01-20 16:30:00'
  },
  {
    id: 2,
    title: 'Design User Interface Mockups',
    description: 'Create wireframes and mockups for all major screens including user management and task views',
    assignee_id: 3,
    assignee_name: 'Jane Smith',
    assignee_email: '<EMAIL>',
    status: 'In Progress',
    due_date: '2024-01-25 12:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-16 10:00:00',
    updated_at: '2024-01-22 14:30:00'
  },
  {
    id: 3,
    title: 'Implement User Authentication',
    description: 'Develop secure login system with JWT tokens and role-based access control',
    assignee_id: 2,
    assignee_name: 'John Doe',
    assignee_email: '<EMAIL>',
    status: 'In Progress',
    due_date: '2024-01-30 15:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-17 11:00:00',
    updated_at: '2024-01-23 09:15:00'
  },
  {
    id: 4,
    title: 'Database Schema Design',
    description: 'Design and implement the complete database schema with all required tables and relationships',
    assignee_id: 4,
    assignee_name: 'Mike Wilson',
    assignee_email: '<EMAIL>',
    status: 'ToDo',
    due_date: '2024-02-05 10:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-18 14:00:00',
    updated_at: '2024-01-18 14:00:00'
  },
  {
    id: 5,
    title: 'API Development',
    description: 'Create RESTful APIs for all CRUD operations including users, tasks, comments, and linked items',
    assignee_id: 2,
    assignee_name: 'John Doe',
    assignee_email: '<EMAIL>',
    status: 'New',
    due_date: '2024-02-10 16:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-19 09:30:00',
    updated_at: '2024-01-19 09:30:00'
  },
  {
    id: 6,
    title: 'Frontend Components Development',
    description: 'Build React/Angular components for task management, user management, and dashboard',
    assignee_id: 3,
    assignee_name: 'Jane Smith',
    assignee_email: '<EMAIL>',
    status: 'New',
    due_date: '2024-02-15 14:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-20 13:00:00',
    updated_at: '2024-01-20 13:00:00'
  },
  {
    id: 7,
    title: 'Testing and Quality Assurance',
    description: 'Comprehensive testing of all features including unit tests, integration tests, and user acceptance testing',
    assignee_id: 5,
    assignee_name: 'Sarah Johnson',
    assignee_email: '<EMAIL>',
    status: 'New',
    due_date: '2024-02-20 18:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-21 10:30:00',
    updated_at: '2024-01-21 10:30:00'
  },
  {
    id: 8,
    title: 'Documentation and Deployment',
    description: 'Create user documentation, API documentation, and deploy to production environment',
    assignee_id: 4,
    assignee_name: 'Mike Wilson',
    assignee_email: '<EMAIL>',
    status: 'New',
    due_date: '2024-02-25 12:00:00',
    created_by: 1,
    creator_name: 'System Administrator',
    creator_email: '<EMAIL>',
    created_at: '2024-01-22 15:45:00',
    updated_at: '2024-01-22 15:45:00'
  },
  {
    id: 9,
    title: 'Bug Fix: Login Issue',
    description: 'Fix the intermittent login timeout issue reported by users',
    assignee_id: 2,
    assignee_name: 'John Doe',
    assignee_email: '<EMAIL>',
    status: 'Blocked',
    due_date: '2024-01-22 09:00:00',
    created_by: 3,
    creator_name: 'Jane Smith',
    creator_email: '<EMAIL>',
    created_at: '2024-01-20 11:00:00',
    updated_at: '2024-01-21 16:20:00'
  },
  {
    id: 10,
    title: 'Feature Request: Dark Mode',
    description: 'Implement dark mode theme option for better user experience',
    assignee_id: 3,
    assignee_name: 'Jane Smith',
    assignee_email: '<EMAIL>',
    status: 'ToDo',
    due_date: '2024-02-01 16:00:00',
    created_by: 2,
    creator_name: 'John Doe',
    creator_email: '<EMAIL>',
    created_at: '2024-01-21 14:20:00',
    updated_at: '2024-01-21 14:20:00'
  }
];

const demoComments = [
  {
    id: 1,
    task_id: 1,
    user_id: 2,
    author_name: 'John Doe',
    author_email: '<EMAIL>',
    content: 'Environment setup completed successfully. All tools are configured and working properly.',
    timestamp: '2024-01-20 16:00:00'
  },
  {
    id: 2,
    task_id: 1,
    user_id: 1,
    author_name: 'System Administrator',
    author_email: '<EMAIL>',
    content: 'Great work! Please proceed with the next phase.',
    timestamp: '2024-01-20 16:15:00'
  },
  {
    id: 3,
    task_id: 2,
    user_id: 3,
    author_name: 'Jane Smith',
    author_email: '<EMAIL>',
    content: 'Initial mockups are ready for review. Shared the designs in the project folder.',
    timestamp: '2024-01-22 14:00:00'
  },
  {
    id: 4,
    task_id: 2,
    user_id: 1,
    author_name: 'System Administrator',
    author_email: '<EMAIL>',
    content: 'Mockups look good. Please incorporate the feedback from the client meeting.',
    timestamp: '2024-01-22 14:30:00'
  },
  {
    id: 5,
    task_id: 3,
    user_id: 2,
    author_name: 'John Doe',
    author_email: '<EMAIL>',
    content: 'JWT implementation is complete. Working on role-based access control now.',
    timestamp: '2024-01-23 09:00:00'
  },
  {
    id: 6,
    task_id: 3,
    user_id: 1,
    author_name: 'System Administrator',
    author_email: '<EMAIL>',
    content: 'Make sure to test with different user roles thoroughly.',
    timestamp: '2024-01-23 09:15:00'
  },
  {
    id: 7,
    task_id: 9,
    user_id: 2,
    author_name: 'John Doe',
    author_email: '<EMAIL>',
    content: 'Investigating the root cause. Seems to be related to session timeout configuration.',
    timestamp: '2024-01-21 16:00:00'
  },
  {
    id: 8,
    task_id: 9,
    user_id: 1,
    author_name: 'System Administrator',
    author_email: '<EMAIL>',
    content: 'This is blocking several users. Please prioritize this fix.',
    timestamp: '2024-01-21 16:20:00'
  },
  {
    id: 9,
    task_id: 10,
    user_id: 3,
    author_name: 'Jane Smith',
    author_email: '<EMAIL>',
    content: 'Researching best practices for dark mode implementation.',
    timestamp: '2024-01-21 14:30:00'
  },
  {
    id: 10,
    task_id: 10,
    user_id: 2,
    author_name: 'John Doe',
    author_email: '<EMAIL>',
    content: 'Consider using CSS custom properties for theme switching.',
    timestamp: '2024-01-21 15:00:00'
  }
];

const demoLinkedItems = [
  {
    id: 1,
    task_id: 1,
    link: 'https://docs.company.com/dev-setup',
    title: 'Development Setup Guide',
    created_at: '2024-01-15 09:30:00'
  },
  {
    id: 2,
    task_id: 1,
    link: 'https://github.com/company/project-template',
    title: 'Project Template Repository',
    created_at: '2024-01-15 09:35:00'
  },
  {
    id: 3,
    task_id: 2,
    link: 'https://figma.com/project-mockups',
    title: 'UI Mockups - Figma',
    created_at: '2024-01-16 10:15:00'
  },
  {
    id: 4,
    task_id: 2,
    link: 'https://docs.company.com/design-system',
    title: 'Company Design System',
    created_at: '2024-01-16 10:20:00'
  },
  {
    id: 5,
    task_id: 3,
    link: 'https://jwt.io/introduction/',
    title: 'JWT Introduction Documentation',
    created_at: '2024-01-17 11:30:00'
  },
  {
    id: 6,
    task_id: 3,
    link: 'https://docs.company.com/security-guidelines',
    title: 'Security Implementation Guidelines',
    created_at: '2024-01-17 11:35:00'
  },
  {
    id: 7,
    task_id: 4,
    link: 'https://dbdiagram.io/project-schema',
    title: 'Database Schema Diagram',
    created_at: '2024-01-18 14:30:00'
  },
  {
    id: 8,
    task_id: 9,
    link: 'https://github.com/company/project/issues/123',
    title: 'GitHub Issue #123',
    created_at: '2024-01-20 11:15:00'
  },
  {
    id: 9,
    task_id: 9,
    link: 'https://logs.company.com/login-errors',
    title: 'Login Error Logs',
    created_at: '2024-01-20 11:20:00'
  },
  {
    id: 10,
    task_id: 10,
    link: 'https://web.dev/prefers-color-scheme/',
    title: 'Dark Mode Best Practices',
    created_at: '2024-01-21 14:45:00'
  }
];

const demoLoginLogs = [
  {
    id: 1,
    user_id: 1,
    timestamp: '2024-01-15 09:00:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    success: true
  },
  {
    id: 2,
    user_id: 2,
    timestamp: '2024-01-15 09:15:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    success: true
  },
  {
    id: 3,
    user_id: 3,
    timestamp: '2024-01-15 09:30:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    success: true
  },
  {
    id: 4,
    user_id: 1,
    timestamp: '2024-01-15 14:00:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    success: true
  },
  {
    id: 5,
    user_id: 4,
    timestamp: '2024-01-15 10:00:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    success: true
  },
  {
    id: 6,
    user_id: 5,
    timestamp: '2024-01-15 10:15:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15',
    success: true
  },
  {
    id: 7,
    user_id: 2,
    timestamp: '2024-01-22 08:30:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    success: false
  },
  {
    id: 8,
    user_id: 2,
    timestamp: '2024-01-22 08:32:00',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    success: true
  }
];

module.exports = {
  demoUsers,
  demoTasks,
  demoComments,
  demoLinkedItems,
  demoLoginLogs
};
