{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mysql", "api"], "author": "", "license": "ISC", "description": "Backend API for Task Management Application", "dependencies": {"@angular/animations": "^20.1.2", "@angular/cdk": "^20.1.2", "@angular/material": "^20.1.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2"}, "devDependencies": {"nodemon": "^3.1.10"}}