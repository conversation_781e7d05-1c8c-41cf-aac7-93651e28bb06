"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseTsconfigFile = exports.TsconfigParseError = void 0;
const ts = require("typescript");
const virtual_host_1 = require("./virtual-host");
const path_1 = require("path");
const diagnostics_1 = require("./diagnostics");
/** Code of the error raised by TypeScript when a tsconfig doesn't match any files. */
const NO_INPUTS_ERROR_CODE = 18003;
/** Class capturing a tsconfig parse error. */
class TsconfigParseError extends Error {
}
exports.TsconfigParseError = TsconfigParseError;
/**
 * Attempts to parse the specified tsconfig file.
 *
 * @throws {TsconfigParseError} If the tsconfig could not be read or parsed.
 */
function parseTsconfigFile(tsconfigPath, fileSystem) {
    if (!fileSystem.fileExists(tsconfigPath)) {
        throw new TsconfigParseError(`Tsconfig cannot not be read: ${tsconfigPath}`);
    }
    const { config, error } = ts.readConfigFile(tsconfigPath, p => fileSystem.read(fileSystem.resolve(p)));
    // If there is a config reading error, we never attempt to parse the config.
    if (error) {
        throw new TsconfigParseError((0, diagnostics_1.formatDiagnostics)([error], fileSystem));
    }
    const parsed = ts.parseJsonConfigFileContent(config, new virtual_host_1.FileSystemHost(fileSystem), (0, path_1.dirname)(tsconfigPath), {});
    // Skip the "No inputs found..." error since we don't want to interrupt the migration if a
    // tsconfig doesn't match a file. This will result in an empty `Program` which is still valid.
    const errors = parsed.errors.filter(diag => diag.code !== NO_INPUTS_ERROR_CODE);
    if (errors.length) {
        throw new TsconfigParseError((0, diagnostics_1.formatDiagnostics)(errors, fileSystem));
    }
    return parsed;
}
exports.parseTsconfigFile = parseTsconfigFile;
//# sourceMappingURL=data:application/json;base64,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