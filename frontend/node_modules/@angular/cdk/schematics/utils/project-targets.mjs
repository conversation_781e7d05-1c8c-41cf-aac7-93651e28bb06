"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectTestTargets = exports.getProjectBuildTargets = exports.getProjectTargetOptions = void 0;
const schematics_1 = require("@angular-devkit/schematics");
/** Resolves the architect options for the build target of the given project. */
function getProjectTargetOptions(project, buildTarget) {
    const options = project.targets?.get(buildTarget)?.options;
    if (!options) {
        throw new schematics_1.SchematicsException(`Cannot determine project target configuration for: ${buildTarget}.`);
    }
    return options;
}
exports.getProjectTargetOptions = getProjectTargetOptions;
/** Gets all of the default CLI-provided build targets in a project. */
function getProjectBuildTargets(project) {
    return getTargetsByBuilderName(project, builder => builder === '@angular-devkit/build-angular:application' ||
        builder === '@angular-devkit/build-angular:browser' ||
        builder === '@angular-devkit/build-angular:browser-esbuild');
}
exports.getProjectBuildTargets = getProjectBuildTargets;
/** Gets all of the default CLI-provided testing targets in a project. */
function getProjectTestTargets(project) {
    return getTargetsByBuilderName(project, builder => builder === '@angular-devkit/build-angular:karma');
}
exports.getProjectTestTargets = getProjectTestTargets;
/** Gets all targets from the given project that pass a predicate check. */
function getTargetsByBuilderName(project, predicate) {
    return Array.from(project.targets.keys())
        .filter(name => predicate(project.targets.get(name)?.builder))
        .map(name => project.targets.get(name));
}
//# sourceMappingURL=data:application/json;base64,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