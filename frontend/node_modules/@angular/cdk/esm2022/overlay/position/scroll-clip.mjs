/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Gets whether an element is scrolled outside of view by any of its parent scrolling containers.
 * @param element Dimensions of the element (from getBoundingClientRect)
 * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)
 * @returns Whether the element is scrolled out of view
 * @docs-private
 */
export function isElementScrolledOutsideView(element, scrollContainers) {
    return scrollContainers.some(containerBounds => {
        const outsideAbove = element.bottom < containerBounds.top;
        const outsideBelow = element.top > containerBounds.bottom;
        const outsideLeft = element.right < containerBounds.left;
        const outsideRight = element.left > containerBounds.right;
        return outsideAbove || outsideBelow || outsideLeft || outsideRight;
    });
}
/**
 * Gets whether an element is clipped by any of its scrolling containers.
 * @param element Dimensions of the element (from getBoundingClientRect)
 * @param scrollContainers Dimensions of element's scrolling containers (from getBoundingClientRect)
 * @returns Whether the element is clipped
 * @docs-private
 */
export function isElementClippedByScrolling(element, scrollContainers) {
    return scrollContainers.some(scrollContainerRect => {
        const clippedAbove = element.top < scrollContainerRect.top;
        const clippedBelow = element.bottom > scrollContainerRect.bottom;
        const clippedLeft = element.left < scrollContainerRect.left;
        const clippedRight = element.right > scrollContainerRect.right;
        return clippedAbove || clippedBelow || clippedLeft || clippedRight;
    });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoic2Nyb2xsLWNsaXAuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9zcmMvY2RrL292ZXJsYXkvcG9zaXRpb24vc2Nyb2xsLWNsaXAudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBUUg7Ozs7OztHQU1HO0FBQ0gsTUFBTSxVQUFVLDRCQUE0QixDQUFDLE9BQW1CLEVBQUUsZ0JBQThCO0lBQzlGLE9BQU8sZ0JBQWdCLENBQUMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxFQUFFO1FBQzdDLE1BQU0sWUFBWSxHQUFHLE9BQU8sQ0FBQyxNQUFNLEdBQUcsZUFBZSxDQUFDLEdBQUcsQ0FBQztRQUMxRCxNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsR0FBRyxHQUFHLGVBQWUsQ0FBQyxNQUFNLENBQUM7UUFDMUQsTUFBTSxXQUFXLEdBQUcsT0FBTyxDQUFDLEtBQUssR0FBRyxlQUFlLENBQUMsSUFBSSxDQUFDO1FBQ3pELE1BQU0sWUFBWSxHQUFHLE9BQU8sQ0FBQyxJQUFJLEdBQUcsZUFBZSxDQUFDLEtBQUssQ0FBQztRQUUxRCxPQUFPLFlBQVksSUFBSSxZQUFZLElBQUksV0FBVyxJQUFJLFlBQVksQ0FBQztJQUNyRSxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUM7QUFFRDs7Ozs7O0dBTUc7QUFDSCxNQUFNLFVBQVUsMkJBQTJCLENBQUMsT0FBbUIsRUFBRSxnQkFBOEI7SUFDN0YsT0FBTyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsbUJBQW1CLENBQUMsRUFBRTtRQUNqRCxNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsR0FBRyxHQUFHLG1CQUFtQixDQUFDLEdBQUcsQ0FBQztRQUMzRCxNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsTUFBTSxHQUFHLG1CQUFtQixDQUFDLE1BQU0sQ0FBQztRQUNqRSxNQUFNLFdBQVcsR0FBRyxPQUFPLENBQUMsSUFBSSxHQUFHLG1CQUFtQixDQUFDLElBQUksQ0FBQztRQUM1RCxNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsS0FBSyxHQUFHLG1CQUFtQixDQUFDLEtBQUssQ0FBQztRQUUvRCxPQUFPLFlBQVksSUFBSSxZQUFZLElBQUksV0FBVyxJQUFJLFlBQVksQ0FBQztJQUNyRSxDQUFDLENBQUMsQ0FBQztBQUNMLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuLy8gVE9ETyhqZWxib3Vybik6IG1vdmUgdGhpcyB0byBsaXZlIHdpdGggdGhlIHJlc3Qgb2YgdGhlIHNjcm9sbGluZyBjb2RlXG4vLyBUT0RPKGplbGJvdXJuKTogc29tZWRheSByZXBsYWNlIHRoaXMgd2l0aCBJbnRlcnNlY3Rpb25PYnNlcnZlcnNcblxuLyoqIEVxdWl2YWxlbnQgb2YgYERPTVJlY3RgIHdpdGhvdXQgc29tZSBvZiB0aGUgcHJvcGVydGllcyB3ZSBkb24ndCBjYXJlIGFib3V0LiAqL1xudHlwZSBEaW1lbnNpb25zID0gT21pdDxET01SZWN0LCAneCcgfCAneScgfCAndG9KU09OJz47XG5cbi8qKlxuICogR2V0cyB3aGV0aGVyIGFuIGVsZW1lbnQgaXMgc2Nyb2xsZWQgb3V0c2lkZSBvZiB2aWV3IGJ5IGFueSBvZiBpdHMgcGFyZW50IHNjcm9sbGluZyBjb250YWluZXJzLlxuICogQHBhcmFtIGVsZW1lbnQgRGltZW5zaW9ucyBvZiB0aGUgZWxlbWVudCAoZnJvbSBnZXRCb3VuZGluZ0NsaWVudFJlY3QpXG4gKiBAcGFyYW0gc2Nyb2xsQ29udGFpbmVycyBEaW1lbnNpb25zIG9mIGVsZW1lbnQncyBzY3JvbGxpbmcgY29udGFpbmVycyAoZnJvbSBnZXRCb3VuZGluZ0NsaWVudFJlY3QpXG4gKiBAcmV0dXJucyBXaGV0aGVyIHRoZSBlbGVtZW50IGlzIHNjcm9sbGVkIG91dCBvZiB2aWV3XG4gKiBAZG9jcy1wcml2YXRlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0VsZW1lbnRTY3JvbGxlZE91dHNpZGVWaWV3KGVsZW1lbnQ6IERpbWVuc2lvbnMsIHNjcm9sbENvbnRhaW5lcnM6IERpbWVuc2lvbnNbXSkge1xuICByZXR1cm4gc2Nyb2xsQ29udGFpbmVycy5zb21lKGNvbnRhaW5lckJvdW5kcyA9PiB7XG4gICAgY29uc3Qgb3V0c2lkZUFib3ZlID0gZWxlbWVudC5ib3R0b20gPCBjb250YWluZXJCb3VuZHMudG9wO1xuICAgIGNvbnN0IG91dHNpZGVCZWxvdyA9IGVsZW1lbnQudG9wID4gY29udGFpbmVyQm91bmRzLmJvdHRvbTtcbiAgICBjb25zdCBvdXRzaWRlTGVmdCA9IGVsZW1lbnQucmlnaHQgPCBjb250YWluZXJCb3VuZHMubGVmdDtcbiAgICBjb25zdCBvdXRzaWRlUmlnaHQgPSBlbGVtZW50LmxlZnQgPiBjb250YWluZXJCb3VuZHMucmlnaHQ7XG5cbiAgICByZXR1cm4gb3V0c2lkZUFib3ZlIHx8IG91dHNpZGVCZWxvdyB8fCBvdXRzaWRlTGVmdCB8fCBvdXRzaWRlUmlnaHQ7XG4gIH0pO1xufVxuXG4vKipcbiAqIEdldHMgd2hldGhlciBhbiBlbGVtZW50IGlzIGNsaXBwZWQgYnkgYW55IG9mIGl0cyBzY3JvbGxpbmcgY29udGFpbmVycy5cbiAqIEBwYXJhbSBlbGVtZW50IERpbWVuc2lvbnMgb2YgdGhlIGVsZW1lbnQgKGZyb20gZ2V0Qm91bmRpbmdDbGllbnRSZWN0KVxuICogQHBhcmFtIHNjcm9sbENvbnRhaW5lcnMgRGltZW5zaW9ucyBvZiBlbGVtZW50J3Mgc2Nyb2xsaW5nIGNvbnRhaW5lcnMgKGZyb20gZ2V0Qm91bmRpbmdDbGllbnRSZWN0KVxuICogQHJldHVybnMgV2hldGhlciB0aGUgZWxlbWVudCBpcyBjbGlwcGVkXG4gKiBAZG9jcy1wcml2YXRlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0VsZW1lbnRDbGlwcGVkQnlTY3JvbGxpbmcoZWxlbWVudDogRGltZW5zaW9ucywgc2Nyb2xsQ29udGFpbmVyczogRGltZW5zaW9uc1tdKSB7XG4gIHJldHVybiBzY3JvbGxDb250YWluZXJzLnNvbWUoc2Nyb2xsQ29udGFpbmVyUmVjdCA9PiB7XG4gICAgY29uc3QgY2xpcHBlZEFib3ZlID0gZWxlbWVudC50b3AgPCBzY3JvbGxDb250YWluZXJSZWN0LnRvcDtcbiAgICBjb25zdCBjbGlwcGVkQmVsb3cgPSBlbGVtZW50LmJvdHRvbSA+IHNjcm9sbENvbnRhaW5lclJlY3QuYm90dG9tO1xuICAgIGNvbnN0IGNsaXBwZWRMZWZ0ID0gZWxlbWVudC5sZWZ0IDwgc2Nyb2xsQ29udGFpbmVyUmVjdC5sZWZ0O1xuICAgIGNvbnN0IGNsaXBwZWRSaWdodCA9IGVsZW1lbnQucmlnaHQgPiBzY3JvbGxDb250YWluZXJSZWN0LnJpZ2h0O1xuXG4gICAgcmV0dXJuIGNsaXBwZWRBYm92ZSB8fCBjbGlwcGVkQmVsb3cgfHwgY2xpcHBlZExlZnQgfHwgY2xpcHBlZFJpZ2h0O1xuICB9KTtcbn1cbiJdfQ==