@use '@material/button/button-text-theme' as mdc-button-text-theme;
@use '@material/button/button-filled-theme' as mdc-button-filled-theme;
@use '@material/button/button-protected-theme' as mdc-button-protected-theme;
@use '@material/button/button-outlined-theme' as mdc-button-outlined-theme;

@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/filled-button' as tokens-mdc-filled-button;
@use '../core/tokens/m2/mat/filled-button' as tokens-mat-filled-button;
@use '../core/tokens/m2/mdc/outlined-button' as tokens-mdc-outlined-button;
@use '../core/tokens/m2/mat/outlined-button' as tokens-mat-outlined-button;
@use '../core/tokens/m2/mdc/protected-button' as tokens-mdc-protected-button;
@use '../core/tokens/m2/mat/protected-button' as tokens-mat-protected-button;
@use '../core/tokens/m2/mdc/text-button' as tokens-mdc-text-button;
@use '../core/tokens/m2/mat/text-button' as tokens-mat-text-button;
@use '../core/style/sass-utils';

@mixin _text-button-variant($theme, $palette) {
  $mdc-tokens: if($palette,
    tokens-mdc-text-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mdc-text-button.get-color-tokens($theme)
  );

  $mat-tokens: if($palette,
    tokens-mat-text-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mat-text-button.get-color-tokens($theme)
  );

  @include mdc-button-text-theme.theme($mdc-tokens);
  @include token-utils.create-token-values(tokens-mat-text-button.$prefix, $mat-tokens);
}

@mixin _filled-button-variant($theme, $palette) {
  $mdc-tokens: if($palette,
    tokens-mdc-filled-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mdc-filled-button.get-color-tokens($theme)
  );

  $mat-tokens: if($palette,
    tokens-mat-filled-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mat-filled-button.get-color-tokens($theme)
  );

  @include mdc-button-filled-theme.theme($mdc-tokens);
  @include token-utils.create-token-values(tokens-mat-filled-button.$prefix, $mat-tokens);
}

@mixin _protected-button-variant($theme, $palette) {
  $mdc-tokens: if($palette,
    tokens-mdc-protected-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mdc-protected-button.get-color-tokens($theme)
  );

  $mat-tokens: if($palette,
    tokens-mat-protected-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mat-protected-button.get-color-tokens($theme)
  );

  @include mdc-button-protected-theme.theme($mdc-tokens);
  @include token-utils.create-token-values(tokens-mat-protected-button.$prefix, $mat-tokens);
}

@mixin _outlined-button-variant($theme, $palette) {
  $mdc-tokens: if($palette,
    tokens-mdc-outlined-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mdc-outlined-button.get-color-tokens($theme)
  );

  $mat-tokens: if($palette,
    tokens-mat-outlined-button.private-get-color-palette-color-tokens($theme, $palette),
    tokens-mat-outlined-button.get-color-tokens($theme)
  );

  @include mdc-button-outlined-theme.theme($mdc-tokens);
  @include token-utils.create-token-values(tokens-mat-outlined-button.$prefix, $mat-tokens);
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mdc-text-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mdc-text-button.$prefix, $options...);
  $mdc-protected-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mdc-protected-button.$prefix, $options...);
  $mdc-filled-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mdc-filled-button.$prefix, $options...);
  $mdc-outlined-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mdc-outlined-button.$prefix, $options...);
  $mat-text-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mat-text-button.$prefix, $options...);
  $mat-protected-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mat-protected-button.$prefix, $options...);
  $mat-filled-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mat-filled-button.$prefix, $options...);
  $mat-outlined-button-tokens:
      token-utils.get-tokens-for($tokens, tokens-mat-outlined-button.$prefix, $options...);
  @include mdc-button-text-theme.theme($mdc-text-button-tokens);
  @include mdc-button-protected-theme.theme($mdc-protected-button-tokens);
  @include mdc-button-filled-theme.theme($mdc-filled-button-tokens);
  @include mdc-button-outlined-theme.theme($mdc-outlined-button-tokens);
  @include token-utils.create-token-values(tokens-mat-text-button.$prefix, $mat-text-button-tokens);
  @include token-utils.create-token-values(
      tokens-mat-protected-button.$prefix, $mat-protected-button-tokens);
  @include token-utils.create-token-values(
      tokens-mat-filled-button.$prefix, $mat-filled-button-tokens);
  @include token-utils.create-token-values(
      tokens-mat-outlined-button.$prefix, $mat-outlined-button-tokens);
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-button.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-button-text-theme.theme(
        tokens-mdc-text-button.get-unthemable-tokens());
      @include mdc-button-filled-theme.theme(
        tokens-mdc-filled-button.get-unthemable-tokens());
      @include mdc-button-protected-theme.theme(
        tokens-mdc-protected-button.get-unthemable-tokens());
      @include mdc-button-outlined-theme.theme(
        tokens-mdc-outlined-button.get-unthemable-tokens());

      @include token-utils.create-token-values(tokens-mat-text-button.$prefix,
        tokens-mat-text-button.get-unthemable-tokens());
      @include token-utils.create-token-values(tokens-mat-filled-button.$prefix,
        tokens-mat-filled-button.get-unthemable-tokens());
      @include token-utils.create-token-values(tokens-mat-protected-button.$prefix,
        tokens-mat-protected-button.get-unthemable-tokens());
      @include token-utils.create-token-values(tokens-mat-outlined-button.$prefix,
        tokens-mat-outlined-button.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-button.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the button: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include _text-button-variant($theme, null);
      @include _filled-button-variant($theme, null);
      @include _protected-button-variant($theme, null);
      @include _outlined-button-variant($theme, null);
    }

    .mat-mdc-button {
      &.mat-primary {
        @include _text-button-variant($theme, primary);
      }

      &.mat-accent {
        @include _text-button-variant($theme, accent);
      }

      &.mat-warn {
        @include _text-button-variant($theme, warn);
      }
    }

    .mat-mdc-unelevated-button {
      &.mat-primary {
        @include _filled-button-variant($theme, primary);
      }

      &.mat-accent {
        @include _filled-button-variant($theme, accent);
      }

      &.mat-warn {
        @include _filled-button-variant($theme, warn);
      }
    }

    .mat-mdc-raised-button {
      &.mat-primary {
        @include _protected-button-variant($theme, primary);
      }

      &.mat-accent {
        @include _protected-button-variant($theme, accent);
      }

      &.mat-warn {
        @include _protected-button-variant($theme, warn);
      }
    }

    .mat-mdc-outlined-button {
      &.mat-primary {
        @include _outlined-button-variant($theme, primary);
      }

      &.mat-accent {
        @include _outlined-button-variant($theme, accent);
      }

      &.mat-warn {
        @include _outlined-button-variant($theme, warn);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-button.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-button-text-theme.theme(
        tokens-mdc-text-button.get-typography-tokens($theme));
      @include mdc-button-filled-theme.theme(
        tokens-mdc-filled-button.get-typography-tokens($theme));
      @include mdc-button-outlined-theme.theme(
        tokens-mdc-outlined-button.get-typography-tokens($theme));
      @include mdc-button-protected-theme.theme(
        tokens-mdc-protected-button.get-typography-tokens($theme));

      @include token-utils.create-token-values(tokens-mat-text-button.$prefix,
        tokens-mat-text-button.get-typography-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-filled-button.$prefix,
        tokens-mat-filled-button.get-typography-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-protected-button.$prefix,
        tokens-mat-protected-button.get-typography-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-outlined-button.$prefix,
        tokens-mat-outlined-button.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-button.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-button-text-theme.theme(
        tokens-mdc-text-button.get-density-tokens($theme));
      @include mdc-button-filled-theme.theme(
        tokens-mdc-filled-button.get-density-tokens($theme));
      @include mdc-button-outlined-theme.theme(
        tokens-mdc-outlined-button.get-density-tokens($theme));
      @include mdc-button-protected-theme.theme(
        tokens-mdc-protected-button.get-density-tokens($theme));

      @include token-utils.create-token-values(tokens-mat-text-button.$prefix,
        tokens-mat-text-button.get-density-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-filled-button.$prefix,
        tokens-mat-filled-button.get-density-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-protected-button.$prefix,
        tokens-mat-protected-button.get-density-tokens($theme));
      @include token-utils.create-token-values(tokens-mat-outlined-button.$prefix,
        tokens-mat-outlined-button.get-density-tokens($theme));
    }
  }
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-button.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the button: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-button') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
