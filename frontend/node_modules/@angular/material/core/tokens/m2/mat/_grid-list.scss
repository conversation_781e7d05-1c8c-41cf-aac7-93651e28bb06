@use '../../token-utils';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, grid-list);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // TODO(crisbeto): other components have tokens for all typography dimensions.
    // Here we only set the font size to maintain the same appearance as the pre-tokens
    // theming API. Consider adding more tokens for letter spacing, font weight etc.
    tile-header-primary-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    tile-header-secondary-text-size: inspection.get-theme-typography($theme, caption, font-size),
    tile-footer-primary-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    tile-footer-secondary-text-size: inspection.get-theme-typography($theme, caption, font-size),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
