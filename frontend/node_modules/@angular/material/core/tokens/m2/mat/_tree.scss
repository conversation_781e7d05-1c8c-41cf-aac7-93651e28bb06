@use 'sass:map';
@use '../../token-utils';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, tree);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $foreground-base: inspection.get-theme-color($theme, foreground, base);

  @return (
    container-background-color: inspection.get-theme-color($theme, background, card),
    node-text-color: inspection.get-theme-color($theme, foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    node-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    node-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    node-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),

    // TODO(crisbeto): provide tokens for line height and letter spacing to match other components.
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $min-height-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
    -4: 28px,
  );

  @return (
    node-min-height: map.get($min-height-scale, $density-scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
