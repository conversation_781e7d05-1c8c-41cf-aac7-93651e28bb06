@use 'sass:map';
@use '../../token-utils';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, toolbar);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return private-get-color-palette-color-tokens(
    $background-color: inspection.get-theme-color($theme, background, app-bar),
    $text-color: inspection.get-theme-color($theme, foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    title-text-font: inspection.get-theme-typography($theme, headline-6, font-family),
    title-text-line-height: inspection.get-theme-typography($theme, headline-6, line-height),
    title-text-size: inspection.get-theme-typography($theme, headline-6, font-size),
    title-text-tracking: inspection.get-theme-typography($theme, headline-6, letter-spacing),
    title-text-weight: inspection.get-theme-typography($theme, headline-6, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -3);
  $standard-scale: (
    0: 64px,
    -1: 60px,
    -2: 56px,
    -3: 52px,
  );

  $mobile-scale: (
    0: 56px,
    -1: 52px,
    -2: 48px,
    -3: 44px,
  );

  @return (
    standard-height: map.get($standard-scale, $density-scale),
    mobile-height: map.get($mobile-scale, $density-scale),
  );
}

// Generates the tokens used to theme the toolbar based on a palette.
@function private-get-color-palette-color-tokens($background-color, $text-color) {
  @return (
    container-background-color: $background-color,
    container-text-color: $text-color,
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
