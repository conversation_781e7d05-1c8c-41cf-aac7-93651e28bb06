@use 'sass:map';
@use 'sass:meta';
@use '@material/ripple/ripple-theme' as mdc-ripple-theme;
@use '../../token-utils';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../../mdc-helpers/mdc-helpers';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, filled-button);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // Start/end padding of the button.
    horizontal-padding: 16px,

    // Space between the icon and the button's main content.
    icon-spacing: 8px,

    // Amount by which to offset the icon so that its presence
    // doesn't increase throw off the horizontal padding.
    icon-offset: -4px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $ripple-opacities: if($is-dark,
    mdc-ripple-theme.$light-ink-opacities,
    mdc-ripple-theme.$dark-ink-opacities
  );

  @return (
    // Color of the element that shows the hover, focus and pressed states.
    state-layer-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the element that shows the hover, focus and pressed states while disabled.
    disabled-state-layer-color: inspection.get-theme-color($theme, foreground, base),

    // Color of the ripple element.
    ripple-color: inspection.get-theme-color($theme, foreground, base, 0.1),

    // Opacity of the ripple when the button is hovered.
    hover-state-layer-opacity: map.get($ripple-opacities, hover),

    // Opacity of the ripple when the button is focused.
    focus-state-layer-opacity: map.get($ripple-opacities, focus),

    // Opacity of the ripple when the button is pressed.
    pressed-state-layer-opacity: map.get($ripple-opacities, press),
  );
}

// Generates the mapping for the properties that change based on the button palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $state-layer-color: null;
  $ripple-color: null;
  $contrast-color: inspection.get-theme-color($theme, $palette-name, default-contrast);

  // Ideally we would derive all values directly from the theme, but it causes a lot of regressions
  // internally. For now we fall back to the old hardcoded behavior only for internal apps.
  @if (token-utils.$private-is-internal-build or meta.type-of($contrast-color) != 'color') {
    $is-dark: inspection.get-theme-type($theme) == dark;
    $container-color: inspection.get-theme-color($theme, $palette-name);
    $contrast-tone: mdc-helpers.variable-safe-contrast-tone($container-color, $is-dark);
    $color: if($contrast-tone == 'dark', #000, #fff);
    $state-layer-color: $color;
    $ripple-color: rgba($color, 0.1);
  }
  @else {
    $state-layer-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 1);
    $ripple-color: inspection.get-theme-color($theme, $palette-name, default-contrast, 0.1);
  }

  @return (
    state-layer-color: $state-layer-color,
    ripple-color: $ripple-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    touch-target-display: if($density-scale < -1, none, block),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
