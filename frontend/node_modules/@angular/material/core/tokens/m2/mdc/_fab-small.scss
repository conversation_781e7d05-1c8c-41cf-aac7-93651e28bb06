@use '../../../theming/inspection';
@use '../../token-utils';

@use 'sass:map';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, fab-small);

@function get-unthemable-tokens() {
  @return (
    container-shape: 50%,
    icon-size: 24px,

    // We don't use this token, because it doesn't set the color of any text inside the FAB.
    // We create a custom token for it instead.
    icon-color: null,

    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    container-height: null,
    container-width: null,

    focus-icon-color: null,
    focus-outline-color: null,
    focus-outline-width: null,
    focus-state-layer-color: null,
    focus-state-layer-opacity: null,

    hover-icon-color: null,
    hover-state-layer-color: null,
    hover-state-layer-opacity: null,

    lowered-container-elevation: null,
    lowered-focus-container-elevation: null,
    lowered-hover-container-elevation: null,
    lowered-pressed-container-elevation: null,

    pressed-icon-color: null,
    pressed-ripple-color: null,
    pressed-ripple-opacity: null,
    pressed-state-layer-color: null,
    pressed-state-layer-opacity: null
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    // Background color of the FAB.
    container-color: inspection.get-theme-color($theme, background, card),
    container-elevation: 6,
    focus-container-elevation: 8,
    hover-container-elevation: 8,
    pressed-container-elevation: 12,
    container-shadow-color: #000,
  );
}

// Generates the mapping for the properties that change based on the FAB palette color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  @return (
    container-color: inspection.get-theme-color($theme, $palette-name, default),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return map.merge(
    get-unthemable-tokens(),
    map.merge(
      get-color-tokens(token-utils.$placeholder-color-config),
      map.merge(
        get-typography-tokens(token-utils.$placeholder-typography-config),
        get-density-tokens(token-utils.$placeholder-density-config)
      )
    )
  );
}
