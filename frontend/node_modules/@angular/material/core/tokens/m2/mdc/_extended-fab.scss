@use '../../token-utils';
@use '../../../theming/inspection';

@use 'sass:map';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, extended-fab);

@function get-unthemable-tokens() {
  @return (
    container-height: 48px,
    container-shape: 24px,

    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    container-color: null,
    container-surface-tint-layer-color: null,
    focus-icon-color: null,
    focus-label-text-color: null,
    focus-outline-color: null,
    focus-outline-width: null,
    focus-state-layer-color: null,
    focus-state-layer-opacity: null,
    hover-icon-color: null,
    hover-label-text-color: null,
    hover-state-layer-color: null,
    hover-state-layer-opacity: null,
    icon-color: null,
    icon-size: null,
    label-text-color: null,
    lowered-container-elevation: null,
    lowered-focus-container-elevation: null,
    lowered-hover-container-elevation: null,
    lowered-pressed-container-elevation: null,
    pressed-icon-color: null,
    pressed-label-text-color: null,
    pressed-ripple-color: null,
    pressed-ripple-opacity: null,
    pressed-state-layer-color: null,
    pressed-state-layer-opacity: null
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    container-elevation: 6,
    focus-container-elevation: 8,
    hover-container-elevation: 8,
    pressed-container-elevation: 12,
    container-shadow-color: #000,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    label-text-font: inspection.get-theme-typography($theme, button, font-family),
    label-text-size: inspection.get-theme-typography($theme, button, font-size),
    label-text-tracking: inspection.get-theme-typography($theme, button, letter-spacing),
    label-text-weight: inspection.get-theme-typography($theme, button, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return map.merge(
    get-unthemable-tokens(),
    map.merge(
      get-color-tokens(token-utils.$placeholder-color-config),
      map.merge(
        get-typography-tokens(token-utils.$placeholder-typography-config),
        get-density-tokens(token-utils.$placeholder-density-config)
      )
    )
  );
}
