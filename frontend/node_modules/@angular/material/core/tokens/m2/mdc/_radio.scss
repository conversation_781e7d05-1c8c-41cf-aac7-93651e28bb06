@use 'sass:map';
@use '../../../theming/palette';
@use '../../../theming/theming';
@use '../../../theming/inspection';
@use '../../../style/sass-utils';
@use '../../token-utils';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mdc, radio);

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    disabled-selected-icon-opacity: 0.38,
    disabled-unselected-icon-opacity: 0.38,
    // This is specified both here and in the density tokens, because it
    // determines the size of the radio button itself and there are internal
    // tests who don't configure the theme correctly.
    state-layer-size: 40px,
    // =============================================================================================
    // = TOKENS NOT USED IN ANGULAR MATERIAL                                                       =
    // =============================================================================================
    selected-focus-state-layer-color: null,
    selected-focus-state-layer-opacity: null,
    selected-hover-state-layer-color: null,
    selected-hover-state-layer-opacity: null,
    selected-pressed-state-layer-color: null,
    selected-pressed-state-layer-opacity: null,
    unselected-focus-icon-color: null,
    unselected-focus-state-layer-color: null,
    unselected-focus-state-layer-opacity: null,
    unselected-hover-state-layer-color: null,
    unselected-hover-state-layer-opacity: null,
    unselected-pressed-state-layer-color: null,
    unselected-pressed-state-layer-opacity: null,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: accent) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $palette-color: inspection.get-theme-color($theme, $palette-name, default);
  $icon-color: theming.get-color-from-palette(palette.$gray-palette, if($is-dark, 200, 900));

  @return (
    disabled-selected-icon-color: inspection.get-theme-color($theme, foreground, icon, 1),
    disabled-unselected-icon-color: inspection.get-theme-color($theme, foreground, icon, 1),
    unselected-hover-icon-color: $icon-color,
    unselected-icon-color: inspection.get-theme-color($theme, foreground, icon, 0.54),
    unselected-pressed-icon-color: inspection.get-theme-color($theme, foreground, icon, 0.54),
    selected-focus-icon-color: $palette-color,
    selected-hover-icon-color: $palette-color,
    selected-icon-color: $palette-color,
    selected-pressed-icon-color: $palette-color,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    // The diameter of the radio's ripple.
    state-layer-size: map.get((
      0: 40px,
      -1: 36px,
      -2: 32px,
      -3: 28px,
    ), $scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(token-utils.$placeholder-color-config),
      get-typography-tokens(token-utils.$placeholder-typography-config),
      get-density-tokens(token-utils.$placeholder-density-config)
  );
}
