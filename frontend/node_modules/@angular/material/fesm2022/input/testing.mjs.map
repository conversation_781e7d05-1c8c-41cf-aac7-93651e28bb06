{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/input/testing/input-harness.ts", "../../../../../../../src/material/input/testing/native-option-harness.ts", "../../../../../../../src/material/input/testing/native-select-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {MatFormFieldControlHarness} from '@angular/material/form-field/testing/control';\nimport {InputHarnessFilters} from './input-harness-filters';\n\n/** Harness for interacting with a standard Material inputs in tests. */\nexport class MatInputHarness extends MatFormFieldControlHarness {\n  // TODO: We do not want to handle `select` elements with `matNativeControl` because\n  // not all methods of this harness work reasonably for native select elements.\n  // For more details. See: https://github.com/angular/components/pull/18221.\n  static hostSelector = '[matInput], input[matNativeControl], textarea[matNativeControl]';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatInputHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: InputHarnessFilters = {}): HarnessPredicate<MatInputHarness> {\n    return new HarnessPredicate(MatInputHarness, options)\n      .addOption('value', options.value, (harness, value) => {\n        return HarnessPredicate.stringMatches(harness.getValue(), value);\n      })\n      .addOption('placeholder', options.placeholder, (harness, placeholder) => {\n        return HarnessPredicate.stringMatches(harness.getPlaceholder(), placeholder);\n      });\n  }\n\n  /** Whether the input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Whether the input is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Whether the input is readonly. */\n  async isReadonly(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('readOnly');\n  }\n\n  /** Gets the value of the input. */\n  async getValue(): Promise<string> {\n    // The \"value\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('value');\n  }\n\n  /** Gets the name of the input. */\n  async getName(): Promise<string> {\n    // The \"name\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('name');\n  }\n\n  /**\n   * Gets the type of the input. Returns \"textarea\" if the input is\n   * a textarea.\n   */\n  async getType(): Promise<string> {\n    // The \"type\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('type');\n  }\n\n  /** Gets the placeholder of the input. */\n  async getPlaceholder(): Promise<string> {\n    const host = await this.host();\n    const [nativePlaceholder, fallback] = await parallel(() => [\n      host.getProperty('placeholder'),\n      host.getAttribute('data-placeholder'),\n    ]);\n    return nativePlaceholder || fallback || '';\n  }\n\n  /** Gets the id of the input. */\n  async getId(): Promise<string> {\n    // The input directive always assigns a unique id to the input in\n    // case no id has been explicitly specified.\n    return await (await this.host()).getProperty<string>('id');\n  }\n\n  /**\n   * Focuses the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /**\n   * Blurs the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the input is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /**\n   * Sets the value of the input. The value will be set by simulating\n   * keypresses that correspond to the given value.\n   */\n  async setValue(newValue: string): Promise<void> {\n    const inputEl = await this.host();\n    await inputEl.clear();\n    // We don't want to send keys for the value if the value is an empty\n    // string in order to clear the value. Sending keys with an empty string\n    // still results in unnecessary focus events.\n    if (newValue) {\n      await inputEl.sendKeys(newValue);\n    }\n\n    // Some input types won't respond to key presses (e.g. `color`) so to be sure that the\n    // value is set, we also set the property after the keyboard sequence. Note that we don't\n    // want to do it before, because it can cause the value to be entered twice.\n    await inputEl.setInputValue(newValue);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {NativeOptionHarnessFilters} from './native-select-harness-filters';\n\n/** Harness for interacting with a native `option` in tests. */\nexport class MatNativeOptionHarness extends ComponentHarness {\n  /** Selector used to locate option instances. */\n  static hostSelector = 'select[matNativeControl] option';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatNativeOptionHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which option instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: NativeOptionHarnessFilters = {}) {\n    return new HarnessPredicate(MatNativeOptionHarness, options)\n      .addOption('text', options.text, async (harness, title) =>\n        HarnessPredicate.stringMatches(await harness.getText(), title),\n      )\n      .addOption(\n        'index',\n        options.index,\n        async (harness, index) => (await harness.getIndex()) === index,\n      )\n      .addOption(\n        'isSelected',\n        options.isSelected,\n        async (harness, isSelected) => (await harness.isSelected()) === isSelected,\n      );\n  }\n\n  /** Gets the option's label text. */\n  async getText(): Promise<string> {\n    return (await this.host()).getProperty<string>('label');\n  }\n\n  /** Index of the option within the native `select` element. */\n  async getIndex(): Promise<number> {\n    return (await this.host()).getProperty<number>('index');\n  }\n\n  /** Gets whether the option is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Gets whether the option is selected. */\n  async isSelected(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('selected');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {MatFormFieldControlHarness} from '@angular/material/form-field/testing/control';\nimport {MatNativeOptionHarness} from './native-option-harness';\nimport {\n  NativeOptionHarnessFilters,\n  NativeSelectHarnessFilters,\n} from './native-select-harness-filters';\n\n/** Harness for interacting with a native `select` in tests. */\nexport class MatNativeSelectHarness extends MatFormFieldControlHarness {\n  static hostSelector = 'select[matNativeControl]';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatNativeSelectHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which select instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: NativeSelectHarnessFilters = {}): HarnessPredicate<MatNativeSelectHarness> {\n    return new HarnessPredicate(MatNativeSelectHarness, options);\n  }\n\n  /** Gets a boolean promise indicating if the select is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Gets a boolean promise indicating if the select is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Gets a boolean promise indicating if the select is in multi-selection mode. */\n  async isMultiple(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('multiple');\n  }\n\n  /** Gets the name of the select. */\n  async getName(): Promise<string> {\n    // The \"name\" property of the native select is never undefined.\n    return await (await this.host()).getProperty<string>('name');\n  }\n\n  /** Gets the id of the select. */\n  async getId(): Promise<string> {\n    // We're guaranteed to have an id, because the `matNativeControl` always assigns one.\n    return await (await this.host()).getProperty<string>('id');\n  }\n\n  /** Focuses the select and returns a void promise that indicates when the action is complete. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the select and returns a void promise that indicates when the action is complete. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the select is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Gets the options inside the select panel. */\n  async getOptions(filter: NativeOptionHarnessFilters = {}): Promise<MatNativeOptionHarness[]> {\n    return this.locatorForAll(MatNativeOptionHarness.with(filter))();\n  }\n\n  /**\n   * Selects the options that match the passed-in filter. If the select is in multi-selection\n   * mode all options will be clicked, otherwise the harness will pick the first matching option.\n   */\n  async selectOptions(filter: NativeOptionHarnessFilters = {}): Promise<void> {\n    const [isMultiple, options] = await parallel(() => {\n      return [this.isMultiple(), this.getOptions(filter)];\n    });\n\n    if (options.length === 0) {\n      throw Error('Select does not have options matching the specified filter');\n    }\n\n    const [host, optionIndexes] = await parallel(() => [\n      this.host(),\n      parallel(() => options.slice(0, isMultiple ? undefined : 1).map(option => option.getIndex())),\n    ]);\n\n    await host.selectOptions(...optionIndexes);\n  }\n}\n"], "names": [], "mappings": ";;;AAYA;AACM,MAAO,eAAgB,SAAQ,0BAA0B,CAAA;;;;aAItD,IAAY,CAAA,YAAA,GAAG,iEAAiE,CAAC,EAAA;AAExF;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA+B,EAAE,EAAA;AAC3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC;AAClD,aAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;YACpD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;AACnE,SAAC,CAAC;AACD,aAAA,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,KAAI;YACtE,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC,CAAC;AAC/E,SAAC,CAAC,CAAC;KACN;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,QAAQ,GAAA;;AAEZ,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC,CAAC;KAC/D;;AAGD,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC,CAAC;KAC9D;AAED;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AACzD,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/B,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC;AACtC,SAAA,CAAC,CAAC;AACH,QAAA,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,CAAC;KAC5C;;AAGD,IAAA,MAAM,KAAK,GAAA;;;AAGT,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,IAAI,CAAC,CAAC;KAC5D;AAED;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;KACpC;AAED;;;AAGG;AACH,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KACnC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC;KACxC;AAED;;;AAGG;IACH,MAAM,QAAQ,CAAC,QAAgB,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClC,QAAA,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;;;;QAItB,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAClC;;;;AAKD,QAAA,MAAM,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;KACvC;;;ACpHH;AACM,MAAO,sBAAuB,SAAQ,gBAAgB,CAAA;;aAEnD,IAAY,CAAA,YAAA,GAAG,iCAAiC,CAAC,EAAA;AAExD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO,CAAC;aACzD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,KACpD,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAC/D;aACA,SAAS,CACR,OAAO,EACP,OAAO,CAAC,KAAK,EACb,OAAO,OAAO,EAAE,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,MAAM,KAAK,CAC/D;aACA,SAAS,CACR,YAAY,EACZ,OAAO,CAAC,UAAU,EAClB,OAAO,OAAO,EAAE,UAAU,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,UAAU,CAC3E,CAAC;KACL;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC,CAAC;KACzD;;AAGD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC,CAAC;KACzD;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;;ACzCH;AACM,MAAO,sBAAuB,SAAQ,0BAA0B,CAAA;aAC7D,IAAY,CAAA,YAAA,GAAG,0BAA0B,CAAC,EAAA;AAEjD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,KAAK,GAAA;;AAET,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,IAAI,CAAC,CAAC;KAC5D;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;KACpC;;AAGD,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KACnC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC;KACxC;;AAGD,IAAA,MAAM,UAAU,CAAC,MAAA,GAAqC,EAAE,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAClE;AAED;;;AAGG;AACH,IAAA,MAAM,aAAa,CAAC,MAAA,GAAqC,EAAE,EAAA;QACzD,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAK;AAChD,YAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,YAAA,MAAM,KAAK,CAAC,4DAA4D,CAAC,CAAC;SAC3E;QAED,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;YACjD,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9F,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC,CAAC;KAC5C;;;;;"}