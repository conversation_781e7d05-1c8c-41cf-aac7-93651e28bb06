{"version": 3, "file": "snack-bar.mjs", "sources": ["../../../../../../src/material/snack-bar/snack-bar-ref.ts", "../../../../../../src/material/snack-bar/snack-bar-config.ts", "../../../../../../src/material/snack-bar/snack-bar-content.ts", "../../../../../../src/material/snack-bar/simple-snack-bar.ts", "../../../../../../src/material/snack-bar/simple-snack-bar.html", "../../../../../../src/material/snack-bar/snack-bar-animations.ts", "../../../../../../src/material/snack-bar/snack-bar-container.ts", "../../../../../../src/material/snack-bar/snack-bar-container.html", "../../../../../../src/material/snack-bar/snack-bar.ts", "../../../../../../src/material/snack-bar/module.ts", "../../../../../../src/material/snack-bar/snack-bar_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayRef} from '@angular/cdk/overlay';\nimport {Observable, Subject} from 'rxjs';\nimport {MatSnackBarContainer} from './snack-bar-container';\n\n/** Event that is emitted when a snack bar is dismissed. */\nexport interface MatSnackBarDismiss {\n  /** Whether the snack bar was dismissed using the action button. */\n  dismissedByAction: boolean;\n}\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nexport class MatSnackBarRef<T> {\n  /** The instance of the component making up the content of the snack bar. */\n  instance: T;\n\n  /**\n   * The instance of the component making up the content of the snack bar.\n   * @docs-private\n   */\n  containerInstance: MatSnackBarContainer;\n\n  /** Subject for notifying the user that the snack bar has been dismissed. */\n  private readonly _afterDismissed = new Subject<MatSnackBarDismiss>();\n\n  /** Subject for notifying the user that the snack bar has opened and appeared. */\n  private readonly _afterOpened = new Subject<void>();\n\n  /** Subject for notifying the user that the snack bar action was called. */\n  private readonly _onAction = new Subject<void>();\n\n  /**\n   * Timeout ID for the duration setTimeout call. Used to clear the timeout if the snackbar is\n   * dismissed before the duration passes.\n   */\n  private _durationTimeoutId: number;\n\n  /** Whether the snack bar was dismissed using the action button. */\n  private _dismissedByAction = false;\n\n  constructor(\n    containerInstance: MatSnackBarContainer,\n    private _overlayRef: OverlayRef,\n  ) {\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n\n  /** Dismisses the snack bar. */\n  dismiss(): void {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n\n  /** Marks the snackbar action clicked. */\n  dismissWithAction(): void {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction(): void {\n    this.dismissWithAction();\n  }\n\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration: number): void {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n\n  /** Marks the snackbar as opened */\n  _open(): void {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n\n  /** Cleans up the DOM after closing. */\n  private _finishDismiss(): void {\n    this._overlayRef.dispose();\n\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n\n    this._afterDismissed.next({dismissedByAction: this._dismissedByAction});\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed(): Observable<MatSnackBarDismiss> {\n    return this._afterDismissed;\n  }\n\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened(): Observable<void> {\n    return this.containerInstance._onEnter;\n  }\n\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction(): Observable<void> {\n    return this._onAction;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ViewContainerRef, InjectionToken} from '@angular/core';\nimport {AriaLivePoliteness} from '@angular/cdk/a11y';\nimport {Direction} from '@angular/cdk/bidi';\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nexport const MAT_SNACK_BAR_DATA = new InjectionToken<any>('MatSnackBarData');\n\n/** Possible values for horizontalPosition on MatSnackBarConfig. */\nexport type MatSnackBarHorizontalPosition = 'start' | 'center' | 'end' | 'left' | 'right';\n\n/** Possible values for verticalPosition on MatSnackBarConfig. */\nexport type MatSnackBarVerticalPosition = 'top' | 'bottom';\n\n/**\n * Configuration used when opening a snack-bar.\n */\nexport class MatSnackBarConfig<D = any> {\n  /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n  politeness?: AriaLivePoliteness = 'assertive';\n\n  /**\n   * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n   * component or template, the announcement message will default to the specified message.\n   */\n  announcementMessage?: string = '';\n\n  /**\n   * The view container that serves as the parent for the snackbar for the purposes of dependency\n   * injection. Note: this does not affect where the snackbar is inserted in the DOM.\n   */\n  viewContainerRef?: ViewContainerRef;\n\n  /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n  duration?: number = 0;\n\n  /** Extra CSS classes to be added to the snack bar container. */\n  panelClass?: string | string[];\n\n  /** Text layout direction for the snack bar. */\n  direction?: Direction;\n\n  /** Data being injected into the child component. */\n  data?: D | null = null;\n\n  /** The horizontal position to place the snack bar. */\n  horizontalPosition?: MatSnackBarHorizontalPosition = 'center';\n\n  /** The vertical position to place the snack bar. */\n  verticalPosition?: MatSnackBarVerticalPosition = 'bottom';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directive} from '@angular/core';\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\n@Directive({\n  selector: `[matSnackBarLabel]`,\n  standalone: true,\n  host: {\n    'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n  },\n})\nexport class MatSnackBarLabel {}\n\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\n@Directive({\n  selector: `[matSnackBarActions]`,\n  standalone: true,\n  host: {\n    'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n  },\n})\nexport class MatSnackBarActions {}\n\n/** Directive that should be applied to each of the snack bar's action buttons. */\n@Directive({\n  selector: `[matSnackBarAction]`,\n  standalone: true,\n  host: {\n    'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n  },\n})\nexport class MatSnackBarAction {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ChangeDetectionStrategy, Component, Inject, ViewEncapsulation} from '@angular/core';\nimport {MatButton} from '@angular/material/button';\nimport {MatSnackBarRef} from './snack-bar-ref';\nimport {MAT_SNACK_BAR_DATA} from './snack-bar-config';\nimport {MatSnackBarAction, MatSnackBarActions, MatSnackBarLabel} from './snack-bar-content';\n\n/**\n * Interface for a simple snack bar component that has a message and a single action.\n */\nexport interface TextOnlySnackBar {\n  data: {message: string; action: string};\n  snackBarRef: MatSnackBarRef<TextOnlySnackBar>;\n  action: () => void;\n  hasAction: boolean;\n}\n\n@Component({\n  selector: 'simple-snack-bar',\n  templateUrl: 'simple-snack-bar.html',\n  styleUrl: 'simple-snack-bar.css',\n  exportAs: 'matSnackBar',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n  standalone: true,\n  host: {\n    'class': 'mat-mdc-simple-snack-bar',\n  },\n})\nexport class SimpleSnackBar implements TextOnlySnackBar {\n  constructor(\n    public snackBarRef: MatSnackBarRef<SimpleSnackBar>,\n    @Inject(MAT_SNACK_BAR_DATA) public data: {message: string; action: string},\n  ) {}\n\n  /** Performs the action on the snack bar. */\n  action(): void {\n    this.snackBarRef.dismissWithAction();\n  }\n\n  /** If the action button should be shown. */\n  get hasAction(): boolean {\n    return !!this.data.action;\n  }\n}\n", "<div matSnackBarLabel>\n  {{data.message}}\n</div>\n\n@if (hasAction) {\n  <div matSnackBarActions>\n    <button mat-button matSnackBarAction (click)=\"action()\">\n      {{data.action}}\n    </button>\n  </div>\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  AnimationTriggerMetadata,\n} from '@angular/animations';\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nexport const matSnackBarAnimations: {\n  readonly snackBarState: AnimationTriggerMetadata;\n} = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: trigger('state', [\n    state(\n      'void, hidden',\n      style({\n        transform: 'scale(0.8)',\n        opacity: 0,\n      }),\n    ),\n    state(\n      'visible',\n      style({\n        transform: 'scale(1)',\n        opacity: 1,\n      }),\n    ),\n    transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    transition(\n      '* => void, * => hidden',\n      animate(\n        '75ms cubic-bezier(0.4, 0.0, 1, 1)',\n        style({\n          opacity: 0,\n        }),\n      ),\n    ),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ComponentRef,\n  ElementRef,\n  EmbeddedViewRef,\n  inject,\n  NgZone,\n  OnDestroy,\n  ViewChild,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {DOCUMENT} from '@angular/common';\nimport {matSnackBarAnimations} from './snack-bar-animations';\nimport {\n  BasePortalOutlet,\n  CdkPortalOutlet,\n  ComponentPortal,\n  DomPortal,\n  TemplatePortal,\n} from '@angular/cdk/portal';\nimport {Observable, Subject} from 'rxjs';\nimport {AriaLivePoliteness} from '@angular/cdk/a11y';\nimport {Platform} from '@angular/cdk/platform';\nimport {AnimationEvent} from '@angular/animations';\nimport {MatSnackBarConfig} from './snack-bar-config';\n\nlet uniqueId = 0;\n\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\n@Component({\n  selector: 'mat-snack-bar-container',\n  templateUrl: 'snack-bar-container.html',\n  styleUrl: 'snack-bar-container.css',\n  // In Ivy embedded views will be change detected from their declaration place, rather than\n  // where they were stamped out. This means that we can't have the snack bar container be OnPush,\n  // because it might cause snack bars that were opened from a template not to be out of date.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  encapsulation: ViewEncapsulation.None,\n  animations: [matSnackBarAnimations.snackBarState],\n  standalone: true,\n  imports: [CdkPortalOutlet],\n  host: {\n    'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n    '[@state]': '_animationState',\n    '(@state.done)': 'onAnimationEnd($event)',\n  },\n})\nexport class MatSnackBarContainer extends BasePortalOutlet implements OnDestroy {\n  private _document = inject(DOCUMENT);\n  private _trackedModals = new Set<Element>();\n\n  /** The number of milliseconds to wait before announcing the snack bar's content. */\n  private readonly _announceDelay: number = 150;\n\n  /** The timeout for announcing the snack bar's content. */\n  private _announceTimeoutId: number;\n\n  /** Whether the component has been destroyed. */\n  private _destroyed = false;\n\n  /** The portal outlet inside of this container into which the snack bar content will be loaded. */\n  @ViewChild(CdkPortalOutlet, {static: true}) _portalOutlet: CdkPortalOutlet;\n\n  /** Subject for notifying that the snack bar has announced to screen readers. */\n  readonly _onAnnounce: Subject<void> = new Subject();\n\n  /** Subject for notifying that the snack bar has exited from view. */\n  readonly _onExit: Subject<void> = new Subject();\n\n  /** Subject for notifying that the snack bar has finished entering the view. */\n  readonly _onEnter: Subject<void> = new Subject();\n\n  /** The state of the snack bar animations. */\n  _animationState = 'void';\n\n  /** aria-live value for the live region. */\n  _live: AriaLivePoliteness;\n\n  /**\n   * Element that will have the `mdc-snackbar__label` class applied if the attached component\n   * or template does not have it. This ensures that the appropriate structure, typography, and\n   * color is applied to the attached view.\n   */\n  @ViewChild('label', {static: true}) _label: ElementRef;\n\n  /**\n   * Role of the live region. This is only for Firefox as there is a known issue where Firefox +\n   * JAWS does not read out aria-live message.\n   */\n  _role?: 'status' | 'alert';\n\n  /** Unique ID of the aria-live element. */\n  readonly _liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n\n  constructor(\n    private _ngZone: NgZone,\n    private _elementRef: ElementRef<HTMLElement>,\n    private _changeDetectorRef: ChangeDetectorRef,\n    private _platform: Platform,\n    /** The snack bar configuration. */\n    public snackBarConfig: MatSnackBarConfig,\n  ) {\n    super();\n\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n      this._live = 'assertive';\n    } else if (snackBarConfig.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal<C>(portal: TemplatePortal<C>): EmbeddedViewRef<C> {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n\n  /**\n   * Attaches a DOM portal to the snack bar container.\n   * @deprecated To be turned into a method.\n   * @breaking-change 10.0.0\n   */\n  override attachDomPortal = (portal: DomPortal) => {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachDomPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  };\n\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(event: AnimationEvent) {\n    const {fromState, toState} = event;\n\n    if ((toState === 'void' && fromState !== 'void') || toState === 'hidden') {\n      this._completeExit();\n    }\n\n    if (toState === 'visible') {\n      // Note: we shouldn't use `this` inside the zone callback,\n      // because it can cause a memory leak.\n      const onEnter = this._onEnter;\n\n      this._ngZone.run(() => {\n        onEnter.next();\n        onEnter.complete();\n      });\n    }\n  }\n\n  /** Begin animation of snack bar entrance into view. */\n  enter(): void {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n      // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n    }\n  }\n\n  /** Begin animation of the snack bar exiting from view. */\n  exit(): Observable<void> {\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n    });\n\n    return this._onExit;\n  }\n\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n\n  /**\n   * Removes the element in a microtask. Helps prevent errors where we end up\n   * removing an element which is in the middle of an animation.\n   */\n  private _completeExit() {\n    queueMicrotask(() => {\n      this._onExit.next();\n      this._onExit.complete();\n    });\n  }\n\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  private _afterPortalAttached() {\n    const element: HTMLElement = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n\n    this._exposeToModals();\n\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  private _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll(\n      'body > .cdk-overlay-container [aria-modal=\"true\"]',\n    );\n\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n\n  /** Clears the references to the live element from any modals it was added to. */\n  private _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n\n  /** Asserts that no content is already attached to the container. */\n  private _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  private _screenReaderAnnounce() {\n    if (!this._announceTimeoutId) {\n      this._ngZone.runOutsideAngular(() => {\n        this._announceTimeoutId = setTimeout(() => {\n          const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n          const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n\n          if (inertElement && liveElement) {\n            // If an element in the snack bar content is focused before being moved\n            // track it and restore focus after moving to the live region.\n            let focusedElement: HTMLElement | null = null;\n            if (\n              this._platform.isBrowser &&\n              document.activeElement instanceof HTMLElement &&\n              inertElement.contains(document.activeElement)\n            ) {\n              focusedElement = document.activeElement;\n            }\n\n            inertElement.removeAttribute('aria-hidden');\n            liveElement.appendChild(inertElement);\n            focusedElement?.focus();\n\n            this._onAnnounce.next();\n            this._onAnnounce.complete();\n          }\n        }, this._announceDelay);\n      });\n    }\n  }\n}\n", "<div class=\"mdc-snackbar__surface\">\n  <!--\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\n    the attached template/component does not contain it.\n  -->\n  <div class=\"mat-mdc-snack-bar-label\" #label>\n    <!-- <PERSON><PERSON> holds the snack bar content, will be empty after announcing to screen readers. -->\n    <div aria-hidden=\"true\">\n      <ng-template cdkPortalOutlet />\n    </div>\n\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\n    <div [attr.aria-live]=\"_live\" [attr.role]=\"_role\" [attr.id]=\"_liveElementId\"></div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {LiveAnnouncer} from '@angular/cdk/a11y';\nimport {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';\nimport {ComponentType, Overlay, OverlayConfig, OverlayRef} from '@angular/cdk/overlay';\nimport {\n  ComponentRef,\n  EmbeddedViewRef,\n  Inject,\n  Injectable,\n  InjectionToken,\n  Injector,\n  OnDestroy,\n  Optional,\n  SkipSelf,\n  TemplateRef,\n} from '@angular/core';\nimport {SimpleSnackBar, TextOnlySnackBar} from './simple-snack-bar';\nimport {MatSnackBarContainer} from './snack-bar-container';\nimport {MAT_SNACK_BAR_DATA, MatSnackBarConfig} from './snack-bar-config';\nimport {MatSnackBarRef} from './snack-bar-ref';\nimport {ComponentPortal, TemplatePortal} from '@angular/cdk/portal';\nimport {takeUntil} from 'rxjs/operators';\n\n/** @docs-private */\nexport function MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY(): MatSnackBarConfig {\n  return new MatSnackBarConfig();\n}\n\n/** Injection token that can be used to specify default snack bar. */\nexport const MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken<MatSnackBarConfig>(\n  'mat-snack-bar-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * Service to dispatch Material Design snack bar messages.\n */\n@Injectable({providedIn: 'root'})\nexport class MatSnackBar implements OnDestroy {\n  /**\n   * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n   * If there is a parent snack-bar service, all operations should delegate to that parent\n   * via `_openedSnackBarRef`.\n   */\n  private _snackBarRefAtThisLevel: MatSnackBarRef<any> | null = null;\n\n  /** The component that should be rendered as the snack bar's simple component. */\n  simpleSnackBarComponent = SimpleSnackBar;\n\n  /** The container component that attaches the provided template or component. */\n  snackBarContainerComponent = MatSnackBarContainer;\n\n  /** The CSS class to apply for handset mode. */\n  handsetCssClass = 'mat-mdc-snack-bar-handset';\n\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef(): MatSnackBarRef<any> | null {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n\n  set _openedSnackBarRef(value: MatSnackBarRef<any> | null) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n\n  constructor(\n    private _overlay: Overlay,\n    private _live: LiveAnnouncer,\n    private _injector: Injector,\n    private _breakpointObserver: BreakpointObserver,\n    @Optional() @SkipSelf() private _parentSnackBar: MatSnackBar,\n    @Inject(MAT_SNACK_BAR_DEFAULT_OPTIONS) private _defaultConfig: MatSnackBarConfig,\n  ) {}\n\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent<T, D = any>(\n    component: ComponentType<T>,\n    config?: MatSnackBarConfig<D>,\n  ): MatSnackBarRef<T> {\n    return this._attach(component, config) as MatSnackBarRef<T>;\n  }\n\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(\n    template: TemplateRef<any>,\n    config?: MatSnackBarConfig,\n  ): MatSnackBarRef<EmbeddedViewRef<any>> {\n    return this._attach(template, config);\n  }\n\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(\n    message: string,\n    action: string = '',\n    config?: MatSnackBarConfig,\n  ): MatSnackBarRef<TextOnlySnackBar> {\n    const _config = {...this._defaultConfig, ...config};\n\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {message, action};\n\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss(): void {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  private _attachSnackBarContainer(\n    overlayRef: OverlayRef,\n    config: MatSnackBarConfig,\n  ): MatSnackBarContainer {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{provide: MatSnackBarConfig, useValue: config}],\n    });\n\n    const containerPortal = new ComponentPortal(\n      this.snackBarContainerComponent,\n      config.viewContainerRef,\n      injector,\n    );\n    const containerRef: ComponentRef<MatSnackBarContainer> = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  private _attach<T>(\n    content: ComponentType<T> | TemplateRef<T>,\n    userConfig?: MatSnackBarConfig,\n  ): MatSnackBarRef<T | EmbeddedViewRef<any>> {\n    const config = {...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig};\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef<T | EmbeddedViewRef<any>>(container, overlayRef);\n\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null!, {\n        $implicit: config.data,\n        snackBarRef,\n      } as any);\n\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal<T>(portal);\n\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver\n      .observe(Breakpoints.HandsetPortrait)\n      .pipe(takeUntil(overlayRef.detachments()))\n      .subscribe(state => {\n        overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n      });\n\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage!, config.politeness);\n      });\n    }\n\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n\n  /** Animates the old snack bar out and the new one in. */\n  private _animateSnackBar(snackBarRef: MatSnackBarRef<any>, config: MatSnackBarConfig) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration!));\n    }\n  }\n\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  private _createOverlay(config: MatSnackBarConfig): OverlayRef {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft =\n      config.horizontalPosition === 'left' ||\n      (config.horizontalPosition === 'start' && !isRtl) ||\n      (config.horizontalPosition === 'end' && isRtl);\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  private _createInjector<T>(config: MatSnackBarConfig, snackBarRef: MatSnackBarRef<T>): Injector {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [\n        {provide: MatSnackBarRef, useValue: snackBarRef},\n        {provide: MAT_SNACK_BAR_DATA, useValue: config.data},\n      ],\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatButtonModule} from '@angular/material/button';\nimport {MatCommonModule} from '@angular/material/core';\n\nimport {SimpleSnackBar} from './simple-snack-bar';\nimport {MatSnackBarContainer} from './snack-bar-container';\nimport {MatSnackBarAction, MatSnackBarActions, MatSnackBarLabel} from './snack-bar-content';\nimport {MatSnackBar} from './snack-bar';\n\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\n\n@NgModule({\n  imports: [\n    OverlayModule,\n    PortalModule,\n    MatButtonModule,\n    MatCommonModule,\n    SimpleSnackBar,\n    ...DIRECTIVES,\n  ],\n  exports: [MatCommonModule, ...DIRECTIVES],\n  providers: [MatSnackBar],\n})\nexport class MatSnackBarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i2.MatSnackBarConfig"], "mappings": ";;;;;;;;;;;;;;;;AAkBA;AACA,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAExC;;AAEG;MACU,cAAc,CAAA;IA4BzB,WACE,CAAA,iBAAuC,EAC/B,WAAuB,EAAA;QAAvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;;AAnBhB,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,OAAO,EAAsB,CAAC;;AAGpD,QAAA,IAAA,CAAA,YAAY,GAAG,IAAI,OAAO,EAAQ,CAAC;;AAGnC,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,OAAO,EAAQ,CAAC;;QASzC,IAAkB,CAAA,kBAAA,GAAG,KAAK,CAAC;AAMjC,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;KAClE;;IAGD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;SAC/B;AACD,QAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;KACvC;;IAGD,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC1B,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAC/B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;AACD,QAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;KACvC;AAED;;;;AAIG;IACH,eAAe,GAAA;QACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC1B;;AAGD,IAAA,aAAa,CAAC,QAAgB,EAAA;;;QAG5B,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;KAC7F;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;AAC7B,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;SAC9B;KACF;;IAGO,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;AAE3B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC1B,YAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;SAC3B;AAED,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAAC,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;KACjC;;IAGD,cAAc,GAAA;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;;IAGD,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;KACxC;;IAGD,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AACF;;ACtHD;MACa,kBAAkB,GAAG,IAAI,cAAc,CAAM,iBAAiB,EAAE;AAQ7E;;AAEG;MACU,iBAAiB,CAAA;AAA9B,IAAA,WAAA,GAAA;;QAEE,IAAU,CAAA,UAAA,GAAwB,WAAW,CAAC;AAE9C;;;AAGG;QACH,IAAmB,CAAA,mBAAA,GAAY,EAAE,CAAC;;QASlC,IAAQ,CAAA,QAAA,GAAY,CAAC,CAAC;;QAStB,IAAI,CAAA,IAAA,GAAc,IAAI,CAAC;;QAGvB,IAAkB,CAAA,kBAAA,GAAmC,QAAQ,CAAC;;QAG9D,IAAgB,CAAA,gBAAA,GAAiC,QAAQ,CAAC;KAC3D;AAAA;;AC/CD;MAQa,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,6CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAP5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAoB,kBAAA,CAAA;AAC9B,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,6CAA6C;AACvD,qBAAA;AACF,iBAAA,CAAA;;AAGD;MAQa,kBAAkB,CAAA;8GAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iDAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAP9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAsB,oBAAA,CAAA;AAChC,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,iDAAiD;AAC3D,qBAAA;AACF,iBAAA,CAAA;;AAGD;MAQa,iBAAiB,CAAA;8GAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,+CAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAP7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAqB,mBAAA,CAAA;AAC/B,oBAAA,UAAU,EAAE,IAAI;AAChB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,+CAA+C;AACzD,qBAAA;AACF,iBAAA,CAAA;;;MCAY,cAAc,CAAA;IACzB,WACS,CAAA,WAA2C,EACf,IAAuC,EAAA;QADnE,IAAW,CAAA,WAAA,GAAX,WAAW,CAAgC;QACf,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAmC;KACxE;;IAGJ,MAAM,GAAA;AACJ,QAAA,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;KACtC;;AAGD,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;KAC3B;AAdU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,6CAGf,kBAAkB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAHjB,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,0BAAA,EAAA,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECrC3B,0NAWA,EDoBY,MAAA,EAAA,CAAA,yCAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,iLAAE,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,kBAAkB,EAAA,QAAA,EAAA,sBAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAMjE,cAAc,EAAA,UAAA,EAAA,CAAA;kBAb1B,SAAS;+BACE,kBAAkB,EAAA,QAAA,EAGlB,aAAa,EACR,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EACpB,uBAAuB,CAAC,MAAM,EAAA,OAAA,EACtC,CAAC,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,EACjE,UAAA,EAAA,IAAI,EACV,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,0BAA0B;AACpC,qBAAA,EAAA,QAAA,EAAA,0NAAA,EAAA,MAAA,EAAA,CAAA,yCAAA,CAAA,EAAA,CAAA;;0BAKE,MAAM;2BAAC,kBAAkB,CAAA;;;AExB9B;;;AAGG;AACU,MAAA,qBAAqB,GAE9B;;AAEF,IAAA,aAAa,EAAE,OAAO,CAAC,OAAO,EAAE;AAC9B,QAAA,KAAK,CACH,cAAc,EACd,KAAK,CAAC;AACJ,YAAA,SAAS,EAAE,YAAY;AACvB,YAAA,OAAO,EAAE,CAAC;AACX,SAAA,CAAC,CACH;AACD,QAAA,KAAK,CACH,SAAS,EACT,KAAK,CAAC;AACJ,YAAA,SAAS,EAAE,UAAU;AACrB,YAAA,OAAO,EAAE,CAAC;AACX,SAAA,CAAC,CACH;AACD,QAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,kCAAkC,CAAC,CAAC;QACvE,UAAU,CACR,wBAAwB,EACxB,OAAO,CACL,mCAAmC,EACnC,KAAK,CAAC;AACJ,YAAA,OAAO,EAAE,CAAC;AACX,SAAA,CAAC,CACH,CACF;KACF,CAAC;;;ACbJ,IAAI,QAAQ,GAAG,CAAC,CAAC;AAEjB;;;AAGG;AAoBG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AA+CxD,IAAA,WAAA,CACU,OAAe,EACf,WAAoC,EACpC,kBAAqC,EACrC,SAAmB;;IAEpB,cAAiC,EAAA;AAExC,QAAA,KAAK,EAAE,CAAC;QAPA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACrC,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QAEpB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAmB;AApDlC,QAAA,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAW,CAAC;;QAG3B,IAAc,CAAA,cAAA,GAAW,GAAG,CAAC;;QAMtC,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;;AAMlB,QAAA,IAAA,CAAA,WAAW,GAAkB,IAAI,OAAO,EAAE,CAAC;;AAG3C,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,OAAO,EAAE,CAAC;;AAGvC,QAAA,IAAA,CAAA,QAAQ,GAAkB,IAAI,OAAO,EAAE,CAAC;;QAGjD,IAAe,CAAA,eAAA,GAAG,MAAM,CAAC;;AAmBhB,QAAA,IAAA,CAAA,cAAc,GAAG,CAAA,6BAAA,EAAgC,QAAQ,EAAE,EAAE,CAAC;AAkDvE;;;;AAIG;AACM,QAAA,IAAA,CAAA,eAAe,GAAG,CAAC,MAAiB,KAAI;YAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC;;;QA9CA,IAAI,cAAc,CAAC,UAAU,KAAK,WAAW,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE;AACpF,YAAA,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;SAC1B;AAAM,aAAA,IAAI,cAAc,CAAC,UAAU,KAAK,KAAK,EAAE;AAC9C,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM;AACL,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;SACvB;;;AAID,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;AAC3B,gBAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;aACvB;AACD,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE;AAC9B,gBAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;aACtB;SACF;KACF;;AAGD,IAAA,qBAAqB,CAAI,MAA0B,EAAA;QACjD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAChE,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,OAAO,MAAM,CAAC;KACf;;AAGD,IAAA,oBAAoB,CAAI,MAAyB,EAAA;QAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC/D,IAAI,CAAC,oBAAoB,EAAE,CAAC;AAC5B,QAAA,OAAO,MAAM,CAAC;KACf;;AAeD,IAAA,cAAc,CAAC,KAAqB,EAAA;AAClC,QAAA,MAAM,EAAC,SAAS,EAAE,OAAO,EAAC,GAAG,KAAK,CAAC;AAEnC,QAAA,IAAI,CAAC,OAAO,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ,EAAE;YACxE,IAAI,CAAC,aAAa,EAAE,CAAC;SACtB;AAED,QAAA,IAAI,OAAO,KAAK,SAAS,EAAE;;;AAGzB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAE9B,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;gBACpB,OAAO,CAAC,IAAI,EAAE,CAAC;gBACf,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,aAAC,CAAC,CAAC;SACJ;KACF;;IAGD,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;;;AAGjC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACvC,YAAA,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,CAAC;YACxC,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;KACF;;IAGD,IAAI,GAAA;;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;;;;AAIpB,YAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;AAChC,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;;;;YAKvC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;;;AAI5D,YAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACxC,SAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;AAED;;;AAGG;IACK,aAAa,GAAA;QACnB,cAAc,CAAC,MAAK;AAClB,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AACpB,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AAC1B,SAAC,CAAC,CAAC;KACJ;AAED;;;AAGG;IACK,oBAAoB,GAAA;AAC1B,QAAA,MAAM,OAAO,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC5D,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;QAEpD,IAAI,YAAY,EAAE;AAChB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;;AAE/B,gBAAA,YAAY,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;aACnE;iBAAM;AACL,gBAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;aACrC;SACF;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;;;;AAKvB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QACxC,MAAM,UAAU,GAAG,qBAAqB,CAAC;AACzC,QAAA,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA,CAAA,EAAI,UAAU,CAAE,CAAA,CAAC,CAAC,CAAC;KAC5E;AAED;;;;AAIG;IACK,eAAe,GAAA;;;;;;;AAOrB,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAC5C,mDAAmD,CACpD,CAAC;AAEF,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtC,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAE/B,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;aACrC;iBAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;gBACtC,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;aACtD;SACF;KACF;;IAGO,gBAAgB,GAAA;AACtB,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,IAAG;YAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEjD,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAElE,gBAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,oBAAA,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;iBAC3C;qBAAM;AACL,oBAAA,KAAK,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;iBACpC;aACF;AACH,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;KAC7B;;IAGO,kBAAkB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AACvF,YAAA,MAAM,KAAK,CAAC,0EAA0E,CAAC,CAAC;SACzF;KACF;AAED;;;AAGG;IACK,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,gBAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,MAAK;AACxC,oBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;AACnF,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAEhF,oBAAA,IAAI,YAAY,IAAI,WAAW,EAAE;;;wBAG/B,IAAI,cAAc,GAAuB,IAAI,CAAC;AAC9C,wBAAA,IACE,IAAI,CAAC,SAAS,CAAC,SAAS;4BACxB,QAAQ,CAAC,aAAa,YAAY,WAAW;4BAC7C,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAC7C;AACA,4BAAA,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC;yBACzC;AAED,wBAAA,YAAY,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC5C,wBAAA,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;wBACtC,cAAc,EAAE,KAAK,EAAE,CAAC;AAExB,wBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;AACxB,wBAAA,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;qBAC7B;AACH,iBAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;AAC1B,aAAC,CAAC,CAAC;SACJ;KACF;8GAxSU,oBAAoB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAApB,oBAAoB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,yBAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,aAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,EAAA,cAAA,EAAA,6DAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,eAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAcpB,eAAe,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,OAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC3E5B,wpBAeA,EAAA,MAAA,EAAA,CAAA,y4GAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDuCY,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,UAAA,EAFb,CAAC,qBAAqB,CAAC,aAAa,CAAC,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAStC,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAnBhC,SAAS;+BACE,yBAAyB,EAAA,eAAA,EAOlB,uBAAuB,CAAC,OAAO,iBACjC,iBAAiB,CAAC,IAAI,EACzB,UAAA,EAAA,CAAC,qBAAqB,CAAC,aAAa,CAAC,EACrC,UAAA,EAAA,IAAI,WACP,CAAC,eAAe,CAAC,EACpB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,6DAA6D;AACtE,wBAAA,UAAU,EAAE,iBAAiB;AAC7B,wBAAA,eAAe,EAAE,wBAAwB;AAC1C,qBAAA,EAAA,QAAA,EAAA,wpBAAA,EAAA,MAAA,EAAA,CAAA,y4GAAA,CAAA,EAAA,CAAA;wLAgB2C,aAAa,EAAA,CAAA;sBAAxD,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,eAAe,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;gBAsBN,MAAM,EAAA,CAAA;sBAAzC,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,OAAO,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;;;AEnEpC;SACgB,qCAAqC,GAAA;IACnD,OAAO,IAAI,iBAAiB,EAAE,CAAC;AACjC,CAAC;AAED;MACa,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B,EAC/B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,qCAAqC;AAC/C,CAAA,EACD;AAEF;;AAEG;MAEU,WAAW,CAAA;;AAkBtB,IAAA,IAAI,kBAAkB,GAAA;AACpB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,uBAAuB,CAAC;KAC1E;IAED,IAAI,kBAAkB,CAAC,KAAiC,EAAA;AACtD,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,eAAe,CAAC,kBAAkB,GAAG,KAAK,CAAC;SACjD;aAAM;AACL,YAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;SACtC;KACF;IAED,WACU,CAAA,QAAiB,EACjB,KAAoB,EACpB,SAAmB,EACnB,mBAAuC,EACf,eAA4B,EACb,cAAiC,EAAA;QALxE,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QACjB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAe;QACpB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAmB,CAAA,mBAAA,GAAnB,mBAAmB,CAAoB;QACf,IAAe,CAAA,eAAA,GAAf,eAAe,CAAa;QACb,IAAc,CAAA,cAAA,GAAd,cAAc,CAAmB;AApClF;;;;AAIG;QACK,IAAuB,CAAA,uBAAA,GAA+B,IAAI,CAAC;;QAGnE,IAAuB,CAAA,uBAAA,GAAG,cAAc,CAAC;;QAGzC,IAA0B,CAAA,0BAAA,GAAG,oBAAoB,CAAC;;QAGlD,IAAe,CAAA,eAAA,GAAG,2BAA2B,CAAC;KAuB1C;AAEJ;;;;;;AAMG;IACH,iBAAiB,CACf,SAA2B,EAC3B,MAA6B,EAAA;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAsB,CAAC;KAC7D;AAED;;;;;;AAMG;IACH,gBAAgB,CACd,QAA0B,EAC1B,MAA0B,EAAA;QAE1B,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;KACvC;AAED;;;;;AAKG;AACH,IAAA,IAAI,CACF,OAAe,EACf,MAAiB,GAAA,EAAE,EACnB,MAA0B,EAAA;QAE1B,MAAM,OAAO,GAAG,EAAC,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,MAAM,EAAC,CAAC;;;QAIpD,OAAO,CAAC,IAAI,GAAG,EAAC,OAAO,EAAE,MAAM,EAAC,CAAC;;;AAIjC,QAAA,IAAI,OAAO,CAAC,mBAAmB,KAAK,OAAO,EAAE;AAC3C,YAAA,OAAO,CAAC,mBAAmB,GAAG,SAAS,CAAC;SACzC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;KACtE;AAED;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;SACnC;KACF;IAED,WAAW,GAAA;;AAET,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAChC,YAAA,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;SACxC;KACF;AAED;;AAEG;IACK,wBAAwB,CAC9B,UAAsB,EACtB,MAAyB,EAAA;AAEzB,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAC3F,QAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC/B,YAAA,MAAM,EAAE,YAAY,IAAI,IAAI,CAAC,SAAS;YACtC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,EAAC,CAAC;AAC5D,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,CACzC,IAAI,CAAC,0BAA0B,EAC/B,MAAM,CAAC,gBAAgB,EACvB,QAAQ,CACT,CAAC;QACF,MAAM,YAAY,GAAuC,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AAC5F,QAAA,YAAY,CAAC,QAAQ,CAAC,cAAc,GAAG,MAAM,CAAC;QAC9C,OAAO,YAAY,CAAC,QAAQ,CAAC;KAC9B;AAED;;AAEG;IACK,OAAO,CACb,OAA0C,EAC1C,UAA8B,EAAA;AAE9B,QAAA,MAAM,MAAM,GAAG,EAAC,GAAG,IAAI,iBAAiB,EAAE,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,UAAU,EAAC,CAAC;QACnF,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QACpE,MAAM,WAAW,GAAG,IAAI,cAAc,CAA2B,SAAS,EAAE,UAAU,CAAC,CAAC;AAExF,QAAA,IAAI,OAAO,YAAY,WAAW,EAAE;YAClC,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,OAAO,EAAE,IAAK,EAAE;gBAChD,SAAS,EAAE,MAAM,CAAC,IAAI;gBACtB,WAAW;AACL,aAAA,CAAC,CAAC;YAEV,WAAW,CAAC,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;SAC/D;aAAM;YACL,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,SAAS,CAAC,qBAAqB,CAAI,MAAM,CAAC,CAAC;;AAG9D,YAAA,WAAW,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;SAC5C;;;;AAKD,QAAA,IAAI,CAAC,mBAAmB;AACrB,aAAA,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC;aACpC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;aACzC,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAClF,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,MAAM,CAAC,mBAAmB,EAAE;;AAE9B,YAAA,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,MAAK;AACnC,gBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,mBAAoB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;AACtE,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;QACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;;IAGO,gBAAgB,CAAC,WAAgC,EAAE,MAAyB,EAAA;;AAElF,QAAA,WAAW,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,MAAK;;AAE1C,YAAA,IAAI,IAAI,CAAC,kBAAkB,IAAI,WAAW,EAAE;AAC1C,gBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;aAChC;AAED,YAAA,IAAI,MAAM,CAAC,mBAAmB,EAAE;AAC9B,gBAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;aACpB;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;;;YAG3B,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,SAAS,CAAC,MAAK;AACtD,gBAAA,WAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;AACxC,aAAC,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;SACnC;aAAM;;AAEL,YAAA,WAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;SACvC;;QAGD,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,GAAG,CAAC,EAAE;AAC1C,YAAA,WAAW,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,QAAS,CAAC,CAAC,CAAC;SACxF;KACF;AAED;;;AAGG;AACK,IAAA,cAAc,CAAC,MAAyB,EAAA;AAC9C,QAAA,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;AAC1C,QAAA,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QAE3C,IAAI,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;;AAEzD,QAAA,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC;AACzC,QAAA,MAAM,MAAM,GACV,MAAM,CAAC,kBAAkB,KAAK,MAAM;aACnC,MAAM,CAAC,kBAAkB,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;aAChD,MAAM,CAAC,kBAAkB,KAAK,KAAK,IAAI,KAAK,CAAC,CAAC;QACjD,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,kBAAkB,KAAK,QAAQ,CAAC;QAClE,IAAI,MAAM,EAAE;AACV,YAAA,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC5B;aAAM,IAAI,OAAO,EAAE;AAClB,YAAA,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7B;aAAM;YACL,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;SACvC;;AAED,QAAA,IAAI,MAAM,CAAC,gBAAgB,KAAK,KAAK,EAAE;AACrC,YAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SAC3B;aAAM;AACL,YAAA,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;SAC9B;AAED,QAAA,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QAClD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;KAC5C;AAED;;;;AAIG;IACK,eAAe,CAAI,MAAyB,EAAE,WAA8B,EAAA;AAClF,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;QAE3F,OAAO,QAAQ,CAAC,MAAM,CAAC;AACrB,YAAA,MAAM,EAAE,YAAY,IAAI,IAAI,CAAC,SAAS;AACtC,YAAA,SAAS,EAAE;AACT,gBAAA,EAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAC;gBAChD,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAC;AACrD,aAAA;AACF,SAAA,CAAC,CAAC;KACJ;AAxQU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,0LAqCZ,6BAA6B,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AArC5B,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,cADC,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,WAAW,EAAA,UAAA,EAAA,CAAA;kBADvB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAqC3B,QAAQ;;0BAAI,QAAQ;;0BACpB,MAAM;2BAAC,6BAA6B,CAAA;;;AClEzC,MAAM,UAAU,GAAG,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,CAAC;MActF,iBAAiB,CAAA;8GAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,YAV1B,aAAa;YACb,YAAY;YACZ,eAAe;YACf,eAAe;AACf,YAAA,cAAc,EARE,oBAAoB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAWrF,EAAA,OAAA,EAAA,CAAA,eAAe,EAXP,oBAAoB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;AAcpF,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,EAFjB,SAAA,EAAA,CAAC,WAAW,CAAC,YARtB,aAAa;YACb,YAAY;YACZ,eAAe;YACf,eAAe;AACf,YAAA,cAAc,EAGN,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGd,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAZ7B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,aAAa;wBACb,YAAY;wBACZ,eAAe;wBACf,eAAe;wBACf,cAAc;AACd,wBAAA,GAAG,UAAU;AACd,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,GAAG,UAAU,CAAC;oBACzC,SAAS,EAAE,CAAC,WAAW,CAAC;AACzB,iBAAA,CAAA;;;AChCD;;AAEG;;;;"}