{"version": 3, "file": "sort.mjs", "sources": ["../../../../../../src/material/sort/sort-errors.ts", "../../../../../../src/material/sort/sort.ts", "../../../../../../src/material/sort/sort-animations.ts", "../../../../../../src/material/sort/sort-header-intl.ts", "../../../../../../src/material/sort/sort-header.ts", "../../../../../../src/material/sort/sort-header.html", "../../../../../../src/material/sort/sort-module.ts", "../../../../../../src/material/sort/sort_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** @docs-private */\nexport function getSortDuplicateSortableIdError(id: string): Error {\n  return Error(`Cannot have two MatSortables with the same id (${id}).`);\n}\n\n/** @docs-private */\nexport function getSortHeaderNotContainedWithinSortError(): Error {\n  return Error(`MatSortHeader must be placed within a parent element with the MatSort directive.`);\n}\n\n/** @docs-private */\nexport function getSortHeaderMissingIdError(): Error {\n  return Error(`MatSortHeader must be provided with a unique id.`);\n}\n\n/** @docs-private */\nexport function getSortInvalidDirectionError(direction: string): Error {\n  return Error(`${direction} is not a valid sort direction ('asc' or 'desc').`);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  Directive,\n  EventEmitter,\n  Inject,\n  InjectionToken,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  Optional,\n  Output,\n  booleanAttribute,\n} from '@angular/core';\nimport {Observable, ReplaySubject, Subject} from 'rxjs';\nimport {SortDirection} from './sort-direction';\nimport {\n  getSortDuplicateSortableIdError,\n  getSortHeaderMissingIdError,\n  getSortInvalidDirectionError,\n} from './sort-errors';\n\n/** Position of the arrow that displays when sorted. */\nexport type SortHeaderArrowPosition = 'before' | 'after';\n\n/** Interface for a directive that holds sorting state consumed by `MatSortHeader`. */\nexport interface MatSortable {\n  /** The id of the column being sorted. */\n  id: string;\n\n  /** Starting sort direction. */\n  start: SortDirection;\n\n  /** Whether to disable clearing the sorting state. */\n  disableClear: boolean;\n}\n\n/** The current sort state. */\nexport interface Sort {\n  /** The id of the column being sorted. */\n  active: string;\n\n  /** The sort direction. */\n  direction: SortDirection;\n}\n\n/** Default options for `mat-sort`.  */\nexport interface MatSortDefaultOptions {\n  /** Whether to disable clearing the sorting state. */\n  disableClear?: boolean;\n  /** Position of the arrow that displays when sorted. */\n  arrowPosition?: SortHeaderArrowPosition;\n}\n\n/** Injection token to be used to override the default options for `mat-sort`. */\nexport const MAT_SORT_DEFAULT_OPTIONS = new InjectionToken<MatSortDefaultOptions>(\n  'MAT_SORT_DEFAULT_OPTIONS',\n);\n\n/** Container for MatSortables to manage the sort state and provide default sort parameters. */\n@Directive({\n  selector: '[matSort]',\n  exportAs: 'matSort',\n  host: {\n    'class': 'mat-sort',\n  },\n  standalone: true,\n})\nexport class MatSort implements OnChanges, OnDestroy, OnInit {\n  private _initializedStream = new ReplaySubject<void>(1);\n\n  /** Collection of all registered sortables that this directive manages. */\n  sortables = new Map<string, MatSortable>();\n\n  /** Used to notify any child components listening to state changes. */\n  readonly _stateChanges = new Subject<void>();\n\n  /** The id of the most recently sorted MatSortable. */\n  @Input('matSortActive') active: string;\n\n  /**\n   * The direction to set when an MatSortable is initially sorted.\n   * May be overridden by the MatSortable's sort start.\n   */\n  @Input('matSortStart') start: SortDirection = 'asc';\n\n  /** The sort direction of the currently active MatSortable. */\n  @Input('matSortDirection')\n  get direction(): SortDirection {\n    return this._direction;\n  }\n  set direction(direction: SortDirection) {\n    if (\n      direction &&\n      direction !== 'asc' &&\n      direction !== 'desc' &&\n      (typeof ngDevMode === 'undefined' || ngDevMode)\n    ) {\n      throw getSortInvalidDirectionError(direction);\n    }\n    this._direction = direction;\n  }\n  private _direction: SortDirection = '';\n\n  /**\n   * Whether to disable the user from clearing the sort by finishing the sort direction cycle.\n   * May be overridden by the MatSortable's disable clear input.\n   */\n  @Input({alias: 'matSortDisableClear', transform: booleanAttribute})\n  disableClear: boolean;\n\n  /** Whether the sortable is disabled. */\n  @Input({alias: 'matSortDisabled', transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Event emitted when the user changes either the active sort or sort direction. */\n  @Output('matSortChange') readonly sortChange: EventEmitter<Sort> = new EventEmitter<Sort>();\n\n  /** Emits when the paginator is initialized. */\n  initialized: Observable<void> = this._initializedStream;\n\n  constructor(\n    @Optional()\n    @Inject(MAT_SORT_DEFAULT_OPTIONS)\n    private _defaultOptions?: MatSortDefaultOptions,\n  ) {}\n\n  /**\n   * Register function to be used by the contained MatSortables. Adds the MatSortable to the\n   * collection of MatSortables.\n   */\n  register(sortable: MatSortable): void {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!sortable.id) {\n        throw getSortHeaderMissingIdError();\n      }\n\n      if (this.sortables.has(sortable.id)) {\n        throw getSortDuplicateSortableIdError(sortable.id);\n      }\n    }\n\n    this.sortables.set(sortable.id, sortable);\n  }\n\n  /**\n   * Unregister function to be used by the contained MatSortables. Removes the MatSortable from the\n   * collection of contained MatSortables.\n   */\n  deregister(sortable: MatSortable): void {\n    this.sortables.delete(sortable.id);\n  }\n\n  /** Sets the active sort id and determines the new sort direction. */\n  sort(sortable: MatSortable): void {\n    if (this.active != sortable.id) {\n      this.active = sortable.id;\n      this.direction = sortable.start ? sortable.start : this.start;\n    } else {\n      this.direction = this.getNextSortDirection(sortable);\n    }\n\n    this.sortChange.emit({active: this.active, direction: this.direction});\n  }\n\n  /** Returns the next sort direction of the active sortable, checking for potential overrides. */\n  getNextSortDirection(sortable: MatSortable): SortDirection {\n    if (!sortable) {\n      return '';\n    }\n\n    // Get the sort direction cycle with the potential sortable overrides.\n    const disableClear =\n      sortable?.disableClear ?? this.disableClear ?? !!this._defaultOptions?.disableClear;\n    let sortDirectionCycle = getSortDirectionCycle(sortable.start || this.start, disableClear);\n\n    // Get and return the next direction in the cycle\n    let nextDirectionIndex = sortDirectionCycle.indexOf(this.direction) + 1;\n    if (nextDirectionIndex >= sortDirectionCycle.length) {\n      nextDirectionIndex = 0;\n    }\n    return sortDirectionCycle[nextDirectionIndex];\n  }\n\n  ngOnInit() {\n    this._initializedStream.next();\n  }\n\n  ngOnChanges() {\n    this._stateChanges.next();\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._initializedStream.complete();\n  }\n}\n\n/** Returns the sort direction cycle to use given the provided parameters of order and clear. */\nfunction getSortDirectionCycle(start: SortDirection, disableClear: boolean): SortDirection[] {\n  let sortOrder: SortDirection[] = ['asc', 'desc'];\n  if (start == 'desc') {\n    sortOrder.reverse();\n  }\n  if (!disableClear) {\n    sortOrder.push('');\n  }\n\n  return sortOrder;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  state,\n  style,\n  transition,\n  trigger,\n  keyframes,\n  AnimationTriggerMetadata,\n  query,\n  animateChild,\n} from '@angular/animations';\nimport {AnimationCurves, AnimationDurations} from '@angular/material/core';\n\nconst SORT_ANIMATION_TRANSITION =\n  AnimationDurations.ENTERING + ' ' + AnimationCurves.STANDARD_CURVE;\n\n/**\n * Animations used by MatSort.\n * @docs-private\n */\nexport const matSortAnimations: {\n  readonly indicator: AnimationTriggerMetadata;\n  readonly leftPointer: AnimationTriggerMetadata;\n  readonly rightPointer: AnimationTriggerMetadata;\n  readonly arrowOpacity: AnimationTriggerMetadata;\n  readonly arrowPosition: AnimationTriggerMetadata;\n  readonly allowChildren: AnimationTriggerMetadata;\n} = {\n  /** Animation that moves the sort indicator. */\n  indicator: trigger('indicator', [\n    state('active-asc, asc', style({transform: 'translateY(0px)'})),\n    // 10px is the height of the sort indicator, minus the width of the pointers\n    state('active-desc, desc', style({transform: 'translateY(10px)'})),\n    transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  ]),\n\n  /** Animation that rotates the left pointer of the indicator based on the sorting direction. */\n  leftPointer: trigger('leftPointer', [\n    state('active-asc, asc', style({transform: 'rotate(-45deg)'})),\n    state('active-desc, desc', style({transform: 'rotate(45deg)'})),\n    transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  ]),\n\n  /** Animation that rotates the right pointer of the indicator based on the sorting direction. */\n  rightPointer: trigger('rightPointer', [\n    state('active-asc, asc', style({transform: 'rotate(45deg)'})),\n    state('active-desc, desc', style({transform: 'rotate(-45deg)'})),\n    transition('active-asc <=> active-desc', animate(SORT_ANIMATION_TRANSITION)),\n  ]),\n\n  /** Animation that controls the arrow opacity. */\n  arrowOpacity: trigger('arrowOpacity', [\n    state('desc-to-active, asc-to-active, active', style({opacity: 1})),\n    state('desc-to-hint, asc-to-hint, hint', style({opacity: 0.54})),\n    state(\n      'hint-to-desc, active-to-desc, desc, hint-to-asc, active-to-asc, asc, void',\n      style({opacity: 0}),\n    ),\n    // Transition between all states except for immediate transitions\n    transition('* => asc, * => desc, * => active, * => hint, * => void', animate('0ms')),\n    transition('* <=> *', animate(SORT_ANIMATION_TRANSITION)),\n  ]),\n\n  /**\n   * Animation for the translation of the arrow as a whole. States are separated into two\n   * groups: ones with animations and others that are immediate. Immediate states are asc, desc,\n   * peek, and active. The other states define a specific animation (source-to-destination)\n   * and are determined as a function of their prev user-perceived state and what the next state\n   * should be.\n   */\n  arrowPosition: trigger('arrowPosition', [\n    // Hidden Above => Hint Center\n    transition(\n      '* => desc-to-hint, * => desc-to-active',\n      animate(\n        SORT_ANIMATION_TRANSITION,\n        keyframes([style({transform: 'translateY(-25%)'}), style({transform: 'translateY(0)'})]),\n      ),\n    ),\n    // Hint Center => Hidden Below\n    transition(\n      '* => hint-to-desc, * => active-to-desc',\n      animate(\n        SORT_ANIMATION_TRANSITION,\n        keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(25%)'})]),\n      ),\n    ),\n    // Hidden Below => Hint Center\n    transition(\n      '* => asc-to-hint, * => asc-to-active',\n      animate(\n        SORT_ANIMATION_TRANSITION,\n        keyframes([style({transform: 'translateY(25%)'}), style({transform: 'translateY(0)'})]),\n      ),\n    ),\n    // Hint Center => Hidden Above\n    transition(\n      '* => hint-to-asc, * => active-to-asc',\n      animate(\n        SORT_ANIMATION_TRANSITION,\n        keyframes([style({transform: 'translateY(0)'}), style({transform: 'translateY(-25%)'})]),\n      ),\n    ),\n    state(\n      'desc-to-hint, asc-to-hint, hint, desc-to-active, asc-to-active, active',\n      style({transform: 'translateY(0)'}),\n    ),\n    state('hint-to-desc, active-to-desc, desc', style({transform: 'translateY(-25%)'})),\n    state('hint-to-asc, active-to-asc, asc', style({transform: 'translateY(25%)'})),\n  ]),\n\n  /** Necessary trigger that calls animate on children animations. */\n  allowChildren: trigger('allowChildren', [\n    transition('* <=> *', [query('@*', animateChild(), {optional: true})]),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable, SkipSelf, Optional} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatSortHeaderIntl and\n * include it in a custom provider.\n */\n@Injectable({providedIn: 'root'})\nexport class MatSortHeaderIntl {\n  /**\n   * Stream that emits whenever the labels here are changed. Use this to notify\n   * components if the labels have changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n}\n\n/** @docs-private */\nexport function MAT_SORT_HEADER_INTL_PROVIDER_FACTORY(parentIntl: MatSortHeaderIntl) {\n  return parentIntl || new MatSortHeaderIntl();\n}\n\n/** @docs-private */\nexport const MAT_SORT_HEADER_INTL_PROVIDER = {\n  // If there is already an MatSortHeaderIntl available, use that. Otherwise, provide a new one.\n  provide: MatSortHeaderIntl,\n  deps: [[new Optional(), new SkipSelf(), MatSortHeaderIntl]],\n  useFactory: MAT_SORT_HEADER_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AriaDescriber, FocusMonitor} from '@angular/cdk/a11y';\nimport {ENTER, SPACE} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Inject,\n  Input,\n  OnDestroy,\n  OnInit,\n  Optional,\n  ViewEncapsulation,\n  booleanAttribute,\n} from '@angular/core';\nimport {merge, Subscription} from 'rxjs';\nimport {\n  MAT_SORT_DEFAULT_OPTIONS,\n  MatSort,\n  MatSortable,\n  MatSortDefaultOptions,\n  SortHeaderArrowPosition,\n} from './sort';\nimport {matSortAnimations} from './sort-animations';\nimport {SortDirection} from './sort-direction';\nimport {getSortHeaderNotContainedWithinSortError} from './sort-errors';\nimport {MatSortHeaderIntl} from './sort-header-intl';\n\n/**\n * Valid positions for the arrow to be in for its opacity and translation. If the state is a\n * sort direction, the position of the arrow will be above/below and opacity 0. If the state is\n * hint, the arrow will be in the center with a slight opacity. Active state means the arrow will\n * be fully opaque in the center.\n *\n * @docs-private\n */\nexport type ArrowViewState = SortDirection | 'hint' | 'active';\n\n/**\n * States describing the arrow's animated position (animating fromState to toState).\n * If the fromState is not defined, there will be no animated transition to the toState.\n * @docs-private\n */\nexport interface ArrowViewStateTransition {\n  fromState?: ArrowViewState;\n  toState?: ArrowViewState;\n}\n\n/** Column definition associated with a `MatSortHeader`. */\ninterface MatSortHeaderColumnDef {\n  name: string;\n}\n\n/**\n * Applies sorting behavior (click to change sort) and styles to an element, including an\n * arrow to display the current sort direction.\n *\n * Must be provided with an id and contained within a parent MatSort directive.\n *\n * If used on header cells in a CdkTable, it will automatically default its id from its containing\n * column definition.\n */\n@Component({\n  selector: '[mat-sort-header]',\n  exportAs: 'matSortHeader',\n  templateUrl: 'sort-header.html',\n  styleUrl: 'sort-header.css',\n  host: {\n    'class': 'mat-sort-header',\n    '(click)': '_handleClick()',\n    '(keydown)': '_handleKeydown($event)',\n    '(mouseenter)': '_setIndicatorHintVisible(true)',\n    '(mouseleave)': '_setIndicatorHintVisible(false)',\n    '[attr.aria-sort]': '_getAriaSortAttribute()',\n    '[class.mat-sort-header-disabled]': '_isDisabled()',\n  },\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  animations: [\n    matSortAnimations.indicator,\n    matSortAnimations.leftPointer,\n    matSortAnimations.rightPointer,\n    matSortAnimations.arrowOpacity,\n    matSortAnimations.arrowPosition,\n    matSortAnimations.allowChildren,\n  ],\n  standalone: true,\n})\nexport class MatSortHeader implements MatSortable, OnDestroy, OnInit, AfterViewInit {\n  private _rerenderSubscription: Subscription;\n\n  /**\n   * The element with role=\"button\" inside this component's view. We need this\n   * in order to apply a description with AriaDescriber.\n   */\n  private _sortButton: HTMLElement;\n\n  /**\n   * Flag set to true when the indicator should be displayed while the sort is not active. Used to\n   * provide an affordance that the header is sortable by showing on focus and hover.\n   */\n  _showIndicatorHint: boolean = false;\n\n  /**\n   * The view transition state of the arrow (translation/ opacity) - indicates its `from` and `to`\n   * position through the animation. If animations are currently disabled, the fromState is removed\n   * so that there is no animation displayed.\n   */\n  _viewState: ArrowViewStateTransition = {};\n\n  /** The direction the arrow should be facing according to the current state. */\n  _arrowDirection: SortDirection = '';\n\n  /**\n   * Whether the view state animation should show the transition between the `from` and `to` states.\n   */\n  _disableViewStateAnimation = false;\n\n  /**\n   * ID of this sort header. If used within the context of a CdkColumnDef, this will default to\n   * the column's name.\n   */\n  @Input('mat-sort-header') id: string;\n\n  /** Sets the position of the arrow that displays when sorted. */\n  @Input() arrowPosition: SortHeaderArrowPosition = 'after';\n\n  /** Overrides the sort start value of the containing MatSort for this MatSortable. */\n  @Input() start: SortDirection;\n\n  /** whether the sort header is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /**\n   * Description applied to MatSortHeader's button element with aria-describedby. This text should\n   * describe the action that will occur when the user clicks the sort header.\n   */\n  @Input()\n  get sortActionDescription(): string {\n    return this._sortActionDescription;\n  }\n  set sortActionDescription(value: string) {\n    this._updateSortActionDescription(value);\n  }\n  // Default the action description to \"Sort\" because it's better than nothing.\n  // Without a description, the button's label comes from the sort header text content,\n  // which doesn't give any indication that it performs a sorting operation.\n  private _sortActionDescription: string = 'Sort';\n\n  /** Overrides the disable clear value of the containing MatSort for this MatSortable. */\n  @Input({transform: booleanAttribute})\n  disableClear: boolean;\n\n  constructor(\n    /**\n     * @deprecated `_intl` parameter isn't being used anymore and it'll be removed.\n     * @breaking-change 13.0.0\n     */\n    public _intl: MatSortHeaderIntl,\n    private _changeDetectorRef: ChangeDetectorRef,\n    // `MatSort` is not optionally injected, but just asserted manually w/ better error.\n    // tslint:disable-next-line: lightweight-tokens\n    @Optional() public _sort: MatSort,\n    @Inject('MAT_SORT_HEADER_COLUMN_DEF')\n    @Optional()\n    public _columnDef: MatSortHeaderColumnDef,\n    private _focusMonitor: FocusMonitor,\n    private _elementRef: ElementRef<HTMLElement>,\n    /** @breaking-change 14.0.0 _ariaDescriber will be required. */\n    @Optional() private _ariaDescriber?: AriaDescriber | null,\n    @Optional()\n    @Inject(MAT_SORT_DEFAULT_OPTIONS)\n    defaultOptions?: MatSortDefaultOptions,\n  ) {\n    // Note that we use a string token for the `_columnDef`, because the value is provided both by\n    // `material/table` and `cdk/table` and we can't have the CDK depending on Material,\n    // and we want to avoid having the sort header depending on the CDK table because\n    // of this single reference.\n    if (!_sort && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getSortHeaderNotContainedWithinSortError();\n    }\n\n    if (defaultOptions?.arrowPosition) {\n      this.arrowPosition = defaultOptions?.arrowPosition;\n    }\n\n    this._handleStateChanges();\n  }\n\n  ngOnInit() {\n    if (!this.id && this._columnDef) {\n      this.id = this._columnDef.name;\n    }\n\n    // Initialize the direction of the arrow and set the view state to be immediately that state.\n    this._updateArrowDirection();\n    this._setAnimationTransitionState({\n      toState: this._isSorted() ? 'active' : this._arrowDirection,\n    });\n\n    this._sort.register(this);\n\n    this._sortButton = this._elementRef.nativeElement.querySelector('.mat-sort-header-container')!;\n    this._updateSortActionDescription(this._sortActionDescription);\n  }\n\n  ngAfterViewInit() {\n    // We use the focus monitor because we also want to style\n    // things differently based on the focus origin.\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const newState = !!origin;\n      if (newState !== this._showIndicatorHint) {\n        this._setIndicatorHintVisible(newState);\n        this._changeDetectorRef.markForCheck();\n      }\n    });\n  }\n\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._sort.deregister(this);\n    this._rerenderSubscription.unsubscribe();\n\n    if (this._sortButton) {\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n    }\n  }\n\n  /**\n   * Sets the \"hint\" state such that the arrow will be semi-transparently displayed as a hint to the\n   * user showing what the active sort will become. If set to false, the arrow will fade away.\n   */\n  _setIndicatorHintVisible(visible: boolean) {\n    // No-op if the sort header is disabled - should not make the hint visible.\n    if (this._isDisabled() && visible) {\n      return;\n    }\n\n    this._showIndicatorHint = visible;\n\n    if (!this._isSorted()) {\n      this._updateArrowDirection();\n      if (this._showIndicatorHint) {\n        this._setAnimationTransitionState({fromState: this._arrowDirection, toState: 'hint'});\n      } else {\n        this._setAnimationTransitionState({fromState: 'hint', toState: this._arrowDirection});\n      }\n    }\n  }\n\n  /**\n   * Sets the animation transition view state for the arrow's position and opacity. If the\n   * `disableViewStateAnimation` flag is set to true, the `fromState` will be ignored so that\n   * no animation appears.\n   */\n  _setAnimationTransitionState(viewState: ArrowViewStateTransition) {\n    this._viewState = viewState || {};\n\n    // If the animation for arrow position state (opacity/translation) should be disabled,\n    // remove the fromState so that it jumps right to the toState.\n    if (this._disableViewStateAnimation) {\n      this._viewState = {toState: viewState.toState};\n    }\n  }\n\n  /** Triggers the sort on this sort header and removes the indicator hint. */\n  _toggleOnInteraction() {\n    this._sort.sort(this);\n\n    // Do not show the animation if the header was already shown in the right position.\n    if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n      this._disableViewStateAnimation = true;\n    }\n  }\n\n  _handleClick() {\n    if (!this._isDisabled()) {\n      this._sort.sort(this);\n    }\n  }\n\n  _handleKeydown(event: KeyboardEvent) {\n    if (!this._isDisabled() && (event.keyCode === SPACE || event.keyCode === ENTER)) {\n      event.preventDefault();\n      this._toggleOnInteraction();\n    }\n  }\n\n  /** Whether this MatSortHeader is currently sorted in either ascending or descending order. */\n  _isSorted() {\n    return (\n      this._sort.active == this.id &&\n      (this._sort.direction === 'asc' || this._sort.direction === 'desc')\n    );\n  }\n\n  /** Returns the animation state for the arrow direction (indicator and pointers). */\n  _getArrowDirectionState() {\n    return `${this._isSorted() ? 'active-' : ''}${this._arrowDirection}`;\n  }\n\n  /** Returns the arrow position state (opacity, translation). */\n  _getArrowViewState() {\n    const fromState = this._viewState.fromState;\n    return (fromState ? `${fromState}-to-` : '') + this._viewState.toState;\n  }\n\n  /**\n   * Updates the direction the arrow should be pointing. If it is not sorted, the arrow should be\n   * facing the start direction. Otherwise if it is sorted, the arrow should point in the currently\n   * active sorted direction. The reason this is updated through a function is because the direction\n   * should only be changed at specific times - when deactivated but the hint is displayed and when\n   * the sort is active and the direction changes. Otherwise the arrow's direction should linger\n   * in cases such as the sort becoming deactivated but we want to animate the arrow away while\n   * preserving its direction, even though the next sort direction is actually different and should\n   * only be changed once the arrow displays again (hint or activation).\n   */\n  _updateArrowDirection() {\n    this._arrowDirection = this._isSorted() ? this._sort.direction : this.start || this._sort.start;\n  }\n\n  _isDisabled() {\n    return this._sort.disabled || this.disabled;\n  }\n\n  /**\n   * Gets the aria-sort attribute that should be applied to this sort header. If this header\n   * is not sorted, returns null so that the attribute is removed from the host element. Aria spec\n   * says that the aria-sort property should only be present on one header at a time, so removing\n   * ensures this is true.\n   */\n  _getAriaSortAttribute() {\n    if (!this._isSorted()) {\n      return 'none';\n    }\n\n    return this._sort.direction == 'asc' ? 'ascending' : 'descending';\n  }\n\n  /** Whether the arrow inside the sort header should be rendered. */\n  _renderArrow() {\n    return !this._isDisabled() || this._isSorted();\n  }\n\n  private _updateSortActionDescription(newDescription: string) {\n    // We use AriaDescriber for the sort button instead of setting an `aria-label` because some\n    // screen readers (notably VoiceOver) will read both the column header *and* the button's label\n    // for every *cell* in the table, creating a lot of unnecessary noise.\n\n    // If _sortButton is undefined, the component hasn't been initialized yet so there's\n    // nothing to update in the DOM.\n    if (this._sortButton) {\n      // removeDescription will no-op if there is no existing message.\n      // TODO(jelbourn): remove optional chaining when AriaDescriber is required.\n      this._ariaDescriber?.removeDescription(this._sortButton, this._sortActionDescription);\n      this._ariaDescriber?.describe(this._sortButton, newDescription);\n    }\n\n    this._sortActionDescription = newDescription;\n  }\n\n  /** Handles changes in the sorting state. */\n  private _handleStateChanges() {\n    this._rerenderSubscription = merge(\n      this._sort.sortChange,\n      this._sort._stateChanges,\n      this._intl.changes,\n    ).subscribe(() => {\n      if (this._isSorted()) {\n        this._updateArrowDirection();\n\n        // Do not show the animation if the header was already shown in the right position.\n        if (this._viewState.toState === 'hint' || this._viewState.toState === 'active') {\n          this._disableViewStateAnimation = true;\n        }\n\n        this._setAnimationTransitionState({fromState: this._arrowDirection, toState: 'active'});\n        this._showIndicatorHint = false;\n      }\n\n      // If this header was recently active and now no longer sorted, animate away the arrow.\n      if (!this._isSorted() && this._viewState && this._viewState.toState === 'active') {\n        this._disableViewStateAnimation = false;\n        this._setAnimationTransitionState({fromState: 'active', toState: this._arrowDirection});\n      }\n\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n}\n", "<!--\n  We set the `tabindex` on an element inside the table header, rather than the header itself,\n  because of a bug in NVDA where having a `tabindex` on a `th` breaks keyboard navigation in the\n  table (see https://github.com/nvaccess/nvda/issues/7718). This allows for the header to both\n  be focusable, and have screen readers read out its `aria-sort` state. We prefer this approach\n  over having a button with an `aria-label` inside the header, because the button's `aria-label`\n  will be read out as the user is navigating the table's cell (see #13012).\n\n  The approach is based off of: https://dequeuniversity.com/library/aria/tables/sf-sortable-grid\n-->\n<div class=\"mat-sort-header-container mat-focus-indicator\"\n     [class.mat-sort-header-sorted]=\"_isSorted()\"\n     [class.mat-sort-header-position-before]=\"arrowPosition === 'before'\"\n     [attr.tabindex]=\"_isDisabled() ? null : 0\"\n     [attr.role]=\"_isDisabled() ? null : 'button'\">\n\n  <!--\n    TODO(crisbeto): this div isn't strictly necessary, but we have to keep it due to a large\n    number of screenshot diff failures. It should be removed eventually. Note that the difference\n    isn't visible with a shorter header, but once it breaks up into multiple lines, this element\n    causes it to be center-aligned, whereas removing it will keep the text to the left.\n  -->\n  <div class=\"mat-sort-header-content\">\n    <ng-content></ng-content>\n  </div>\n\n  <!-- Disable animations while a current animation is running -->\n  @if (_renderArrow()) {\n    <div class=\"mat-sort-header-arrow\"\n        [@arrowOpacity]=\"_getArrowViewState()\"\n        [@arrowPosition]=\"_getArrowViewState()\"\n        [@allowChildren]=\"_getArrowDirectionState()\"\n        (@arrowPosition.start)=\"_disableViewStateAnimation = true\"\n        (@arrowPosition.done)=\"_disableViewStateAnimation = false\">\n      <div class=\"mat-sort-header-stem\"></div>\n      <div class=\"mat-sort-header-indicator\" [@indicator]=\"_getArrowDirectionState()\">\n        <div class=\"mat-sort-header-pointer-left\" [@leftPointer]=\"_getArrowDirectionState()\"></div>\n        <div class=\"mat-sort-header-pointer-right\" [@rightPointer]=\"_getArrowDirectionState()\"></div>\n        <div class=\"mat-sort-header-pointer-middle\"></div>\n      </div>\n    </div>\n  }\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatSortHeader} from './sort-header';\nimport {MatSort} from './sort';\nimport {MAT_SORT_HEADER_INTL_PROVIDER} from './sort-header-intl';\nimport {MatCommonModule} from '@angular/material/core';\n\n@NgModule({\n  imports: [MatCommonModule, MatSort, MatSortHeader],\n  exports: [MatSort, MatSortHeader],\n  providers: [MAT_SORT_HEADER_INTL_PROVIDER],\n})\nexport class MatSortModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.MatSortHeaderIntl", "i2.MatSort"], "mappings": ";;;;;;;;AAQA;AACM,SAAU,+BAA+B,CAAC,EAAU,EAAA;AACxD,IAAA,OAAO,KAAK,CAAC,CAAA,+CAAA,EAAkD,EAAE,CAAA,EAAA,CAAI,CAAC,CAAC;AACzE,CAAC;AAED;SACgB,wCAAwC,GAAA;AACtD,IAAA,OAAO,KAAK,CAAC,CAAkF,gFAAA,CAAA,CAAC,CAAC;AACnG,CAAC;AAED;SACgB,2BAA2B,GAAA;AACzC,IAAA,OAAO,KAAK,CAAC,CAAkD,gDAAA,CAAA,CAAC,CAAC;AACnE,CAAC;AAED;AACM,SAAU,4BAA4B,CAAC,SAAiB,EAAA;AAC5D,IAAA,OAAO,KAAK,CAAC,CAAA,EAAG,SAAS,CAAA,iDAAA,CAAmD,CAAC,CAAC;AAChF;;ACmCA;MACa,wBAAwB,GAAG,IAAI,cAAc,CACxD,0BAA0B,EAC1B;AAEF;MASa,OAAO,CAAA;;AAmBlB,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IACD,IAAI,SAAS,CAAC,SAAwB,EAAA;AACpC,QAAA,IACE,SAAS;AACT,YAAA,SAAS,KAAK,KAAK;AACnB,YAAA,SAAS,KAAK,MAAM;aACnB,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAC/C;AACA,YAAA,MAAM,4BAA4B,CAAC,SAAS,CAAC,CAAC;SAC/C;AACD,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;KAC7B;AAoBD,IAAA,WAAA,CAGU,eAAuC,EAAA;QAAvC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAwB;AAvDzC,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,aAAa,CAAO,CAAC,CAAC,CAAC;;AAGxD,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;;AAGlC,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,OAAO,EAAQ,CAAC;AAK7C;;;AAGG;QACoB,IAAK,CAAA,KAAA,GAAkB,KAAK,CAAC;QAkB5C,IAAU,CAAA,UAAA,GAAkB,EAAE,CAAC;;QAWvC,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;AAGQ,QAAA,IAAA,CAAA,UAAU,GAAuB,IAAI,YAAY,EAAQ,CAAC;;AAG5F,QAAA,IAAA,CAAA,WAAW,GAAqB,IAAI,CAAC,kBAAkB,CAAC;KAMpD;AAEJ;;;AAGG;AACH,IAAA,QAAQ,CAAC,QAAqB,EAAA;AAC5B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAChB,MAAM,2BAA2B,EAAE,CAAC;aACrC;YAED,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;AACnC,gBAAA,MAAM,+BAA+B,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACpD;SACF;QAED,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;KAC3C;AAED;;;AAGG;AACH,IAAA,UAAU,CAAC,QAAqB,EAAA;QAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;KACpC;;AAGD,IAAA,IAAI,CAAC,QAAqB,EAAA;QACxB,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,EAAE,EAAE;AAC9B,YAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC1B,YAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAC/D;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;SACtD;AAED,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAC,CAAC,CAAC;KACxE;;AAGD,IAAA,oBAAoB,CAAC,QAAqB,EAAA;QACxC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,OAAO,EAAE,CAAC;SACX;;AAGD,QAAA,MAAM,YAAY,GAChB,QAAQ,EAAE,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,YAAY,CAAC;AACtF,QAAA,IAAI,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;;AAG3F,QAAA,IAAI,kBAAkB,GAAG,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AACxE,QAAA,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,EAAE;YACnD,kBAAkB,GAAG,CAAC,CAAC;SACxB;AACD,QAAA,OAAO,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;KAC/C;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;KAChC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;KAC3B;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;KACpC;AA/HU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,OAAO,kBAuDR,wBAAwB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAvDvB,OAAO,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,MAAA,EAAA,CAAA,eAAA,EAAA,QAAA,CAAA,EAAA,KAAA,EAAA,CAAA,cAAA,EAAA,OAAA,CAAA,EAAA,SAAA,EAAA,CAAA,kBAAA,EAAA,WAAA,CAAA,EAAA,YAAA,EAAA,CAAA,qBAAA,EAAA,cAAA,EAwC+B,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,EAAA,UAAA,EAIpB,gBAAgB,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,UAAA,EAAA,eAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,UAAA,EAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FA5ClD,OAAO,EAAA,UAAA,EAAA,CAAA;kBARnB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,WAAW;AACrB,oBAAA,QAAQ,EAAE,SAAS;AACnB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,UAAU;AACpB,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAuDI,QAAQ;;0BACR,MAAM;2BAAC,wBAAwB,CAAA;yCA7CV,MAAM,EAAA,CAAA;sBAA7B,KAAK;uBAAC,eAAe,CAAA;gBAMC,KAAK,EAAA,CAAA;sBAA3B,KAAK;uBAAC,cAAc,CAAA;gBAIjB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,kBAAkB,CAAA;gBAsBzB,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKlE,QAAQ,EAAA,CAAA;sBADP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAI5B,UAAU,EAAA,CAAA;sBAA3C,MAAM;uBAAC,eAAe,CAAA;;AAkFzB;AACA,SAAS,qBAAqB,CAAC,KAAoB,EAAE,YAAqB,EAAA;AACxE,IAAA,IAAI,SAAS,GAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,IAAA,IAAI,KAAK,IAAI,MAAM,EAAE;QACnB,SAAS,CAAC,OAAO,EAAE,CAAC;KACrB;IACD,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACpB;AAED,IAAA,OAAO,SAAS,CAAC;AACnB;;ACpMA,MAAM,yBAAyB,GAC7B,kBAAkB,CAAC,QAAQ,GAAG,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC;AAErE;;;AAGG;AACU,MAAA,iBAAiB,GAO1B;;AAEF,IAAA,SAAS,EAAE,OAAO,CAAC,WAAW,EAAE;QAC9B,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC,CAAC;;QAE/D,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC,CAAC;AAClE,QAAA,UAAU,CAAC,4BAA4B,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;KAC7E,CAAC;;AAGF,IAAA,WAAW,EAAE,OAAO,CAAC,aAAa,EAAE;QAClC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC,CAAC;QAC9D,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,CAAC;AAC/D,QAAA,UAAU,CAAC,4BAA4B,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;KAC7E,CAAC;;AAGF,IAAA,YAAY,EAAE,OAAO,CAAC,cAAc,EAAE;QACpC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,CAAC;QAC7D,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAC,CAAC;AAChE,QAAA,UAAU,CAAC,4BAA4B,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;KAC7E,CAAC;;AAGF,IAAA,YAAY,EAAE,OAAO,CAAC,cAAc,EAAE;QACpC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,CAAC;QACnE,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;QAChE,KAAK,CACH,2EAA2E,EAC3E,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAC,CAAC,CACpB;;AAED,QAAA,UAAU,CAAC,wDAAwD,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACpF,QAAA,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;KAC1D,CAAC;AAEF;;;;;;AAMG;AACH,IAAA,aAAa,EAAE,OAAO,CAAC,eAAe,EAAE;;AAEtC,QAAA,UAAU,CACR,wCAAwC,EACxC,OAAO,CACL,yBAAyB,EACzB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,CAAC,CAAC,CACzF,CACF;;AAED,QAAA,UAAU,CACR,wCAAwC,EACxC,OAAO,CACL,yBAAyB,EACzB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC,CAAC,CAAC,CACxF,CACF;;AAED,QAAA,UAAU,CACR,sCAAsC,EACtC,OAAO,CACL,yBAAyB,EACzB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,CAAC,CAAC,CACxF,CACF;;AAED,QAAA,UAAU,CACR,sCAAsC,EACtC,OAAO,CACL,yBAAyB,EACzB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC,CAAC,CAAC,CACzF,CACF;QACD,KAAK,CACH,wEAAwE,EACxE,KAAK,CAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAC,CACpC;QACD,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,kBAAkB,EAAC,CAAC,CAAC;QACnF,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC,CAAC,CAAC;KAChF,CAAC;;AAGF,IAAA,aAAa,EAAE,OAAO,CAAC,eAAe,EAAE;AACtC,QAAA,UAAU,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;KACvE,CAAC;;;AC9GJ;;;AAGG;MAEU,iBAAiB,CAAA;AAD9B,IAAA,WAAA,GAAA;AAEE;;;AAGG;AACM,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ,CAAC;AACvD,KAAA;8GANY,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,cADL,MAAM,EAAA,CAAA,CAAA,EAAA;;2FAClB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;AAShC;AACM,SAAU,qCAAqC,CAAC,UAA6B,EAAA;AACjF,IAAA,OAAO,UAAU,IAAI,IAAI,iBAAiB,EAAE,CAAC;AAC/C,CAAC;AAED;AACa,MAAA,6BAA6B,GAAG;;AAE3C,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,iBAAiB,CAAC,CAAC;AAC3D,IAAA,UAAU,EAAE,qCAAqC;;;AC4BnD;;;;;;;;AAQG;MA2BU,aAAa,CAAA;AA8CxB;;;AAGG;AACH,IAAA,IACI,qBAAqB,GAAA;QACvB,OAAO,IAAI,CAAC,sBAAsB,CAAC;KACpC;IACD,IAAI,qBAAqB,CAAC,KAAa,EAAA;AACrC,QAAA,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;KAC1C;AAUD,IAAA,WAAA;AACE;;;AAGG;AACI,IAAA,KAAwB,EACvB,kBAAqC;;;AAG1B,IAAA,KAAc,EAG1B,UAAkC,EACjC,aAA2B,EAC3B,WAAoC;;AAExB,IAAA,cAAqC,EAGzD,cAAsC,EAAA;QAd/B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAmB;QACvB,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QAG1B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAS;QAG1B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAwB;QACjC,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAC3B,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QAExB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAuB;AAzE3D;;;AAGG;QACH,IAAkB,CAAA,kBAAA,GAAY,KAAK,CAAC;AAEpC;;;;AAIG;QACH,IAAU,CAAA,UAAA,GAA6B,EAAE,CAAC;;QAG1C,IAAe,CAAA,eAAA,GAAkB,EAAE,CAAC;AAEpC;;AAEG;QACH,IAA0B,CAAA,0BAAA,GAAG,KAAK,CAAC;;QAS1B,IAAa,CAAA,aAAA,GAA4B,OAAO,CAAC;;QAO1D,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;;;QAgBlB,IAAsB,CAAA,sBAAA,GAAW,MAAM,CAAC;;;;;AA+B9C,QAAA,IAAI,CAAC,KAAK,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC7D,MAAM,wCAAwC,EAAE,CAAC;SAClD;AAED,QAAA,IAAI,cAAc,EAAE,aAAa,EAAE;AACjC,YAAA,IAAI,CAAC,aAAa,GAAG,cAAc,EAAE,aAAa,CAAC;SACpD;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;KAC5B;IAED,QAAQ,GAAA;QACN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;YAC/B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;SAChC;;QAGD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,4BAA4B,CAAC;AAChC,YAAA,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ,GAAG,IAAI,CAAC,eAAe;AAC5D,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAE1B,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,4BAA4B,CAAE,CAAC;AAC/F,QAAA,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;KAChE;IAED,eAAe,GAAA;;;AAGb,QAAA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,SAAS,CAAC,MAAM,IAAG;AACpE,YAAA,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC;AAC1B,YAAA,IAAI,QAAQ,KAAK,IAAI,CAAC,kBAAkB,EAAE;AACxC,gBAAA,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;AACxC,gBAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;aACxC;AACH,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;AAEzC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;SACvF;KACF;AAED;;;AAGG;AACH,IAAA,wBAAwB,CAAC,OAAgB,EAAA;;AAEvC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE;YACjC,OAAO;SACR;AAED,QAAA,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;AAElC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;YACrB,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,gBAAA,IAAI,CAAC,4BAA4B,CAAC,EAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,MAAM,EAAC,CAAC,CAAC;aACvF;iBAAM;AACL,gBAAA,IAAI,CAAC,4BAA4B,CAAC,EAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,EAAC,CAAC,CAAC;aACvF;SACF;KACF;AAED;;;;AAIG;AACH,IAAA,4BAA4B,CAAC,SAAmC,EAAA;AAC9D,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,IAAI,EAAE,CAAC;;;AAIlC,QAAA,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACnC,IAAI,CAAC,UAAU,GAAG,EAAC,OAAO,EAAE,SAAS,CAAC,OAAO,EAAC,CAAC;SAChD;KACF;;IAGD,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;AAGtB,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC9E,YAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;SACxC;KACF;IAED,YAAY,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACvB,YAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACvB;KACF;AAED,IAAA,cAAc,CAAC,KAAoB,EAAA;QACjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;YAC/E,KAAK,CAAC,cAAc,EAAE,CAAC;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;KACF;;IAGD,SAAS,GAAA;QACP,QACE,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE;AAC5B,aAAC,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,EACnE;KACH;;IAGD,uBAAuB,GAAA;AACrB,QAAA,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,SAAS,GAAG,EAAE,CAAA,EAAG,IAAI,CAAC,eAAe,EAAE,CAAC;KACtE;;IAGD,kBAAkB,GAAA;AAChB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC;AAC5C,QAAA,OAAO,CAAC,SAAS,GAAG,CAAA,EAAG,SAAS,CAAM,IAAA,CAAA,GAAG,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;KACxE;AAED;;;;;;;;;AASG;IACH,qBAAqB,GAAA;QACnB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;KACjG;IAED,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC;KAC7C;AAED;;;;;AAKG;IACH,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AACrB,YAAA,OAAO,MAAM,CAAC;SACf;AAED,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,IAAI,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC;KACnE;;IAGD,YAAY,GAAA;QACV,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;KAChD;AAEO,IAAA,4BAA4B,CAAC,cAAsB,EAAA;;;;;;AAOzD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;;;AAGpB,YAAA,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACtF,IAAI,CAAC,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;SACjE;AAED,QAAA,IAAI,CAAC,sBAAsB,GAAG,cAAc,CAAC;KAC9C;;IAGO,mBAAmB,GAAA;QACzB,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAChC,IAAI,CAAC,KAAK,CAAC,UAAU,EACrB,IAAI,CAAC,KAAK,CAAC,aAAa,EACxB,IAAI,CAAC,KAAK,CAAC,OAAO,CACnB,CAAC,SAAS,CAAC,MAAK;AACf,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;gBACpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;;AAG7B,gBAAA,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC9E,oBAAA,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;iBACxC;AAED,gBAAA,IAAI,CAAC,4BAA4B,CAAC,EAAC,SAAS,EAAE,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC,CAAC;AACxF,gBAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;aACjC;;AAGD,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,QAAQ,EAAE;AAChF,gBAAA,IAAI,CAAC,0BAA0B,GAAG,KAAK,CAAC;AACxC,gBAAA,IAAI,CAAC,4BAA4B,CAAC,EAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,eAAe,EAAC,CAAC,CAAC;aACzF;AAED,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;8GA7SU,aAAa,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EA4Ed,4BAA4B,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAQ5B,wBAAwB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AApFvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,iLA2CL,gBAAgB,CAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAoBhB,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,SAAA,EAAA,wBAAA,EAAA,YAAA,EAAA,gCAAA,EAAA,YAAA,EAAA,iCAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gBAAA,EAAA,yBAAA,EAAA,gCAAA,EAAA,eAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EChKrC,wyEA2CA,ED4Cc,MAAA,EAAA,CAAA,o2DAAA,CAAA,EAAA,UAAA,EAAA;AACV,YAAA,iBAAiB,CAAC,SAAS;AAC3B,YAAA,iBAAiB,CAAC,WAAW;AAC7B,YAAA,iBAAiB,CAAC,YAAY;AAC9B,YAAA,iBAAiB,CAAC,YAAY;AAC9B,YAAA,iBAAiB,CAAC,aAAa;AAC/B,YAAA,iBAAiB,CAAC,aAAa;AAChC,SAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,aAAa,EAAA,UAAA,EAAA,CAAA;kBA1BzB,SAAS;+BACE,mBAAmB,EAAA,QAAA,EACnB,eAAe,EAGnB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,iBAAiB;AAC1B,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,WAAW,EAAE,wBAAwB;AACrC,wBAAA,cAAc,EAAE,gCAAgC;AAChD,wBAAA,cAAc,EAAE,iCAAiC;AACjD,wBAAA,kBAAkB,EAAE,yBAAyB;AAC7C,wBAAA,kCAAkC,EAAE,eAAe;AACpD,qBAAA,EAAA,aAAA,EACc,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACnC,UAAA,EAAA;AACV,wBAAA,iBAAiB,CAAC,SAAS;AAC3B,wBAAA,iBAAiB,CAAC,WAAW;AAC7B,wBAAA,iBAAiB,CAAC,YAAY;AAC9B,wBAAA,iBAAiB,CAAC,YAAY;AAC9B,wBAAA,iBAAiB,CAAC,aAAa;AAC/B,wBAAA,iBAAiB,CAAC,aAAa;AAChC,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,QAAA,EAAA,wyEAAA,EAAA,MAAA,EAAA,CAAA,o2DAAA,CAAA,EAAA,CAAA;;0BA6Eb,QAAQ;;0BACR,MAAM;2BAAC,4BAA4B,CAAA;;0BACnC,QAAQ;;0BAKR,QAAQ;;0BACR,QAAQ;;0BACR,MAAM;2BAAC,wBAAwB,CAAA;yCAlDR,EAAE,EAAA,CAAA;sBAA3B,KAAK;uBAAC,iBAAiB,CAAA;gBAGf,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGG,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAIN,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAQhC,qBAAqB,EAAA,CAAA;sBADxB,KAAK;gBAcN,YAAY,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;;;ME7IzB,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAb,aAAa,EAAA,OAAA,EAAA,CAJd,eAAe,EAAE,OAAO,EAAE,aAAa,CAAA,EAAA,OAAA,EAAA,CACvC,OAAO,EAAE,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;AAGrB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAFb,SAAA,EAAA,CAAC,6BAA6B,CAAC,YAFhC,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAId,aAAa,EAAA,UAAA,EAAA,CAAA;kBALzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,aAAa,CAAC;AAClD,oBAAA,OAAO,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;oBACjC,SAAS,EAAE,CAAC,6BAA6B,CAAC;AAC3C,iBAAA,CAAA;;;AClBD;;AAEG;;;;"}