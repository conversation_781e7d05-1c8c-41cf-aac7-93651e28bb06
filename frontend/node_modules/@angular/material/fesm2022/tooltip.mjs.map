{"version": 3, "file": "tooltip.mjs", "sources": ["../../../../../../src/material/tooltip/tooltip.ts", "../../../../../../src/material/tooltip/tooltip.html", "../../../../../../src/material/tooltip/tooltip-animations.ts", "../../../../../../src/material/tooltip/module.ts", "../../../../../../src/material/tooltip/tooltip_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {take, takeUntil} from 'rxjs/operators';\nimport {\n  BooleanInput,\n  coerceBooleanProperty,\n  coerceNumberProperty,\n  NumberInput,\n} from '@angular/cdk/coercion';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  Inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  Optional,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  inject,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {DOCUMENT, NgClass} from '@angular/common';\nimport {normalizePassiveListenerOptions, Platform} from '@angular/cdk/platform';\nimport {AriaDescriber, FocusMonitor} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  ConnectedPosition,\n  ConnectionPositionPair,\n  FlexibleConnectedPositionStrategy,\n  HorizontalConnectionPos,\n  OriginConnectionPosition,\n  Overlay,\n  OverlayConnectionPosition,\n  OverlayRef,\n  ScrollDispatcher,\n  ScrollStrategy,\n  VerticalConnectionPos,\n} from '@angular/cdk/overlay';\nimport {ComponentPortal} from '@angular/cdk/portal';\nimport {Observable, Subject} from 'rxjs';\n\n/** Possible positions for a tooltip. */\nexport type TooltipPosition = 'left' | 'right' | 'above' | 'below' | 'before' | 'after';\n\n/**\n * Options for how the tooltip trigger should handle touch gestures.\n * See `MatTooltip.touchGestures` for more information.\n */\nexport type TooltipTouchGestures = 'auto' | 'on' | 'off';\n\n/** Possible visibility states of a tooltip. */\nexport type TooltipVisibility = 'initial' | 'visible' | 'hidden';\n\n/** Time in ms to throttle repositioning after scroll events. */\nexport const SCROLL_THROTTLE_MS = 20;\n\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nexport function getMatTooltipInvalidPositionError(position: string) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nexport const MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-tooltip-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const overlay = inject(Overlay);\n      return () => overlay.scrollStrategies.reposition({scrollThrottle: SCROLL_THROTTLE_MS});\n    },\n  },\n);\n\n/** @docs-private */\nexport function MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay: Overlay): () => ScrollStrategy {\n  return () => overlay.scrollStrategies.reposition({scrollThrottle: SCROLL_THROTTLE_MS});\n}\n\n/** @docs-private */\nexport const MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n\n/** @docs-private */\nexport function MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY(): MatTooltipDefaultOptions {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500,\n  };\n}\n\n/** Injection token to be used to override the default options for `matTooltip`. */\nexport const MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken<MatTooltipDefaultOptions>(\n  'mat-tooltip-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/** Default `matTooltip` options that can be overridden. */\nexport interface MatTooltipDefaultOptions {\n  /** Default delay when the tooltip is shown. */\n  showDelay: number;\n\n  /** Default delay when the tooltip is hidden. */\n  hideDelay: number;\n\n  /** Default delay when hiding the tooltip on a touch device. */\n  touchendHideDelay: number;\n\n  /** Time between the user putting the pointer on a tooltip trigger and the long press event being fired on a touch device. */\n  touchLongPressShowDelay?: number;\n\n  /** Default touch gesture handling for tooltips. */\n  touchGestures?: TooltipTouchGestures;\n\n  /** Default position for tooltips. */\n  position?: TooltipPosition;\n\n  /**\n   * Default value for whether tooltips should be positioned near the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  positionAtOrigin?: boolean;\n\n  /** Disables the ability for the user to interact with the tooltip element. */\n  disableTooltipInteractivity?: boolean;\n}\n\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nexport const TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\n\nconst PANEL_CLASS = 'tooltip-panel';\n\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({passive: true});\n\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\n@Directive({\n  selector: '[matTooltip]',\n  exportAs: 'matTooltip',\n  host: {\n    'class': 'mat-mdc-tooltip-trigger',\n    '[class.mat-mdc-tooltip-disabled]': 'disabled',\n  },\n  standalone: true,\n})\nexport class MatTooltip implements OnDestroy, AfterViewInit {\n  _overlayRef: OverlayRef | null;\n  _tooltipInstance: TooltipComponent | null;\n\n  private _portal: ComponentPortal<TooltipComponent>;\n  private _position: TooltipPosition = 'below';\n  private _positionAtOrigin: boolean = false;\n  private _disabled: boolean = false;\n  private _tooltipClass: string | string[] | Set<string> | {[key: string]: any};\n  private _scrollStrategy: () => ScrollStrategy;\n  private _viewInitialized = false;\n  private _pointerExitEventsInitialized = false;\n  private readonly _tooltipComponent = TooltipComponent;\n  private _viewportMargin = 8;\n  private _currentPosition: TooltipPosition;\n  private readonly _cssClassPrefix: string = 'mat-mdc';\n\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  @Input('matTooltipPosition')\n  get position(): TooltipPosition {\n    return this._position;\n  }\n\n  set position(value: TooltipPosition) {\n    if (value !== this._position) {\n      this._position = value;\n\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  @Input('matTooltipPositionAtOrigin')\n  get positionAtOrigin(): boolean {\n    return this._positionAtOrigin;\n  }\n\n  set positionAtOrigin(value: BooleanInput) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n\n  /** Disables the display of the tooltip. */\n  @Input('matTooltipDisabled')\n  get disabled(): boolean {\n    return this._disabled;\n  }\n\n  set disabled(value: BooleanInput) {\n    this._disabled = coerceBooleanProperty(value);\n\n    // If tooltip is disabled, hide immediately.\n    if (this._disabled) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n    }\n  }\n\n  /** The default delay in ms before showing the tooltip after show is called */\n  @Input('matTooltipShowDelay')\n  get showDelay(): number {\n    return this._showDelay;\n  }\n\n  set showDelay(value: NumberInput) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n\n  private _showDelay: number;\n\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  @Input('matTooltipHideDelay')\n  get hideDelay(): number {\n    return this._hideDelay;\n  }\n\n  set hideDelay(value: NumberInput) {\n    this._hideDelay = coerceNumberProperty(value);\n\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n\n  private _hideDelay: number;\n\n  /**\n   * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n   * uses a long press gesture to show and hide, however it can conflict with the native browser\n   * gestures. To work around the conflict, Angular Material disables native gestures on the\n   * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n   * elements). The different values for this option configure the touch event handling as follows:\n   * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n   *   browser gestures on particular elements. In particular, it allows text selection on inputs\n   *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n   * - `on` - Enables touch gestures for all elements and disables native\n   *   browser gestures with no exceptions.\n   * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n   *   showing on touch devices.\n   */\n  @Input('matTooltipTouchGestures') touchGestures: TooltipTouchGestures = 'auto';\n\n  /** The message to be displayed in the tooltip */\n  @Input('matTooltip')\n  get message() {\n    return this._message;\n  }\n\n  set message(value: string) {\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n      this._ngZone.runOutsideAngular(() => {\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        Promise.resolve().then(() => {\n          this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n        });\n      });\n    }\n  }\n\n  private _message = '';\n\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  @Input('matTooltipClass')\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n\n  set tooltipClass(value: string | string[] | Set<string> | {[key: string]: any}) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n\n  /** Manually-bound passive event listeners. */\n  private readonly _passiveListeners: (readonly [string, EventListenerOrEventListenerObject])[] =\n    [];\n\n  /** Reference to the current document. */\n  private _document: Document;\n\n  /** Timer started at the last `touchstart` event. */\n  private _touchstartTimeout: ReturnType<typeof setTimeout>;\n\n  /** Emits when the component is destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  constructor(\n    private _overlay: Overlay,\n    private _elementRef: ElementRef<HTMLElement>,\n    private _scrollDispatcher: ScrollDispatcher,\n    private _viewContainerRef: ViewContainerRef,\n    private _ngZone: NgZone,\n    private _platform: Platform,\n    private _ariaDescriber: AriaDescriber,\n    private _focusMonitor: FocusMonitor,\n    @Inject(MAT_TOOLTIP_SCROLL_STRATEGY) scrollStrategy: any,\n    protected _dir: Directionality,\n    @Optional()\n    @Inject(MAT_TOOLTIP_DEFAULT_OPTIONS)\n    private _defaultOptions: MatTooltipDefaultOptions,\n    @Inject(DOCUMENT) _document: any,\n  ) {\n    this._scrollStrategy = scrollStrategy;\n    this._document = _document;\n\n    if (_defaultOptions) {\n      this._showDelay = _defaultOptions.showDelay;\n      this._hideDelay = _defaultOptions.hideDelay;\n\n      if (_defaultOptions.position) {\n        this.position = _defaultOptions.position;\n      }\n\n      if (_defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n      }\n\n      if (_defaultOptions.touchGestures) {\n        this.touchGestures = _defaultOptions.touchGestures;\n      }\n    }\n\n    _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n      }\n    });\n\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n\n    this._focusMonitor\n      .monitor(this._elementRef)\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(origin => {\n        // Note that the focus monitor runs outside the Angular zone.\n        if (!origin) {\n          this._ngZone.run(() => this.hide(0));\n        } else if (origin === 'keyboard') {\n          this._ngZone.run(() => this.show());\n        }\n      });\n  }\n\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n\n    clearTimeout(this._touchstartTimeout);\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n\n    this._destroyed.next();\n    this._destroyed.complete();\n\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay: number = this.showDelay, origin?: {x: number; y: number}): void {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal =\n      this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance\n      .afterHidden()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay: number = this.hideDelay): void {\n    const instance = this._tooltipInstance;\n\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n\n  /** Shows/hides the tooltip */\n  toggle(origin?: {x: number; y: number}): void {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible(): boolean {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n\n  /** Create the overlay config and position strategy */\n  private _createOverlay(origin?: {x: number; y: number}): OverlayRef {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig()\n        .positionStrategy as FlexibleConnectedPositionStrategy;\n\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n\n      this._detach();\n    }\n\n    const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(\n      this._elementRef,\n    );\n\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = this._overlay\n      .position()\n      .flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef)\n      .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n      .withFlexibleDimensions(false)\n      .withViewportMargin(this._viewportMargin)\n      .withScrollableContainers(scrollableAncestors);\n\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n\n    this._overlayRef = this._overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._scrollStrategy(),\n    });\n\n    this._updatePosition(this._overlayRef);\n\n    this._overlayRef\n      .detachments()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this._detach());\n\n    this._overlayRef\n      .outsidePointerEvents()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n\n    this._overlayRef\n      .keydownEvents()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(event => {\n        if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n          event.preventDefault();\n          event.stopPropagation();\n          this._ngZone.run(() => this.hide(0));\n        }\n      });\n\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n\n    return this._overlayRef;\n  }\n\n  /** Detaches the currently-attached tooltip. */\n  private _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n\n    this._tooltipInstance = null;\n  }\n\n  /** Updates the position of the current tooltip. */\n  private _updatePosition(overlayRef: OverlayRef) {\n    const position = overlayRef.getConfig().positionStrategy as FlexibleConnectedPositionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n\n    position.withPositions([\n      this._addOffset({...origin.main, ...overlay.main}),\n      this._addOffset({...origin.fallback, ...overlay.fallback}),\n    ]);\n  }\n\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  protected _addOffset(position: ConnectedPosition): ConnectedPosition {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n\n    return position;\n  }\n\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin(): {main: OriginConnectionPosition; fallback: OriginConnectionPosition} {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition: OriginConnectionPosition;\n\n    if (position == 'above' || position == 'below') {\n      originPosition = {originX: 'center', originY: position == 'above' ? 'top' : 'bottom'};\n    } else if (\n      position == 'before' ||\n      (position == 'left' && isLtr) ||\n      (position == 'right' && !isLtr)\n    ) {\n      originPosition = {originX: 'start', originY: 'center'};\n    } else if (\n      position == 'after' ||\n      (position == 'right' && isLtr) ||\n      (position == 'left' && !isLtr)\n    ) {\n      originPosition = {originX: 'end', originY: 'center'};\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {x, y} = this._invertPosition(originPosition!.originX, originPosition!.originY);\n\n    return {\n      main: originPosition!,\n      fallback: {originX: x, originY: y},\n    };\n  }\n\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition(): {main: OverlayConnectionPosition; fallback: OverlayConnectionPosition} {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition: OverlayConnectionPosition;\n\n    if (position == 'above') {\n      overlayPosition = {overlayX: 'center', overlayY: 'bottom'};\n    } else if (position == 'below') {\n      overlayPosition = {overlayX: 'center', overlayY: 'top'};\n    } else if (\n      position == 'before' ||\n      (position == 'left' && isLtr) ||\n      (position == 'right' && !isLtr)\n    ) {\n      overlayPosition = {overlayX: 'end', overlayY: 'center'};\n    } else if (\n      position == 'after' ||\n      (position == 'right' && isLtr) ||\n      (position == 'left' && !isLtr)\n    ) {\n      overlayPosition = {overlayX: 'start', overlayY: 'center'};\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {x, y} = this._invertPosition(overlayPosition!.overlayX, overlayPosition!.overlayY);\n\n    return {\n      main: overlayPosition!,\n      fallback: {overlayX: x, overlayY: y},\n    };\n  }\n\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  private _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n\n      this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef!.updatePosition();\n        }\n      });\n    }\n  }\n\n  /** Updates the tooltip class */\n  private _setTooltipClass(tooltipClass: string | string[] | Set<string> | {[key: string]: any}) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n\n  /** Inverts an overlay position. */\n  private _invertPosition(x: HorizontalConnectionPos, y: VerticalConnectionPos) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n\n    return {x, y};\n  }\n\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  private _updateCurrentPositionClass(connectionPair: ConnectionPositionPair): void {\n    const {overlayY, originX, originY} = connectionPair;\n    let newPosition: TooltipPosition;\n\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n\n      this._currentPosition = newPosition;\n    }\n  }\n\n  /** Binds the pointer events to the tooltip trigger. */\n  private _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (\n      this._disabled ||\n      !this.message ||\n      !this._viewInitialized ||\n      this._passiveListeners.length\n    ) {\n      return;\n    }\n\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push([\n        'mouseenter',\n        event => {\n          this._setupPointerExitEventsIfNeeded();\n          let point = undefined;\n          if ((event as MouseEvent).x !== undefined && (event as MouseEvent).y !== undefined) {\n            point = event as MouseEvent;\n          }\n          this.show(undefined, point);\n        },\n      ]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n\n      this._passiveListeners.push([\n        'touchstart',\n        event => {\n          const touch = (event as TouchEvent).targetTouches?.[0];\n          const origin = touch ? {x: touch.clientX, y: touch.clientY} : undefined;\n          // Note that it's important that we don't `preventDefault` here,\n          // because it can prevent click events from firing on the element.\n          this._setupPointerExitEventsIfNeeded();\n          clearTimeout(this._touchstartTimeout);\n\n          const DEFAULT_LONGPRESS_DELAY = 500;\n          this._touchstartTimeout = setTimeout(\n            () => this.show(undefined, origin),\n            this._defaultOptions.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY,\n          );\n        },\n      ]);\n    }\n\n    this._addListeners(this._passiveListeners);\n  }\n\n  private _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n\n    const exitListeners: (readonly [string, EventListenerOrEventListenerObject])[] = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(\n        [\n          'mouseleave',\n          event => {\n            const newTarget = (event as MouseEvent).relatedTarget as Node | null;\n            if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n              this.hide();\n            }\n          },\n        ],\n        ['wheel', event => this._wheelListener(event as WheelEvent)],\n      );\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        clearTimeout(this._touchstartTimeout);\n        this.hide(this._defaultOptions.touchendHideDelay);\n      };\n\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n\n  private _addListeners(listeners: (readonly [string, EventListenerOrEventListenerObject])[]) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n\n  private _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n\n  /** Listener for the `wheel` event on the element. */\n  private _wheelListener(event: WheelEvent) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  private _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n        style.userSelect =\n          (style as any).msUserSelect =\n          style.webkitUserSelect =\n          (style as any).MozUserSelect =\n            'none';\n      }\n\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        (style as any).webkitUserDrag = 'none';\n      }\n\n      style.touchAction = 'none';\n      (style as any).webkitTapHighlightColor = 'transparent';\n    }\n  }\n}\n\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\n@Component({\n  selector: 'mat-tooltip-component',\n  templateUrl: 'tooltip.html',\n  styleUrl: 'tooltip.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n    // won't be rendered if the animations are disabled or there is no web animations polyfill.\n    '[style.zoom]': 'isVisible() ? 1 : null',\n    '(mouseleave)': '_handleMouseLeave($event)',\n    'aria-hidden': 'true',\n  },\n  standalone: true,\n  imports: [NgClass],\n})\nexport class TooltipComponent implements OnDestroy {\n  /* Whether the tooltip text overflows to multiple lines */\n  _isMultiline = false;\n\n  /** Message to display in the tooltip */\n  message: string;\n\n  /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n  tooltipClass: string | string[] | Set<string> | {[key: string]: any};\n\n  /** The timeout ID of any current timer set to show the tooltip */\n  private _showTimeoutId: ReturnType<typeof setTimeout> | undefined;\n\n  /** The timeout ID of any current timer set to hide the tooltip */\n  private _hideTimeoutId: ReturnType<typeof setTimeout> | undefined;\n\n  /** Element that caused the tooltip to open. */\n  _triggerElement: HTMLElement;\n\n  /** Amount of milliseconds to delay the closing sequence. */\n  _mouseLeaveHideDelay: number;\n\n  /** Whether animations are currently disabled. */\n  private _animationsDisabled: boolean;\n\n  /** Reference to the internal tooltip element. */\n  @ViewChild('tooltip', {\n    // Use a static query here since we interact directly with\n    // the DOM which can happen before `ngAfterViewInit`.\n    static: true,\n  })\n  _tooltip: ElementRef<HTMLElement>;\n\n  /** Whether interactions on the page should close the tooltip */\n  private _closeOnInteraction = false;\n\n  /** Whether the tooltip is currently visible. */\n  private _isVisible = false;\n\n  /** Subject for notifying that the tooltip has been hidden from the view */\n  private readonly _onHide: Subject<void> = new Subject();\n\n  /** Name of the show animation and the class that toggles it. */\n  private readonly _showAnimation = 'mat-mdc-tooltip-show';\n\n  /** Name of the hide animation and the class that toggles it. */\n  private readonly _hideAnimation = 'mat-mdc-tooltip-hide';\n\n  constructor(\n    private _changeDetectorRef: ChangeDetectorRef,\n    protected _elementRef: ElementRef<HTMLElement>,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay: number): void {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay: number): void {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden(): Observable<void> {\n    return this._onHide;\n  }\n\n  /** Whether the tooltip is being displayed. */\n  isVisible(): boolean {\n    return this._isVisible;\n  }\n\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null!;\n  }\n\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction(): void {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck(): void {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _handleMouseLeave({relatedTarget}: MouseEvent) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget as Node)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  protected _onShow(): void {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n\n  /** Whether the tooltip text has overflown to the next line */\n  private _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({animationName}: AnimationEvent) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n\n  /** Handles the cleanup after an animation has finished. */\n  private _finalizeAnimation(toVisible: boolean) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n\n  /** Toggles the visibility of the tooltip element. */\n  private _toggleVisibility(isVisible: boolean) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (\n        styles.getPropertyValue('animation-duration') === '0s' ||\n        styles.getPropertyValue('animation-name') === 'none'\n      ) {\n        this._animationsDisabled = true;\n      }\n    }\n\n    if (isVisible) {\n      this._onShow();\n    }\n\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n}\n", "<div\n  #tooltip\n  class=\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\"\n  [ngClass]=\"tooltipClass\"\n  (animationend)=\"_handleAnimationEnd($event)\"\n  [class.mdc-tooltip--multiline]=\"_isMultiline\">\n  <div class=\"mdc-tooltip__surface mdc-tooltip__surface-animation\">{{message}}</div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {\n  animate,\n  AnimationTriggerMetadata,\n  state,\n  style,\n  transition,\n  trigger,\n} from '@angular/animations';\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nexport const matTooltipAnimations: {\n  readonly tooltipState: AnimationTriggerMetadata;\n} = {\n  /** Animation that transitions a tooltip in and out. */\n  tooltipState: trigger('state', [\n    // TODO(crisbeto): these values are based on MDC's CSS.\n    // We should be able to use their styles directly once we land #19432.\n    state('initial, void, hidden', style({opacity: 0, transform: 'scale(0.8)'})),\n    state('visible', style({transform: 'scale(1)'})),\n    transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n    transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n  ]),\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {CommonModule} from '@angular/common';\nimport {A11yModule} from '@angular/cdk/a11y';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatCommonModule} from '@angular/material/core';\nimport {\n  MatTooltip,\n  TooltipComponent,\n  MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER,\n} from './tooltip';\n\n@NgModule({\n  imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n  exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n  providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n})\nexport class MatTooltipModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAkEA;AACO,MAAM,kBAAkB,GAAG,GAAG;AAErC;;;AAGG;AACG,SAAU,iCAAiC,CAAC,QAAgB,EAAA;AAChE,IAAA,OAAO,KAAK,CAAC,CAAA,kBAAA,EAAqB,QAAQ,CAAA,aAAA,CAAe,CAAC,CAAC;AAC7D,CAAC;AAED;MACa,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;AAChC,QAAA,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAC,cAAc,EAAE,kBAAkB,EAAC,CAAC,CAAC;KACxF;AACF,CAAA,EACD;AAEF;AACM,SAAU,mCAAmC,CAAC,OAAgB,EAAA;AAClE,IAAA,OAAO,MAAM,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAC,cAAc,EAAE,kBAAkB,EAAC,CAAC,CAAC;AACzF,CAAC;AAED;AACa,MAAA,4CAA4C,GAAG;AAC1D,IAAA,OAAO,EAAE,2BAA2B;IACpC,IAAI,EAAE,CAAC,OAAO,CAAC;AACf,IAAA,UAAU,EAAE,mCAAmC;EAC/C;AAEF;SACgB,mCAAmC,GAAA;IACjD,OAAO;AACL,QAAA,SAAS,EAAE,CAAC;AACZ,QAAA,SAAS,EAAE,CAAC;AACZ,QAAA,iBAAiB,EAAE,IAAI;KACxB,CAAC;AACJ,CAAC;AAED;MACa,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,mCAAmC;AAC7C,CAAA,EACD;AAgCF;;;;AAIG;AACI,MAAM,mBAAmB,GAAG,wBAAwB;AAE3D,MAAM,WAAW,GAAG,eAAe,CAAC;AAEpC;AACA,MAAM,sBAAsB,GAAG,+BAA+B,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC;AAEhF;AACA;AACA,MAAM,8BAA8B,GAAG,CAAC,CAAC;AACzC,MAAM,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,MAAM,SAAS,GAAG,GAAG,CAAC;AAEtB;;;;;AAKG;MAUU,UAAU,CAAA;;AAkBrB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,IAAI,QAAQ,CAAC,KAAsB,EAAA;AACjC,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAEvB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACvC,gBAAA,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,gBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC;aACnC;SACF;KACF;AAED;;;AAGG;AACH,IAAA,IACI,gBAAgB,GAAA;QAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC;KAC/B;IAED,IAAI,gBAAgB,CAAC,KAAmB,EAAA;AACtC,QAAA,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;;AAGD,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;;AAG9C,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACd;aAAM;YACL,IAAI,CAAC,gCAAgC,EAAE,CAAC;SACzC;KACF;;AAGD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,IAAI,SAAS,CAAC,KAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;KAC/C;;AAKD,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,IAAI,SAAS,CAAC,KAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAE9C,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC;SAC9D;KACF;;AAqBD,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IAED,IAAI,OAAO,CAAC,KAAa,EAAA;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;;;;QAKhG,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;QAE1D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC9C,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACd;aAAM;YACL,IAAI,CAAC,gCAAgC,EAAE,CAAC;YACxC,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;;;;;AAKlC,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;AAC1B,oBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACxF,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;KACF;;AAKD,IAAA,IACI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;IAED,IAAI,YAAY,CAAC,KAA6D,EAAA;AAC5E,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC3C;KACF;IAeD,WACU,CAAA,QAAiB,EACjB,WAAoC,EACpC,iBAAmC,EACnC,iBAAmC,EACnC,OAAe,EACf,SAAmB,EACnB,cAA6B,EAC7B,aAA2B,EACE,cAAmB,EAC9C,IAAoB,EAGtB,eAAyC,EAC/B,SAAc,EAAA;QAbxB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAS;QACjB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACpC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAAkB;QACnC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;QACnB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAe;QAC7B,IAAa,CAAA,aAAA,GAAb,aAAa,CAAc;QAEzB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAGtB,IAAe,CAAA,eAAA,GAAf,eAAe,CAA0B;QAlL3C,IAAS,CAAA,SAAA,GAAoB,OAAO,CAAC;QACrC,IAAiB,CAAA,iBAAA,GAAY,KAAK,CAAC;QACnC,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAG3B,IAAgB,CAAA,gBAAA,GAAG,KAAK,CAAC;QACzB,IAA6B,CAAA,6BAAA,GAAG,KAAK,CAAC;QAC7B,IAAiB,CAAA,iBAAA,GAAG,gBAAgB,CAAC;QAC9C,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;QAEX,IAAe,CAAA,eAAA,GAAW,SAAS,CAAC;AAgFrD;;;;;;;;;;;;;AAaG;QAC+B,IAAa,CAAA,aAAA,GAAyB,MAAM,CAAC;QAiCvE,IAAQ,CAAA,QAAA,GAAG,EAAE,CAAC;;QAgBL,IAAiB,CAAA,iBAAA,GAChC,EAAE,CAAC;;AASY,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;AAkBhD,QAAA,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,SAAS,CAAC;AAC5C,YAAA,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,SAAS,CAAC;AAE5C,YAAA,IAAI,eAAe,CAAC,QAAQ,EAAE;AAC5B,gBAAA,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;aAC1C;AAED,YAAA,IAAI,eAAe,CAAC,gBAAgB,EAAE;AACpC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,gBAAgB,CAAC;aAC1D;AAED,YAAA,IAAI,eAAe,CAAC,aAAa,EAAE;AACjC,gBAAA,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;aACpD;SACF;AAED,QAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAC1D,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACxC;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,eAAe,GAAG,8BAA8B,CAAC;KACvD;IAED,eAAe,GAAA;;AAEb,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,gCAAgC,EAAE,CAAC;AAExC,QAAA,IAAI,CAAC,aAAa;AACf,aAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AACzB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAM,IAAG;;YAElB,IAAI,CAAC,MAAM,EAAE;AACX,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACtC;AAAM,iBAAA,IAAI,MAAM,KAAK,UAAU,EAAE;AAChC,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;aACrC;AACH,SAAC,CAAC,CAAC;KACN;AAED;;AAEG;IACH,WAAW,GAAA;AACT,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAErD,QAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAEtC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAC9B;;AAGD,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAI;YACnD,aAAa,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;AAC7E,SAAC,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;AAElC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAE3B,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC9E,QAAA,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;KAClD;;AAGD,IAAA,IAAI,CAAC,KAAgB,GAAA,IAAI,CAAC,SAAS,EAAE,MAA+B,EAAA;AAClE,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC9D,YAAA,IAAI,CAAC,gBAAgB,EAAE,wBAAwB,EAAE,CAAC;YAClD,OAAO;SACR;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,OAAO;AACV,YAAA,IAAI,CAAC,OAAO,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACtF,QAAA,MAAM,QAAQ,IAAI,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC;QACpF,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC1D,QAAA,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC;QAChD,QAAQ;AACL,aAAA,WAAW,EAAE;AACb,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1C,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACtB;;AAGD,IAAA,IAAI,CAAC,KAAA,GAAgB,IAAI,CAAC,SAAS,EAAA;AACjC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAEvC,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE;AACxB,gBAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtB;iBAAM;gBACL,QAAQ,CAAC,wBAAwB,EAAE,CAAC;gBACpC,IAAI,CAAC,OAAO,EAAE,CAAC;aAChB;SACF;KACF;;AAGD,IAAA,MAAM,CAAC,MAA+B,EAAA;QACpC,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;KACvE;;IAGD,iBAAiB,GAAA;AACf,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC;KACrE;;AAGO,IAAA,cAAc,CAAC,MAA+B,EAAA;AACpD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;AAClD,iBAAA,gBAAqD,CAAC;AAEzD,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM,KAAK,gBAAgB,CAAC,OAAO,YAAY,UAAU,EAAE;gBACzF,OAAO,IAAI,CAAC,WAAW,CAAC;aACzB;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;AAED,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,2BAA2B,CAC5E,IAAI,CAAC,WAAW,CACjB,CAAC;;AAGF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC3B,aAAA,QAAQ,EAAE;AACV,aAAA,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1F,aAAA,qBAAqB,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,eAAe,UAAU,CAAC;aACzD,sBAAsB,CAAC,KAAK,CAAC;AAC7B,aAAA,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC;aACxC,wBAAwB,CAAC,mBAAmB,CAAC,CAAC;AAEjD,QAAA,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,IAAG;AAC3E,YAAA,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAExD,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,gBAAA,IAAI,MAAM,CAAC,wBAAwB,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE;;;AAGzF,oBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;iBACtC;aACF;AACH,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YACtC,SAAS,EAAE,IAAI,CAAC,IAAI;AACpB,YAAA,gBAAgB,EAAE,QAAQ;AAC1B,YAAA,UAAU,EAAE,CAAG,EAAA,IAAI,CAAC,eAAe,CAAA,CAAA,EAAI,WAAW,CAAE,CAAA;AACpD,YAAA,cAAc,EAAE,IAAI,CAAC,eAAe,EAAE;AACvC,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAEvC,QAAA,IAAI,CAAC,WAAW;AACb,aAAA,WAAW,EAAE;AACb,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AAEnC,QAAA,IAAI,CAAC,WAAW;AACb,aAAA,oBAAoB,EAAE;AACtB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,CAAC,CAAC;AAEpE,QAAA,IAAI,CAAC,WAAW;AACb,aAAA,aAAa,EAAE;AACf,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChC,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAClF,KAAK,CAAC,cAAc,EAAE,CAAC;gBACvB,KAAK,CAAC,eAAe,EAAE,CAAC;AACxB,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;aACtC;AACH,SAAC,CAAC,CAAC;AAEL,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,2BAA2B,EAAE;YACrD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAG,EAAA,IAAI,CAAC,eAAe,CAAgC,8BAAA,CAAA,CAAC,CAAC;SACzF;QAED,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;;IAGO,OAAO,GAAA;QACb,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;AACtD,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;SAC3B;AAED,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;KAC9B;;AAGO,IAAA,eAAe,CAAC,UAAsB,EAAA;QAC5C,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC,gBAAqD,CAAC;AAC9F,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AACjC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3C,QAAQ,CAAC,aAAa,CAAC;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,EAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,EAAC,CAAC;AAClD,YAAA,IAAI,CAAC,UAAU,CAAC,EAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAC,CAAC;AAC3D,SAAA,CAAC,CAAC;KACJ;;AAGS,IAAA,UAAU,CAAC,QAA2B,EAAA;QAC9C,MAAM,MAAM,GAAG,oBAAoB,CAAC;AACpC,QAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;AAErD,QAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;AAC9B,YAAA,QAAQ,CAAC,OAAO,GAAG,CAAC,MAAM,CAAC;SAC5B;AAAM,aAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,QAAQ,EAAE;AACxC,YAAA,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC;SAC3B;AAAM,aAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;AACvC,YAAA,QAAQ,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;SAC7C;AAAM,aAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;AACrC,YAAA,QAAQ,CAAC,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC;SAC7C;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;AAED;;;AAGG;IACH,UAAU,GAAA;AACR,QAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;AACrD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,IAAI,cAAwC,CAAC;QAE7C,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,EAAE;YAC9C,cAAc,GAAG,EAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG,QAAQ,EAAC,CAAC;SACvF;aAAM,IACL,QAAQ,IAAI,QAAQ;AACpB,aAAC,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC;aAC5B,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAC/B;YACA,cAAc,GAAG,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC;SACxD;aAAM,IACL,QAAQ,IAAI,OAAO;AACnB,aAAC,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC;aAC7B,QAAQ,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAC9B;YACA,cAAc,GAAG,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAC,CAAC;SACtD;AAAM,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACxD,YAAA,MAAM,iCAAiC,CAAC,QAAQ,CAAC,CAAC;SACnD;AAED,QAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,eAAe,CAAC,cAAe,CAAC,OAAO,EAAE,cAAe,CAAC,OAAO,CAAC,CAAC;QAEtF,OAAO;AACL,YAAA,IAAI,EAAE,cAAe;YACrB,QAAQ,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAC;SACnC,CAAC;KACH;;IAGD,mBAAmB,GAAA;AACjB,QAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC;AACrD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,QAAA,IAAI,eAA0C,CAAC;AAE/C,QAAA,IAAI,QAAQ,IAAI,OAAO,EAAE;YACvB,eAAe,GAAG,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;SAC5D;AAAM,aAAA,IAAI,QAAQ,IAAI,OAAO,EAAE;YAC9B,eAAe,GAAG,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;SACzD;aAAM,IACL,QAAQ,IAAI,QAAQ;AACpB,aAAC,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC;aAC5B,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAC/B;YACA,eAAe,GAAG,EAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;SACzD;aAAM,IACL,QAAQ,IAAI,OAAO;AACnB,aAAC,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC;aAC7B,QAAQ,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAC9B;YACA,eAAe,GAAG,EAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC;SAC3D;AAAM,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACxD,YAAA,MAAM,iCAAiC,CAAC,QAAQ,CAAC,CAAC;SACnD;AAED,QAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,eAAe,CAAC,eAAgB,CAAC,QAAQ,EAAE,eAAgB,CAAC,QAAQ,CAAC,CAAC;QAE1F,OAAO;AACL,YAAA,IAAI,EAAE,eAAgB;YACtB,QAAQ,EAAE,EAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAC;SACrC,CAAC;KACH;;IAGO,qBAAqB,GAAA;;;AAG3B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7C,YAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;YAEtC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AACrF,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,oBAAA,IAAI,CAAC,WAAY,CAAC,cAAc,EAAE,CAAC;iBACpC;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;AAGO,IAAA,gBAAgB,CAAC,YAAoE,EAAA;AAC3F,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY,CAAC;AAClD,YAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,CAAC;SACvC;KACF;;IAGO,eAAe,CAAC,CAA0B,EAAE,CAAwB,EAAA;AAC1E,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;AAC1D,YAAA,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,CAAC,GAAG,QAAQ,CAAC;aACd;AAAM,iBAAA,IAAI,CAAC,KAAK,QAAQ,EAAE;gBACzB,CAAC,GAAG,KAAK,CAAC;aACX;SACF;aAAM;AACL,YAAA,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,CAAC,GAAG,OAAO,CAAC;aACb;AAAM,iBAAA,IAAI,CAAC,KAAK,OAAO,EAAE;gBACxB,CAAC,GAAG,KAAK,CAAC;aACX;SACF;AAED,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC;KACf;;AAGO,IAAA,2BAA2B,CAAC,cAAsC,EAAA;QACxE,MAAM,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG,cAAc,CAAC;AACpD,QAAA,IAAI,WAA4B,CAAC;;;AAIjC,QAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;;AAIzB,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AAC1C,gBAAA,WAAW,GAAG,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC;aACpD;iBAAM;AACL,gBAAA,WAAW,GAAG,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;aACtD;SACF;aAAM;AACL,YAAA,WAAW,GAAG,QAAQ,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO,CAAC;SAC9E;AAED,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACzC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;YAEpC,IAAI,UAAU,EAAE;gBACd,MAAM,WAAW,GAAG,CAAG,EAAA,IAAI,CAAC,eAAe,CAAA,CAAA,EAAI,WAAW,CAAA,CAAA,CAAG,CAAC;gBAC9D,UAAU,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACjE,gBAAA,UAAU,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;aACrD;AAED,YAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;SACrC;KACF;;IAGO,gCAAgC,GAAA;;QAEtC,IACE,IAAI,CAAC,SAAS;YACd,CAAC,IAAI,CAAC,OAAO;YACb,CAAC,IAAI,CAAC,gBAAgB;AACtB,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAC7B;YACA,OAAO;SACR;;;AAID,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE;AACvC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBAC1B,YAAY;AACZ,gBAAA,KAAK,IAAG;oBACN,IAAI,CAAC,+BAA+B,EAAE,CAAC;oBACvC,IAAI,KAAK,GAAG,SAAS,CAAC;AACtB,oBAAA,IAAK,KAAoB,CAAC,CAAC,KAAK,SAAS,IAAK,KAAoB,CAAC,CAAC,KAAK,SAAS,EAAE;wBAClF,KAAK,GAAG,KAAmB,CAAC;qBAC7B;AACD,oBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;iBAC7B;AACF,aAAA,CAAC,CAAC;SACJ;AAAM,aAAA,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YACvC,IAAI,CAAC,iCAAiC,EAAE,CAAC;AAEzC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBAC1B,YAAY;AACZ,gBAAA,KAAK,IAAG;oBACN,MAAM,KAAK,GAAI,KAAoB,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;oBACvD,MAAM,MAAM,GAAG,KAAK,GAAG,EAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,EAAC,GAAG,SAAS,CAAC;;;oBAGxE,IAAI,CAAC,+BAA+B,EAAE,CAAC;AACvC,oBAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBAEtC,MAAM,uBAAuB,GAAG,GAAG,CAAC;oBACpC,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAClC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAClC,IAAI,CAAC,eAAe,CAAC,uBAAuB,IAAI,uBAAuB,CACxE,CAAC;iBACH;AACF,aAAA,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAC5C;IAEO,+BAA+B,GAAA;AACrC,QAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACtC,OAAO;SACR;AACD,QAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC;QAE1C,MAAM,aAAa,GAA8D,EAAE,CAAC;AACpF,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE;YACvC,aAAa,CAAC,IAAI,CAChB;gBACE,YAAY;AACZ,gBAAA,KAAK,IAAG;AACN,oBAAA,MAAM,SAAS,GAAI,KAAoB,CAAC,aAA4B,CAAC;AACrE,oBAAA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;wBACvE,IAAI,CAAC,IAAI,EAAE,CAAC;qBACb;iBACF;AACF,aAAA,EACD,CAAC,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,KAAmB,CAAC,CAAC,CAC7D,CAAC;SACH;AAAM,aAAA,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YACvC,IAAI,CAAC,iCAAiC,EAAE,CAAC;YACzC,MAAM,gBAAgB,GAAG,MAAK;AAC5B,gBAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACtC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;AACpD,aAAC,CAAC;AAEF,YAAA,aAAa,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC;SACvF;AAED,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAClC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;KAC/C;AAEO,IAAA,aAAa,CAAC,SAAoE,EAAA;QACxF,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAI;AACtC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;AAC3F,SAAC,CAAC,CAAC;KACJ;IAEO,4BAA4B,GAAA;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;KACvD;;AAGO,IAAA,cAAc,CAAC,KAAiB,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1F,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;;;AAM/C,YAAA,IAAI,mBAAmB,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAC7E,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF;KACF;;IAGO,iCAAiC,GAAA;AACvC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;AAEpC,QAAA,IAAI,QAAQ,KAAK,KAAK,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/C,YAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;;;AAI5B,YAAA,IAAI,QAAQ,KAAK,IAAI,KAAK,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE;AAC1F,gBAAA,KAAK,CAAC,UAAU;AACb,oBAAA,KAAa,CAAC,YAAY;AAC3B,wBAAA,KAAK,CAAC,gBAAgB;AACrB,4BAAA,KAAa,CAAC,aAAa;AAC1B,gCAAA,MAAM,CAAC;aACZ;;;YAID,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC1C,gBAAA,KAAa,CAAC,cAAc,GAAG,MAAM,CAAC;aACxC;AAED,YAAA,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AAC1B,YAAA,KAAa,CAAC,uBAAuB,GAAG,aAAa,CAAC;SACxD;KACF;AA/rBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,EAmLX,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,OAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,gBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,QAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,aAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,EAAA,EAAA,KAAA,EAAA,2BAA2B,EAG3B,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,EAAA,EAAA,KAAA,EAAA,2BAA2B,6BAE3B,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAxLP,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,4BAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,EAAA,aAAA,EAAA,CAAA,yBAAA,EAAA,eAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,SAAA,CAAA,EAAA,YAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,yBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBATtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,yBAAyB;AAClC,wBAAA,kCAAkC,EAAE,UAAU;AAC/C,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAoLI,MAAM;2BAAC,2BAA2B,CAAA;;0BAElC,QAAQ;;0BACR,MAAM;2BAAC,2BAA2B,CAAA;;0BAElC,MAAM;2BAAC,QAAQ,CAAA;yCArKd,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,oBAAoB,CAAA;gBAsBvB,gBAAgB,EAAA,CAAA;sBADnB,KAAK;uBAAC,4BAA4B,CAAA;gBAa/B,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,oBAAoB,CAAA;gBAkBvB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,qBAAqB,CAAA;gBAaxB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,qBAAqB,CAAA;gBA6BM,aAAa,EAAA,CAAA;sBAA9C,KAAK;uBAAC,yBAAyB,CAAA;gBAI5B,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,YAAY,CAAA;gBAkCf,YAAY,EAAA,CAAA;sBADf,KAAK;uBAAC,iBAAiB,CAAA;;AAijB1B;;;AAGG;MAiBU,gBAAgB,CAAA;AAgD3B,IAAA,WAAA,CACU,kBAAqC,EACnC,WAAoC,EACH,aAAsB,EAAA;QAFzD,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAmB;QACnC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;;QAhDhD,IAAY,CAAA,YAAA,GAAG,KAAK,CAAC;;QAgCb,IAAmB,CAAA,mBAAA,GAAG,KAAK,CAAC;;QAG5B,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;;AAGV,QAAA,IAAA,CAAA,OAAO,GAAkB,IAAI,OAAO,EAAE,CAAC;;QAGvC,IAAc,CAAA,cAAA,GAAG,sBAAsB,CAAC;;QAGxC,IAAc,CAAA,cAAA,GAAG,sBAAsB,CAAC;AAOvD,QAAA,IAAI,CAAC,mBAAmB,GAAG,aAAa,KAAK,gBAAgB,CAAC;KAC/D;AAED;;;AAGG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;;AAEhB,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACnC;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;AACpC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC7B,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;SACjC,EAAE,KAAK,CAAC,CAAC;KACX;AAED;;;AAGG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;;AAEhB,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACnC;AAED,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;AACpC,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC9B,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;SACjC,EAAE,KAAK,CAAC,CAAC;KACX;;IAGD,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;;IAGD,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,WAAW,GAAA;QACT,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAK,CAAC;KAC9B;AAED;;;;AAIG;IACH,sBAAsB,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACd;KACF;AAED;;;;AAIG;IACH,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;KACxC;IAED,iBAAiB,CAAC,EAAC,aAAa,EAAa,EAAA;AAC3C,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAqB,CAAC,EAAE;AAC3E,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;aACtC;iBAAM;AACL,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAChC;SACF;KACF;AAED;;;;AAIG;IACO,OAAO,GAAA;AACf,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;KACtB;;IAGO,mBAAmB,GAAA;QACzB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;QACpE,OAAO,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC;KAC5D;;IAGD,mBAAmB,CAAC,EAAC,aAAa,EAAiB,EAAA;AACjD,QAAA,IAAI,aAAa,KAAK,IAAI,CAAC,cAAc,IAAI,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE;YAClF,IAAI,CAAC,kBAAkB,CAAC,aAAa,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC;SAChE;KACF;;IAGD,wBAAwB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACnC;AAED,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACnC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;KACvD;;AAGO,IAAA,kBAAkB,CAAC,SAAkB,EAAA;QAC3C,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;SACjC;AAAM,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SACrB;KACF;;AAGO,IAAA,iBAAiB,CAAC,SAAkB,EAAA;;;;AAI1C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC5C,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AACtC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AACtC,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;AAC5D,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;AACzD,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;SACxC;;;AAID,QAAA,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;AACpF,YAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;;AAGzC,YAAA,IACE,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI;gBACtD,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EACpD;AACA,gBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;aACjC;SACF;QAED,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;AAED,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;AACjD,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;SACpC;KACF;AAxNU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,6EAmDL,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAnDhC,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,EAAA,UAAA,EAAA,EAAA,YAAA,EAAA,wBAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EC74B7B,oTAQA,EAAA,MAAA,EAAA,CAAA,6lJAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDm4BY,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAEN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAhB5B,SAAS;+BACE,uBAAuB,EAAA,aAAA,EAGlB,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;;;AAGJ,wBAAA,cAAc,EAAE,wBAAwB;AACxC,wBAAA,cAAc,EAAE,2BAA2B;AAC3C,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA,EAAA,UAAA,EACW,IAAI,EAAA,OAAA,EACP,CAAC,OAAO,CAAC,EAAA,QAAA,EAAA,oTAAA,EAAA,MAAA,EAAA,CAAA,6lJAAA,CAAA,EAAA,CAAA;;0BAqDf,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCApB3C,QAAQ,EAAA,CAAA;sBALP,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE;;;AAGpB,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA,CAAA;;;AE35BH;;;AAGG;AACU,MAAA,oBAAoB,GAE7B;;AAEF,IAAA,YAAY,EAAE,OAAO,CAAC,OAAO,EAAE;;;AAG7B,QAAA,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAC,CAAC,CAAC;QAC5E,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,EAAC,SAAS,EAAE,UAAU,EAAC,CAAC,CAAC;AAChD,QAAA,UAAU,CAAC,cAAc,EAAE,OAAO,CAAC,kCAAkC,CAAC,CAAC;AACvE,QAAA,UAAU,CAAC,aAAa,EAAE,OAAO,CAAC,iCAAiC,CAAC,CAAC;KACtE,CAAC;;;MCNS,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAJjB,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,gBAAgB,aACtF,UAAU,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,CAAA,EAAA,CAAA,CAAA,EAAA;AAGjE,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EAFhB,SAAA,EAAA,CAAC,4CAA4C,CAAC,YAF/C,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAC1B,eAAe,EAAE,mBAAmB,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAGjE,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,gBAAgB,CAAC;oBACjG,OAAO,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,CAAC;oBAC7E,SAAS,EAAE,CAAC,4CAA4C,CAAC;AAC1D,iBAAA,CAAA;;;ACxBD;;AAEG;;;;"}