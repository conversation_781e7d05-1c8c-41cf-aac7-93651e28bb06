{"version": 3, "file": "slider.mjs", "sources": ["../../../../../../src/material/slider/slider-interface.ts", "../../../../../../src/material/slider/slider-thumb.ts", "../../../../../../src/material/slider/slider-thumb.html", "../../../../../../src/material/slider/slider.ts", "../../../../../../src/material/slider/slider.html", "../../../../../../src/material/slider/slider-input.ts", "../../../../../../src/material/slider/module.ts", "../../../../../../src/material/slider/slider_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {InjectionToken, ChangeDetectorRef, WritableSignal} from '@angular/core';\nimport {MatRipple, RippleGlobalOptions} from '@angular/material/core';\n\n/**\n * Thumb types: range slider has two thumbs (START, END) whereas single point\n * slider only has one thumb (END).\n */\nexport enum _MatThumb {\n  START = 1,\n  END = 2,\n}\n\n/** Tick mark enum, for discrete sliders. */\nexport enum _MatTickMark {\n  ACTIVE = 0,\n  INACTIVE = 1,\n}\n\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER = new InjectionToken<{}>('_MatSlider');\n\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER_THUMB = new InjectionToken<{}>('_MatSliderThumb');\n\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER_RANGE_THUMB = new InjectionToken<{}>('_MatSliderRangeThumb');\n\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER_VISUAL_THUMB = new InjectionToken<{}>('_MatSliderVisualThumb');\n\n/** Represents a drag event emitted by the MatSlider component. */\nexport interface MatSliderDragEvent {\n  /** The MatSliderThumb that was interacted with. */\n  source: _MatSliderThumb;\n\n  /** The MatSlider that was interacted with. */\n  parent: _MatSlider;\n\n  /** The current value of the slider. */\n  value: number;\n}\n\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatSliderChange {\n  /** The MatSliderThumb that was interacted with. */\n  source: _MatSliderThumb;\n\n  /** The MatSlider that was interacted with. */\n  parent: _MatSlider;\n\n  /** The new value of the source slider. */\n  value: number;\n}\n\nexport interface _MatSlider {\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event: PointerEvent, rect: DOMRect): boolean;\n\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition: _MatThumb): _MatSliderThumb | _MatSliderRangeThumb | undefined;\n\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition: _MatThumb): _MatSliderVisualThumb;\n\n  /** The minimum value that the slider can have. */\n  min: number;\n\n  /** The maximum value that the slider can have. */\n  max: number;\n\n  /** The amount that slider values can increment or decrement by. */\n  step: number;\n\n  /** Whether the slider is disabled. */\n  disabled: boolean;\n\n  /** Whether the slider is a range slider. */\n  _isRange: boolean;\n\n  /** Whether the slider is rtl. */\n  _isRtl: boolean;\n\n  /** The stored width of the host element's bounding client rect. */\n  _cachedWidth: number;\n\n  /** The stored width of the host element's bounding client rect. */\n  _cachedLeft: number;\n\n  /**\n   * The padding of the native slider input. This is added in order to make the region where the\n   * thumb ripple extends past the end of the slider track clickable.\n   */\n  _inputPadding: number;\n\n  /** The radius of the visual slider's ripple. */\n  _rippleRadius: number;\n\n  /** The global configuration for `matRipple` instances. */\n  readonly _globalRippleOptions?: RippleGlobalOptions;\n\n  /** Whether animations have been disabled. */\n  _noopAnimations: boolean;\n\n  /** Whether or not the slider should use animations. */\n  _hasAnimation: boolean;\n\n  /** Triggers UI updates that are needed after a slider input value has changed. */\n  _onValueChange: (source: _MatSliderThumb) => void;\n\n  /** Triggers UI updates that are needed after the slider thumb position has changed. */\n  _onTranslateXChange: (source: _MatSliderThumb) => void;\n\n  /** Updates the stored slider dimensions using the current bounding client rect. */\n  _updateDimensions: () => void;\n\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI: (source: _MatSliderThumb) => void;\n\n  /** Used to set the transition duration for thumb and track animations. */\n  _setTransition: (withAnimation: boolean) => void;\n\n  _cdr: ChangeDetectorRef;\n}\n\nexport interface _MatSliderThumb {\n  /** The minimum value that the slider can have. */\n  min: number;\n\n  /** The maximum value that the slider can have. */\n  max: number;\n\n  /** The amount that slider values can increment or decrement by. */\n  step: number;\n\n  /** The current value of this slider input. */\n  value: number;\n\n  /** The current translateX in px of the slider visual thumb. */\n  translateX: number;\n\n  /** Indicates whether this thumb is the start or end thumb. */\n  thumbPosition: _MatThumb;\n\n  /** Similar to percentage but calcualted using translateX relative to the total track width. */\n  fillPercentage: number;\n\n  /** Whether the slider is disabled. */\n  disabled: boolean;\n\n  /** The host native HTML input element. */\n  _hostElement: HTMLInputElement;\n\n  /** Whether the input is currently focused (either by tab or after clicking). */\n  _isFocused: boolean;\n\n  /** The aria-valuetext string representation of the input's value. */\n  _valuetext: WritableSignal<string>;\n\n  /**\n   * Indicates whether UI updates should be skipped.\n   *\n   * This flag is used to avoid flickering\n   * when correcting values on pointer up/down.\n   */\n  _skipUIUpdate: boolean;\n\n  /** Handles the initialization of properties for the slider input. */\n  initProps: () => void;\n\n  /** Handles UI initialization controlled by this slider input. */\n  initUI: () => void;\n\n  /** Calculates the visual thumb's translateX based on the slider input's current value. */\n  _calcTranslateXByValue: () => number;\n\n  /** Updates the visual thumb based on the slider input's current value. */\n  _updateThumbUIByValue: () => void;\n\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive: () => void;\n\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive: () => void;\n}\n\nexport interface _MatSliderRangeThumb extends _MatSliderThumb {\n  /** Whether this slider corresponds to the input on the left hand side. */\n  _isLeftThumb: boolean;\n\n  /**\n   * Gets the sibling MatSliderRangeThumb.\n   * Returns undefined if it is too early in Angular's life cycle.\n   */\n  getSibling: () => _MatSliderRangeThumb | undefined;\n\n  /** Used to cache whether this slider input corresponds to the visual left thumb. */\n  _setIsLeftThumb: () => void;\n\n  /** Updates the input styles to control whether it is pinned to the start or end of the mat-slider. */\n  _updateStaticStyles: () => void;\n\n  /** Updates the min and max properties of this slider input according to it's sibling. */\n  _updateMinMax: () => void;\n}\n\nexport interface _MatSliderVisualThumb {\n  /** The MatRipple for this slider thumb. */\n  _ripple: MatRipple;\n\n  /** Whether the slider thumb is currently being pressed. */\n  _isActive: boolean;\n\n  /** The host native HTML input element. */\n  _hostElement: HTMLElement;\n\n  /** Shows the value indicator ui. */\n  _showValueIndicator: () => void;\n\n  /** Hides the value indicator ui. */\n  _hideValueIndicator: () => void;\n\n  /** Whether the slider visual thumb is currently showing any ripple. */\n  _isShowingAnyRipple: () => boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {MatRipple, RippleAnimationConfig, RippleRef, RippleState} from '@angular/material/core';\nimport {\n  _MatThumb,\n  _MatSlider,\n  _MatSliderThumb,\n  _MatSliderVisualThumb,\n  MAT_SLIDER,\n  MAT_SLIDER_VISUAL_THUMB,\n} from './slider-interface';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\n@Component({\n  selector: 'mat-slider-visual-thumb',\n  templateUrl: './slider-thumb.html',\n  styleUrl: 'slider-thumb.css',\n  host: {\n    'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [{provide: MAT_SLIDER_VISUAL_THUMB, useExisting: MatSliderVisualThumb}],\n  standalone: true,\n  imports: [MatRipple],\n})\nexport class MatSliderVisualThumb implements _MatSliderVisualThumb, AfterViewInit, OnDestroy {\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  @Input() discrete: boolean;\n\n  /** Indicates which slider thumb this input corresponds to. */\n  @Input() thumbPosition: _MatThumb;\n\n  /** The display value of the slider thumb. */\n  @Input() valueIndicatorText: string;\n\n  /** The MatRipple for this slider thumb. */\n  @ViewChild(MatRipple) readonly _ripple: MatRipple;\n\n  /** The slider thumb knob. */\n  @ViewChild('knob') _knob: ElementRef<HTMLElement>;\n\n  /** The slider thumb value indicator container. */\n  @ViewChild('valueIndicatorContainer')\n  _valueIndicatorContainer: ElementRef<HTMLElement>;\n\n  /** The slider input corresponding to this slider thumb. */\n  private _sliderInput: _MatSliderThumb;\n\n  /** The native html element of the slider input corresponding to this thumb. */\n  private _sliderInputEl: HTMLInputElement | undefined;\n\n  /** The RippleRef for the slider thumbs hover state. */\n  private _hoverRippleRef: RippleRef | undefined;\n\n  /** The RippleRef for the slider thumbs focus state. */\n  private _focusRippleRef: RippleRef | undefined;\n\n  /** The RippleRef for the slider thumbs active state. */\n  private _activeRippleRef: RippleRef | undefined;\n\n  /** Whether the slider thumb is currently being hovered. */\n  private _isHovered: boolean = false;\n\n  /** Whether the slider thumb is currently being pressed. */\n  _isActive = false;\n\n  /** Whether the value indicator tooltip is visible. */\n  _isValueIndicatorVisible: boolean = false;\n\n  /** The host native HTML input element. */\n  _hostElement: HTMLElement;\n\n  private _platform = inject(Platform);\n\n  constructor(\n    readonly _cdr: ChangeDetectorRef,\n    private readonly _ngZone: NgZone,\n    _elementRef: ElementRef<HTMLElement>,\n    @Inject(MAT_SLIDER) private _slider: _MatSlider,\n  ) {\n    this._hostElement = _elementRef.nativeElement;\n  }\n\n  ngAfterViewInit() {\n    this._ripple.radius = 24;\n    this._sliderInput = this._slider._getInput(this.thumbPosition)!;\n    this._sliderInputEl = this._sliderInput._hostElement;\n    const input = this._sliderInputEl;\n\n    // These listeners don't update any data bindings so we bind them outside\n    // of the NgZone to prevent Angular from needlessly running change detection.\n    this._ngZone.runOutsideAngular(() => {\n      input.addEventListener('pointermove', this._onPointerMove);\n      input.addEventListener('pointerdown', this._onDragStart);\n      input.addEventListener('pointerup', this._onDragEnd);\n      input.addEventListener('pointerleave', this._onMouseLeave);\n      input.addEventListener('focus', this._onFocus);\n      input.addEventListener('blur', this._onBlur);\n    });\n  }\n\n  ngOnDestroy() {\n    const input = this._sliderInputEl;\n\n    if (input) {\n      input.removeEventListener('pointermove', this._onPointerMove);\n      input.removeEventListener('pointerdown', this._onDragStart);\n      input.removeEventListener('pointerup', this._onDragEnd);\n      input.removeEventListener('pointerleave', this._onMouseLeave);\n      input.removeEventListener('focus', this._onFocus);\n      input.removeEventListener('blur', this._onBlur);\n    }\n  }\n\n  private _onPointerMove = (event: PointerEvent): void => {\n    if (this._sliderInput._isFocused) {\n      return;\n    }\n\n    const rect = this._hostElement.getBoundingClientRect();\n    const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n    this._isHovered = isHovered;\n\n    if (isHovered) {\n      this._showHoverRipple();\n    } else {\n      this._hideRipple(this._hoverRippleRef);\n    }\n  };\n\n  private _onMouseLeave = (): void => {\n    this._isHovered = false;\n    this._hideRipple(this._hoverRippleRef);\n  };\n\n  private _onFocus = (): void => {\n    // We don't want to show the hover ripple on top of the focus ripple.\n    // Happen when the users cursor is over a thumb and then the user tabs to it.\n    this._hideRipple(this._hoverRippleRef);\n    this._showFocusRipple();\n    this._hostElement.classList.add('mdc-slider__thumb--focused');\n  };\n\n  private _onBlur = (): void => {\n    // Happens when the user tabs away while still dragging a thumb.\n    if (!this._isActive) {\n      this._hideRipple(this._focusRippleRef);\n    }\n    // Happens when the user tabs away from a thumb but their cursor is still over it.\n    if (this._isHovered) {\n      this._showHoverRipple();\n    }\n    this._hostElement.classList.remove('mdc-slider__thumb--focused');\n  };\n\n  private _onDragStart = (event: PointerEvent): void => {\n    if (event.button !== 0) {\n      return;\n    }\n    this._isActive = true;\n    this._showActiveRipple();\n  };\n\n  private _onDragEnd = (): void => {\n    this._isActive = false;\n    this._hideRipple(this._activeRippleRef);\n    // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n    if (!this._sliderInput._isFocused) {\n      this._hideRipple(this._focusRippleRef);\n    }\n\n    // On Safari we need to immediately re-show the hover ripple because\n    // sliders do not retain focus from pointer events on that platform.\n    if (this._platform.SAFARI) {\n      this._showHoverRipple();\n    }\n  };\n\n  /** Handles displaying the hover ripple. */\n  private _showHoverRipple(): void {\n    if (!this._isShowingRipple(this._hoverRippleRef)) {\n      this._hoverRippleRef = this._showRipple({enterDuration: 0, exitDuration: 0});\n      this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n    }\n  }\n\n  /** Handles displaying the focus ripple. */\n  private _showFocusRipple(): void {\n    // Show the focus ripple event if noop animations are enabled.\n    if (!this._isShowingRipple(this._focusRippleRef)) {\n      this._focusRippleRef = this._showRipple({enterDuration: 0, exitDuration: 0}, true);\n      this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n    }\n  }\n\n  /** Handles displaying the active ripple. */\n  private _showActiveRipple(): void {\n    if (!this._isShowingRipple(this._activeRippleRef)) {\n      this._activeRippleRef = this._showRipple({enterDuration: 225, exitDuration: 400});\n      this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n    }\n  }\n\n  /** Whether the given rippleRef is currently fading in or visible. */\n  private _isShowingRipple(rippleRef?: RippleRef): boolean {\n    return rippleRef?.state === RippleState.FADING_IN || rippleRef?.state === RippleState.VISIBLE;\n  }\n\n  /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n  private _showRipple(\n    animation: RippleAnimationConfig,\n    ignoreGlobalRippleConfig?: boolean,\n  ): RippleRef | undefined {\n    if (this._slider.disabled) {\n      return;\n    }\n    this._showValueIndicator();\n    if (this._slider._isRange) {\n      const sibling = this._slider._getThumb(\n        this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START,\n      );\n      sibling._showValueIndicator();\n    }\n    if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n      return;\n    }\n    return this._ripple.launch({\n      animation: this._slider._noopAnimations ? {enterDuration: 0, exitDuration: 0} : animation,\n      centered: true,\n      persistent: true,\n    });\n  }\n\n  /**\n   * Fades out the given ripple.\n   * Also hides the value indicator if no ripple is showing.\n   */\n  private _hideRipple(rippleRef?: RippleRef): void {\n    rippleRef?.fadeOut();\n\n    if (this._isShowingAnyRipple()) {\n      return;\n    }\n\n    if (!this._slider._isRange) {\n      this._hideValueIndicator();\n    }\n\n    const sibling = this._getSibling();\n    if (!sibling._isShowingAnyRipple()) {\n      this._hideValueIndicator();\n      sibling._hideValueIndicator();\n    }\n  }\n\n  /** Shows the value indicator ui. */\n  _showValueIndicator(): void {\n    this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n  }\n\n  /** Hides the value indicator ui. */\n  _hideValueIndicator(): void {\n    this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n  }\n\n  _getSibling(): _MatSliderVisualThumb {\n    return this._slider._getThumb(\n      this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START,\n    );\n  }\n\n  /** Gets the value indicator container's native HTML element. */\n  _getValueIndicatorContainer(): HTMLElement | undefined {\n    return this._valueIndicatorContainer?.nativeElement;\n  }\n\n  /** Gets the native HTML element of the slider thumb knob. */\n  _getKnob(): HTMLElement {\n    return this._knob.nativeElement;\n  }\n\n  _isShowingAnyRipple(): boolean {\n    return (\n      this._isShowingRipple(this._hoverRippleRef) ||\n      this._isShowingRipple(this._focusRippleRef) ||\n      this._isShowingRipple(this._activeRippleRef)\n    );\n  }\n}\n", "@if (discrete) {\n  <div class=\"mdc-slider__value-indicator-container\" #valueIndicatorContainer>\n    <div class=\"mdc-slider__value-indicator\">\n      <span class=\"mdc-slider__value-indicator-text\">{{valueIndicatorText}}</span>\n    </div>\n  </div>\n}\n<div class=\"mdc-slider__thumb-knob\" #knob></div>\n<div matRipple class=\"mat-mdc-focus-indicator\" [matRippleDisabled]=\"true\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  inject,\n  Inject,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  Optional,\n  QueryList,\n  ViewChild,\n  ViewChildren,\n  ViewEncapsulation,\n  ANIMATION_MODULE_TYPE,\n} from '@angular/core';\nimport {MAT_RIPPLE_GLOBAL_OPTIONS, RippleGlobalOptions, ThemePalette} from '@angular/material/core';\nimport {Subscription} from 'rxjs';\nimport {\n  _MatThumb,\n  _MatTickMark,\n  _MatSlider,\n  _MatSliderRangeThumb,\n  _Mat<PERSON>liderThumb,\n  _MatSliderVisualThumb,\n  MAT_SLIDER_RANGE_THUMB,\n  MAT_SLIDER_THUMB,\n  MAT_SLIDER,\n  MAT_SLIDER_VISUAL_THUMB,\n} from './slider-interface';\nimport {MatSliderVisualThumb} from './slider-thumb';\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\n@Component({\n  selector: 'mat-slider',\n  templateUrl: 'slider.html',\n  styleUrl: 'slider.css',\n  host: {\n    'class': 'mat-mdc-slider mdc-slider',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    '[class.mdc-slider--range]': '_isRange',\n    '[class.mdc-slider--disabled]': 'disabled',\n    '[class.mdc-slider--discrete]': 'discrete',\n    '[class.mdc-slider--tick-marks]': 'showTickMarks',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n  },\n  exportAs: 'matSlider',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [{provide: MAT_SLIDER, useExisting: MatSlider}],\n  standalone: true,\n  imports: [MatSliderVisualThumb],\n})\nexport class MatSlider implements AfterViewInit, OnDestroy, _MatSlider {\n  /** The active portion of the slider track. */\n  @ViewChild('trackActive') _trackActive: ElementRef<HTMLElement>;\n\n  /** The slider thumb(s). */\n  @ViewChildren(MAT_SLIDER_VISUAL_THUMB) _thumbs: QueryList<_MatSliderVisualThumb>;\n\n  /** The sliders hidden range input(s). */\n  @ContentChild(MAT_SLIDER_THUMB) _input: _MatSliderThumb;\n\n  /** The sliders hidden range input(s). */\n  @ContentChildren(MAT_SLIDER_RANGE_THUMB, {descendants: false})\n  _inputs: QueryList<_MatSliderRangeThumb>;\n\n  /** Whether the slider is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(v: boolean) {\n    this._disabled = v;\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n\n    if (endInput) {\n      endInput.disabled = this._disabled;\n    }\n    if (startInput) {\n      startInput.disabled = this._disabled;\n    }\n  }\n  private _disabled: boolean = false;\n\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  @Input({transform: booleanAttribute})\n  get discrete(): boolean {\n    return this._discrete;\n  }\n  set discrete(v: boolean) {\n    this._discrete = v;\n    this._updateValueIndicatorUIs();\n  }\n  private _discrete: boolean = false;\n\n  /** Whether the slider displays tick marks along the slider track. */\n  @Input({transform: booleanAttribute})\n  showTickMarks: boolean = false;\n\n  /** The minimum value that the slider can have. */\n  @Input({transform: numberAttribute})\n  get min(): number {\n    return this._min;\n  }\n  set min(v: number) {\n    const min = isNaN(v) ? this._min : v;\n    if (this._min !== min) {\n      this._updateMin(min);\n    }\n  }\n  private _min: number = 0;\n\n  /** Palette color of the slider. */\n  @Input()\n  color: ThemePalette;\n\n  /** Whether ripples are disabled in the slider. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  private _updateMin(min: number): void {\n    const prevMin = this._min;\n    this._min = min;\n    this._isRange ? this._updateMinRange({old: prevMin, new: min}) : this._updateMinNonRange(min);\n    this._onMinMaxOrStepChange();\n  }\n\n  private _updateMinRange(min: {old: number; new: number}): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n\n    startInput.min = min.new;\n    endInput.min = Math.max(min.new, startInput.value);\n    startInput.max = Math.min(endInput.max, endInput.value);\n\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n\n    min.new < min.old\n      ? this._onTranslateXChangeBySideEffect(endInput, startInput)\n      : this._onTranslateXChangeBySideEffect(startInput, endInput);\n\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n\n  private _updateMinNonRange(min: number): void {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n\n      input.min = min;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n\n  /** The maximum value that the slider can have. */\n  @Input({transform: numberAttribute})\n  get max(): number {\n    return this._max;\n  }\n  set max(v: number) {\n    const max = isNaN(v) ? this._max : v;\n    if (this._max !== max) {\n      this._updateMax(max);\n    }\n  }\n  private _max: number = 100;\n\n  private _updateMax(max: number): void {\n    const prevMax = this._max;\n    this._max = max;\n    this._isRange ? this._updateMaxRange({old: prevMax, new: max}) : this._updateMaxNonRange(max);\n    this._onMinMaxOrStepChange();\n  }\n\n  private _updateMaxRange(max: {old: number; new: number}): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n\n    endInput.max = max.new;\n    startInput.max = Math.min(max.new, endInput.value);\n    endInput.min = startInput.value;\n\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n\n    max.new > max.old\n      ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n      : this._onTranslateXChangeBySideEffect(endInput, startInput);\n\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n\n  private _updateMaxNonRange(max: number): void {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n\n      input.max = max;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n\n  /** The values at which the thumb will snap. */\n  @Input({transform: numberAttribute})\n  get step(): number {\n    return this._step;\n  }\n  set step(v: number) {\n    const step = isNaN(v) ? this._step : v;\n    if (this._step !== step) {\n      this._updateStep(step);\n    }\n  }\n  private _step: number = 1;\n\n  private _updateStep(step: number): void {\n    this._step = step;\n    this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n    this._onMinMaxOrStepChange();\n  }\n\n  private _updateStepRange(): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n\n    const prevStartValue = startInput.value;\n\n    endInput.min = this._min;\n    startInput.max = this._max;\n\n    endInput.step = this._step;\n    startInput.step = this._step;\n\n    if (this._platform.SAFARI) {\n      endInput.value = endInput.value;\n      startInput.value = startInput.value;\n    }\n\n    endInput.min = Math.max(this._min, startInput.value);\n    startInput.max = Math.min(this._max, endInput.value);\n\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n\n    endInput.value < prevStartValue\n      ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n      : this._onTranslateXChangeBySideEffect(endInput, startInput);\n\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n\n  private _updateStepNonRange(): void {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n\n      input.step = this._step;\n      if (this._platform.SAFARI) {\n        input.value = input.value;\n      }\n\n      input._updateThumbUIByValue();\n\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n\n  /**\n   * Function that will be used to format the value before it is displayed\n   * in the thumb label. Can be used to format very large number in order\n   * for them to fit into the slider thumb.\n   */\n  @Input() displayWith: (value: number) => string = (value: number) => `${value}`;\n\n  /** Used to keep track of & render the active & inactive tick marks on the slider track. */\n  _tickMarks: _MatTickMark[];\n\n  /** Whether animations have been disabled. */\n  _noopAnimations: boolean;\n\n  /** Subscription to changes to the directionality (LTR / RTL) context for the application. */\n  private _dirChangeSubscription: Subscription;\n\n  /** Observer used to monitor size changes in the slider. */\n  private _resizeObserver: ResizeObserver | null;\n\n  // Stored dimensions to avoid calling getBoundingClientRect redundantly.\n\n  _cachedWidth: number;\n  _cachedLeft: number;\n\n  _rippleRadius: number = 24;\n\n  // The value indicator tooltip text for the visual slider thumb(s).\n\n  /** @docs-private */\n  protected startValueIndicatorText: string = '';\n\n  /** @docs-private */\n  protected endValueIndicatorText: string = '';\n\n  // Used to control the translateX of the visual slider thumb(s).\n\n  _endThumbTransform: string;\n  _startThumbTransform: string;\n\n  _isRange: boolean = false;\n\n  /** Whether the slider is rtl. */\n  _isRtl: boolean = false;\n\n  private _hasViewInitialized: boolean = false;\n\n  /**\n   * The width of the tick mark track.\n   * The tick mark track width is different from full track width\n   */\n  _tickMarkTrackWidth: number = 0;\n\n  _hasAnimation: boolean = false;\n\n  private _resizeTimer: null | ReturnType<typeof setTimeout> = null;\n\n  private _platform = inject(Platform);\n\n  constructor(\n    readonly _ngZone: NgZone,\n    readonly _cdr: ChangeDetectorRef,\n    readonly _elementRef: ElementRef<HTMLElement>,\n    @Optional() readonly _dir: Directionality,\n    @Optional()\n    @Inject(MAT_RIPPLE_GLOBAL_OPTIONS)\n    readonly _globalRippleOptions?: RippleGlobalOptions,\n    @Optional() @Inject(ANIMATION_MODULE_TYPE) animationMode?: string,\n  ) {\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n    this._isRtl = this._dir.value === 'rtl';\n  }\n\n  /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n  _knobRadius: number = 8;\n\n  _inputPadding: number;\n\n  ngAfterViewInit(): void {\n    if (this._platform.isBrowser) {\n      this._updateDimensions();\n    }\n\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n    this._isRange = !!eInput && !!sInput;\n    this._cdr.detectChanges();\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      _validateInputs(\n        this._isRange,\n        this._getInput(_MatThumb.END)!,\n        this._getInput(_MatThumb.START),\n      );\n    }\n\n    const thumb = this._getThumb(_MatThumb.END);\n    this._rippleRadius = thumb._ripple.radius;\n    this._inputPadding = this._rippleRadius - this._knobRadius;\n\n    this._isRange\n      ? this._initUIRange(eInput as _MatSliderRangeThumb, sInput as _MatSliderRangeThumb)\n      : this._initUINonRange(eInput!);\n\n    this._updateTrackUI(eInput!);\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n\n    this._observeHostResize();\n    this._cdr.detectChanges();\n  }\n\n  private _initUINonRange(eInput: _MatSliderThumb): void {\n    eInput.initProps();\n    eInput.initUI();\n\n    this._updateValueIndicatorUI(eInput);\n\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n  }\n\n  private _initUIRange(eInput: _MatSliderRangeThumb, sInput: _MatSliderRangeThumb): void {\n    eInput.initProps();\n    eInput.initUI();\n\n    sInput.initProps();\n    sInput.initUI();\n\n    eInput._updateMinMax();\n    sInput._updateMinMax();\n\n    eInput._updateStaticStyles();\n    sInput._updateStaticStyles();\n\n    this._updateValueIndicatorUIs();\n\n    this._hasViewInitialized = true;\n\n    eInput._updateThumbUIByValue();\n    sInput._updateThumbUIByValue();\n  }\n\n  ngOnDestroy(): void {\n    this._dirChangeSubscription.unsubscribe();\n    this._resizeObserver?.disconnect();\n    this._resizeObserver = null;\n  }\n\n  /** Handles updating the slider ui after a dir change. */\n  private _onDirChange(): void {\n    this._isRtl = this._dir.value === 'rtl';\n    this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n    this._updateTickMarkUI();\n  }\n\n  private _onDirChangeRange(): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    endInput._setIsLeftThumb();\n    startInput._setIsLeftThumb();\n\n    endInput.translateX = endInput._calcTranslateXByValue();\n    startInput.translateX = startInput._calcTranslateXByValue();\n\n    endInput._updateStaticStyles();\n    startInput._updateStaticStyles();\n\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n\n    endInput._updateThumbUIByValue();\n    startInput._updateThumbUIByValue();\n  }\n\n  private _onDirChangeNonRange(): void {\n    const input = this._getInput(_MatThumb.END)!;\n    input._updateThumbUIByValue();\n  }\n\n  /** Starts observing and updating the slider if the host changes its size. */\n  private _observeHostResize() {\n    if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n      return;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeObserver = new ResizeObserver(() => {\n        if (this._isActive()) {\n          return;\n        }\n        if (this._resizeTimer) {\n          clearTimeout(this._resizeTimer);\n        }\n        this._onResize();\n      });\n      this._resizeObserver.observe(this._elementRef.nativeElement);\n    });\n  }\n\n  /** Whether any of the thumbs are currently active. */\n  private _isActive(): boolean {\n    return this._getThumb(_MatThumb.START)._isActive || this._getThumb(_MatThumb.END)._isActive;\n  }\n\n  private _getValue(thumbPosition: _MatThumb = _MatThumb.END): number {\n    const input = this._getInput(thumbPosition);\n    if (!input) {\n      return this.min;\n    }\n    return input.value;\n  }\n\n  private _skipUpdate(): boolean {\n    return !!(\n      this._getInput(_MatThumb.START)?._skipUIUpdate || this._getInput(_MatThumb.END)?._skipUIUpdate\n    );\n  }\n\n  /** Stores the slider dimensions. */\n  _updateDimensions(): void {\n    this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n    this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n  }\n\n  /** Sets the styles for the active portion of the track. */\n  _setTrackActiveStyles(styles: {\n    left: string;\n    right: string;\n    transform: string;\n    transformOrigin: string;\n  }): void {\n    const trackStyle = this._trackActive.nativeElement.style;\n\n    trackStyle.left = styles.left;\n    trackStyle.right = styles.right;\n    trackStyle.transformOrigin = styles.transformOrigin;\n    trackStyle.transform = styles.transform;\n  }\n\n  /** Returns the translateX positioning for a tick mark based on it's index. */\n  _calcTickMarkTransform(index: number): string {\n    // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n    const translateX = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n    return `translateX(${translateX}px`;\n  }\n\n  // Handlers for updating the slider ui.\n\n  _onTranslateXChange(source: _MatSliderThumb): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateThumbUI(source);\n    this._updateTrackUI(source);\n    this._updateOverlappingThumbUI(source as _MatSliderRangeThumb);\n  }\n\n  _onTranslateXChangeBySideEffect(\n    input1: _MatSliderRangeThumb,\n    input2: _MatSliderRangeThumb,\n  ): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    input1._updateThumbUIByValue();\n    input2._updateThumbUIByValue();\n  }\n\n  _onValueChange(source: _MatSliderThumb): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateValueIndicatorUI(source);\n    this._updateTickMarkUI();\n    this._cdr.detectChanges();\n  }\n\n  _onMinMaxOrStepChange(): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.markForCheck();\n  }\n\n  _onResize(): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateDimensions();\n    if (this._isRange) {\n      const eInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n      const sInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n      eInput._updateThumbUIByValue();\n      sInput._updateThumbUIByValue();\n\n      eInput._updateStaticStyles();\n      sInput._updateStaticStyles();\n\n      eInput._updateMinMax();\n      sInput._updateMinMax();\n\n      eInput._updateWidthInactive();\n      sInput._updateWidthInactive();\n    } else {\n      const eInput = this._getInput(_MatThumb.END);\n      if (eInput) {\n        eInput._updateThumbUIByValue();\n      }\n    }\n\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.detectChanges();\n  }\n\n  /** Whether or not the slider thumbs overlap. */\n  private _thumbsOverlap: boolean = false;\n\n  /** Returns true if the slider knobs are overlapping one another. */\n  private _areThumbsOverlapping(): boolean {\n    const startInput = this._getInput(_MatThumb.START);\n    const endInput = this._getInput(_MatThumb.END);\n    if (!startInput || !endInput) {\n      return false;\n    }\n    return endInput.translateX - startInput.translateX < 20;\n  }\n\n  /**\n   * Updates the class names of overlapping slider thumbs so\n   * that the current active thumb is styled to be on \"top\".\n   */\n  private _updateOverlappingThumbClassNames(source: _MatSliderRangeThumb): void {\n    const sibling = source.getSibling()!;\n    const sourceThumb = this._getThumb(source.thumbPosition);\n    const siblingThumb = this._getThumb(sibling.thumbPosition);\n    siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n    sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n  }\n\n  /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n  private _updateOverlappingThumbUI(source: _MatSliderRangeThumb): void {\n    if (!this._isRange || this._skipUpdate()) {\n      return;\n    }\n    if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n      this._thumbsOverlap = !this._thumbsOverlap;\n      this._updateOverlappingThumbClassNames(source);\n    }\n  }\n\n  // _MatThumb styles update conditions\n  //\n  // 1. TranslateX, resize, or dir change\n  //    - Reason: The thumb styles need to be updated according to the new translateX.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n\n  /** Updates the translateX of the given thumb. */\n  _updateThumbUI(source: _MatSliderThumb) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const thumb = this._getThumb(\n      source.thumbPosition === _MatThumb.END ? _MatThumb.END : _MatThumb.START,\n    )!;\n    thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n  }\n\n  // Value indicator text update conditions\n  //\n  // 1. Value\n  //    - Reason: The value displayed needs to be updated.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n\n  /** Updates the value indicator tooltip ui for the given thumb. */\n  _updateValueIndicatorUI(source: _MatSliderThumb): void {\n    if (this._skipUpdate()) {\n      return;\n    }\n\n    const valuetext = this.displayWith(source.value);\n\n    this._hasViewInitialized\n      ? source._valuetext.set(valuetext)\n      : source._hostElement.setAttribute('aria-valuetext', valuetext);\n\n    if (this.discrete) {\n      source.thumbPosition === _MatThumb.START\n        ? (this.startValueIndicatorText = valuetext)\n        : (this.endValueIndicatorText = valuetext);\n\n      const visualThumb = this._getThumb(source.thumbPosition);\n      valuetext.length < 3\n        ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value')\n        : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n    }\n  }\n\n  /** Updates all value indicator UIs in the slider. */\n  private _updateValueIndicatorUIs(): void {\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n\n    if (eInput) {\n      this._updateValueIndicatorUI(eInput);\n    }\n    if (sInput) {\n      this._updateValueIndicatorUI(sInput);\n    }\n  }\n\n  // Update Tick Mark Track Width\n  //\n  // 1. Min, max, or step\n  //    - Reason: The maximum reachable value may have changed.\n  //    - Side note: The maximum reachable value is different from the maximum value set by the\n  //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n  //      reachable value of 95.\n  // 2. Resize\n  //    - Reason: The position for the maximum reachable value needs to be recalculated.\n\n  /** Updates the width of the tick mark track. */\n  private _updateTickMarkTrackUI(): void {\n    if (!this.showTickMarks || this._skipUpdate()) {\n      return;\n    }\n\n    const step = this._step && this._step > 0 ? this._step : 1;\n    const maxValue = Math.floor(this.max / step) * step;\n    const percentage = (maxValue - this.min) / (this.max - this.min);\n    this._tickMarkTrackWidth = this._cachedWidth * percentage - 6;\n  }\n\n  // Track active update conditions\n  //\n  // 1. TranslateX\n  //    - Reason: The track active should line up with the new thumb position.\n  // 2. Min or max\n  //    - Reason #1: The 'active' percentage needs to be recalculated.\n  //    - Reason #2: The value may have silently changed.\n  // 3. Step\n  //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n  // 4. Dir change\n  //    - Reason: The track active will need to be updated according to the new thumb position(s).\n  // 5. Resize\n  //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI(source: _MatSliderThumb): void {\n    if (this._skipUpdate()) {\n      return;\n    }\n\n    this._isRange\n      ? this._updateTrackUIRange(source as _MatSliderRangeThumb)\n      : this._updateTrackUINonRange(source as _MatSliderThumb);\n  }\n\n  private _updateTrackUIRange(source: _MatSliderRangeThumb): void {\n    const sibling = source.getSibling();\n    if (!sibling || !this._cachedWidth) {\n      return;\n    }\n\n    const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n\n    if (source._isLeftThumb && this._cachedWidth) {\n      this._setTrackActiveStyles({\n        left: 'auto',\n        right: `${this._cachedWidth - sibling.translateX}px`,\n        transformOrigin: 'right',\n        transform: `scaleX(${activePercentage})`,\n      });\n    } else {\n      this._setTrackActiveStyles({\n        left: `${sibling.translateX}px`,\n        right: 'auto',\n        transformOrigin: 'left',\n        transform: `scaleX(${activePercentage})`,\n      });\n    }\n  }\n\n  private _updateTrackUINonRange(source: _MatSliderThumb): void {\n    this._isRtl\n      ? this._setTrackActiveStyles({\n          left: 'auto',\n          right: '0px',\n          transformOrigin: 'right',\n          transform: `scaleX(${1 - source.fillPercentage})`,\n        })\n      : this._setTrackActiveStyles({\n          left: '0px',\n          right: 'auto',\n          transformOrigin: 'left',\n          transform: `scaleX(${source.fillPercentage})`,\n        });\n  }\n\n  // Tick mark update conditions\n  //\n  // 1. Value\n  //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n  // 2. Min, max, or step\n  //    - Reason #1: the number of tick marks may have changed.\n  //    - Reason #2: The value may have silently changed.\n\n  /** Updates the dots along the slider track. */\n  _updateTickMarkUI(): void {\n    if (\n      !this.showTickMarks ||\n      this.step === undefined ||\n      this.min === undefined ||\n      this.max === undefined\n    ) {\n      return;\n    }\n    const step = this.step > 0 ? this.step : 1;\n    this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n\n    if (this._isRtl) {\n      this._tickMarks.reverse();\n    }\n  }\n\n  private _updateTickMarkUINonRange(step: number): void {\n    const value = this._getValue();\n    let numActive = Math.max(Math.floor((value - this.min) / step), 0);\n    let numInactive = Math.max(Math.floor((this.max - value) / step), 0);\n    this._isRtl ? numActive++ : numInactive++;\n\n    this._tickMarks = Array(numActive)\n      .fill(_MatTickMark.ACTIVE)\n      .concat(Array(numInactive).fill(_MatTickMark.INACTIVE));\n  }\n\n  private _updateTickMarkUIRange(step: number): void {\n    const endValue = this._getValue();\n    const startValue = this._getValue(_MatThumb.START);\n\n    const numInactiveBeforeStartThumb = Math.max(Math.floor((startValue - this.min) / step), 0);\n    const numActive = Math.max(Math.floor((endValue - startValue) / step) + 1, 0);\n    const numInactiveAfterEndThumb = Math.max(Math.floor((this.max - endValue) / step), 0);\n    this._tickMarks = Array(numInactiveBeforeStartThumb)\n      .fill(_MatTickMark.INACTIVE)\n      .concat(\n        Array(numActive).fill(_MatTickMark.ACTIVE),\n        Array(numInactiveAfterEndThumb).fill(_MatTickMark.INACTIVE),\n      );\n  }\n\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition: _MatThumb): _MatSliderThumb | _MatSliderRangeThumb | undefined {\n    if (thumbPosition === _MatThumb.END && this._input) {\n      return this._input;\n    }\n    if (this._inputs?.length) {\n      return thumbPosition === _MatThumb.START ? this._inputs.first : this._inputs.last;\n    }\n    return;\n  }\n\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition: _MatThumb): _MatSliderVisualThumb {\n    return thumbPosition === _MatThumb.END ? this._thumbs?.last! : this._thumbs?.first!;\n  }\n\n  _setTransition(withAnimation: boolean): void {\n    this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n    this._elementRef.nativeElement.classList.toggle(\n      'mat-mdc-slider-with-animation',\n      this._hasAnimation,\n    );\n  }\n\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event: PointerEvent, rect: DOMRect) {\n    const radius = rect.width / 2;\n    const centerX = rect.x + radius;\n    const centerY = rect.y + radius;\n    const dx = event.clientX - centerX;\n    const dy = event.clientY - centerY;\n    return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n  }\n}\n\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(\n  isRange: boolean,\n  endInputElement: _MatSliderThumb | _MatSliderRangeThumb,\n  startInputElement?: _MatSliderThumb,\n): void {\n  const startValid =\n    !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n  const endValid = endInputElement._hostElement.hasAttribute(\n    isRange ? 'matSliderEndThumb' : 'matSliderThumb',\n  );\n\n  if (!startValid || !endValid) {\n    _throwInvalidInputConfigurationError();\n  }\n}\n\nfunction _throwInvalidInputConfigurationError(): void {\n  throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n", "<!-- Inputs -->\n<ng-content></ng-content>\n\n<!-- Track -->\n<div class=\"mdc-slider__track\">\n  <div class=\"mdc-slider__track--inactive\"></div>\n  <div class=\"mdc-slider__track--active\">\n    <div #trackActive class=\"mdc-slider__track--active_fill\"></div>\n  </div>\n  @if (showTickMarks) {\n    <div class=\"mdc-slider__tick-marks\" #tickMarkContainer>\n      @if (_cachedWidth) {\n        @for (tickMark of _tickMarks; track i; let i = $index) {\n          <div\n            [class]=\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\"\n            [style.transform]=\"_calcTickMarkTransform(i)\"></div>\n        }\n      }\n    </div>\n  }\n</div>\n\n<!-- Thumbs -->\n@if (_isRange) {\n  <mat-slider-visual-thumb\n    [discrete]=\"discrete\"\n    [thumbPosition]=\"1\"\n    [valueIndicatorText]=\"startValueIndicatorText\">\n  </mat-slider-visual-thumb>\n}\n\n<mat-slider-visual-thumb\n  [discrete]=\"discrete\"\n  [thumbPosition]=\"2\"\n  [valueIndicatorText]=\"endValueIndicatorText\">\n</mat-slider-visual-thumb>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  booleanAttribute,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  inject,\n  Inject,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnD<PERSON>roy,\n  Output,\n  signal,\n} from '@angular/core';\nimport {ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {Subject} from 'rxjs';\nimport {\n  _MatThumb,\n  MatSliderDragEvent,\n  _MatSlider,\n  _MatSliderRangeThumb,\n  _MatSliderThumb,\n  MAT_SLIDER_RANGE_THUMB,\n  MAT_SLIDER_THUMB,\n  MAT_SLIDER,\n} from './slider-interface';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nexport const MAT_SLIDER_THUMB_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderThumb),\n  multi: true,\n};\n\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nexport const MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderRangeThumb),\n  multi: true,\n};\n\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\n@Directive({\n  selector: 'input[matSliderThumb]',\n  exportAs: 'matSliderThumb',\n  host: {\n    'class': 'mdc-slider__input',\n    'type': 'range',\n    '[attr.aria-valuetext]': '_valuetext()',\n    '(change)': '_onChange()',\n    '(input)': '_onInput()',\n    // TODO(wagnermaciel): Consider using a global event listener instead.\n    // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n    '(blur)': '_onBlur()',\n    '(focus)': '_onFocus()',\n  },\n  providers: [\n    MAT_SLIDER_THUMB_VALUE_ACCESSOR,\n    {provide: MAT_SLIDER_THUMB, useExisting: MatSliderThumb},\n  ],\n  standalone: true,\n})\nexport class MatSliderThumb implements _MatSliderThumb, OnDestroy, ControlValueAccessor {\n  @Input({transform: numberAttribute})\n  get value(): number {\n    return numberAttribute(this._hostElement.value, 0);\n  }\n  set value(value: number) {\n    value = isNaN(value) ? 0 : value;\n    const stringValue = value + '';\n    if (!this._hasSetInitialValue) {\n      this._initialValue = stringValue;\n      return;\n    }\n    if (this._isActive) {\n      return;\n    }\n    this._setValue(stringValue);\n  }\n\n  /**\n   * Handles programmatic value setting. This has been split out to\n   * allow the range thumb to override it and add additional necessary logic.\n   */\n  protected _setValue(value: string) {\n    this._hostElement.value = value;\n    this._updateThumbUIByValue();\n    this._slider._onValueChange(this);\n    this._cdr.detectChanges();\n    this._slider._cdr.markForCheck();\n  }\n\n  /** Event emitted when the `value` is changed. */\n  @Output() readonly valueChange: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted when the slider thumb starts being dragged. */\n  @Output() readonly dragStart: EventEmitter<MatSliderDragEvent> =\n    new EventEmitter<MatSliderDragEvent>();\n\n  /** Event emitted when the slider thumb stops being dragged. */\n  @Output() readonly dragEnd: EventEmitter<MatSliderDragEvent> =\n    new EventEmitter<MatSliderDragEvent>();\n\n  /**\n   * The current translateX in px of the slider visual thumb.\n   * @docs-private\n   */\n  get translateX(): number {\n    if (this._slider.min >= this._slider.max) {\n      this._translateX = this._tickMarkOffset;\n      return this._translateX;\n    }\n    if (this._translateX === undefined) {\n      this._translateX = this._calcTranslateXByValue();\n    }\n    return this._translateX;\n  }\n  set translateX(v: number) {\n    this._translateX = v;\n  }\n  private _translateX: number | undefined;\n\n  /**\n   * Indicates whether this thumb is the start or end thumb.\n   * @docs-private\n   */\n  thumbPosition: _MatThumb = _MatThumb.END;\n\n  /** @docs-private */\n  get min(): number {\n    return numberAttribute(this._hostElement.min, 0);\n  }\n  set min(v: number) {\n    this._hostElement.min = v + '';\n    this._cdr.detectChanges();\n  }\n\n  /** @docs-private */\n  get max(): number {\n    return numberAttribute(this._hostElement.max, 0);\n  }\n  set max(v: number) {\n    this._hostElement.max = v + '';\n    this._cdr.detectChanges();\n  }\n\n  get step(): number {\n    return numberAttribute(this._hostElement.step, 0);\n  }\n  set step(v: number) {\n    this._hostElement.step = v + '';\n    this._cdr.detectChanges();\n  }\n\n  /** @docs-private */\n  get disabled(): boolean {\n    return booleanAttribute(this._hostElement.disabled);\n  }\n  set disabled(v: boolean) {\n    this._hostElement.disabled = v;\n    this._cdr.detectChanges();\n\n    if (this._slider.disabled !== this.disabled) {\n      this._slider.disabled = this.disabled;\n    }\n  }\n\n  /** The percentage of the slider that coincides with the value. */\n  get percentage(): number {\n    if (this._slider.min >= this._slider.max) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n  }\n\n  /** @docs-private */\n  get fillPercentage(): number {\n    if (!this._slider._cachedWidth) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    if (this._translateX === 0) {\n      return 0;\n    }\n    return this.translateX / this._slider._cachedWidth;\n  }\n\n  /** The host native HTML input element. */\n  _hostElement: HTMLInputElement;\n\n  /** The aria-valuetext string representation of the input's value. */\n  _valuetext = signal('');\n\n  /** The radius of a native html slider's knob. */\n  _knobRadius: number = 8;\n\n  /** The distance in px from the start of the slider track to the first tick mark. */\n  _tickMarkOffset = 3;\n\n  /** Whether user's cursor is currently in a mouse down state on the input. */\n  _isActive: boolean = false;\n\n  /** Whether the input is currently focused (either by tab or after clicking). */\n  _isFocused: boolean = false;\n\n  /** Used to relay updates to _isFocused to the slider visual thumbs. */\n  private _setIsFocused(v: boolean): void {\n    this._isFocused = v;\n  }\n\n  /**\n   * Whether the initial value has been set.\n   * This exists because the initial value cannot be immediately set because the min and max\n   * must first be relayed from the parent MatSlider component, which can only happen later\n   * in the component lifecycle.\n   */\n  private _hasSetInitialValue: boolean = false;\n\n  /** The stored initial value. */\n  _initialValue: string | undefined;\n\n  /** Defined when a user is using a form control to manage slider value & validation. */\n  private _formControl: FormControl | undefined;\n\n  /** Emits when the component is destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /**\n   * Indicates whether UI updates should be skipped.\n   *\n   * This flag is used to avoid flickering\n   * when correcting values on pointer up/down.\n   */\n  _skipUIUpdate: boolean = false;\n\n  /** Callback called when the slider input value changes. */\n  protected _onChangeFn: ((value: any) => void) | undefined;\n\n  /** Callback called when the slider input has been touched. */\n  private _onTouchedFn: () => void = () => {};\n\n  /**\n   * Whether the NgModel has been initialized.\n   *\n   * This flag is used to ignore ghost null calls to\n   * writeValue which can break slider initialization.\n   *\n   * See https://github.com/angular/angular/issues/14988.\n   */\n  protected _isControlInitialized = false;\n\n  private _platform = inject(Platform);\n\n  constructor(\n    readonly _ngZone: NgZone,\n    readonly _elementRef: ElementRef<HTMLInputElement>,\n    readonly _cdr: ChangeDetectorRef,\n    @Inject(MAT_SLIDER) protected _slider: _MatSlider,\n  ) {\n    this._hostElement = _elementRef.nativeElement;\n    this._ngZone.runOutsideAngular(() => {\n      this._hostElement.addEventListener('pointerdown', this._onPointerDown.bind(this));\n      this._hostElement.addEventListener('pointermove', this._onPointerMove.bind(this));\n      this._hostElement.addEventListener('pointerup', this._onPointerUp.bind(this));\n    });\n  }\n\n  ngOnDestroy(): void {\n    this._hostElement.removeEventListener('pointerdown', this._onPointerDown);\n    this._hostElement.removeEventListener('pointermove', this._onPointerMove);\n    this._hostElement.removeEventListener('pointerup', this._onPointerUp);\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.dragStart.complete();\n    this.dragEnd.complete();\n  }\n\n  /** @docs-private */\n  initProps(): void {\n    this._updateWidthInactive();\n\n    // If this or the parent slider is disabled, just make everything disabled.\n    if (this.disabled !== this._slider.disabled) {\n      // The MatSlider setter for disabled will relay this and disable both inputs.\n      this._slider.disabled = true;\n    }\n\n    this.step = this._slider.step;\n    this.min = this._slider.min;\n    this.max = this._slider.max;\n    this._initValue();\n  }\n\n  /** @docs-private */\n  initUI(): void {\n    this._updateThumbUIByValue();\n  }\n\n  _initValue(): void {\n    this._hasSetInitialValue = true;\n    if (this._initialValue === undefined) {\n      this.value = this._getDefaultValue();\n    } else {\n      this._hostElement.value = this._initialValue;\n      this._updateThumbUIByValue();\n      this._slider._onValueChange(this);\n      this._cdr.detectChanges();\n    }\n  }\n\n  _getDefaultValue(): number {\n    return this.min;\n  }\n\n  _onBlur(): void {\n    this._setIsFocused(false);\n    this._onTouchedFn();\n  }\n\n  _onFocus(): void {\n    this._slider._setTransition(false);\n    this._slider._updateTrackUI(this);\n    this._setIsFocused(true);\n  }\n\n  _onChange(): void {\n    this.valueChange.emit(this.value);\n    // only used to handle the edge case where user\n    // mousedown on the slider then uses arrow keys.\n    if (this._isActive) {\n      this._updateThumbUIByValue({withAnimation: true});\n    }\n  }\n\n  _onInput(): void {\n    this._onChangeFn?.(this.value);\n    // handles arrowing and updating the value when\n    // a step is defined.\n    if (this._slider.step || !this._isActive) {\n      this._updateThumbUIByValue({withAnimation: true});\n    }\n    this._slider._onValueChange(this);\n  }\n\n  _onNgControlValueChange(): void {\n    // only used to handle when the value change\n    // originates outside of the slider.\n    if (!this._isActive || !this._isFocused) {\n      this._slider._onValueChange(this);\n      this._updateThumbUIByValue();\n    }\n    this._slider.disabled = this._formControl!.disabled;\n  }\n\n  _onPointerDown(event: PointerEvent): void {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n\n    // On IOS, dragging only works if the pointer down happens on the\n    // slider thumb and the slider does not receive focus from pointer events.\n    if (this._platform.IOS) {\n      const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(\n        event,\n        this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect(),\n      );\n\n      this._isActive = isCursorOnSliderThumb;\n      this._updateWidthActive();\n      this._slider._updateDimensions();\n      return;\n    }\n\n    this._isActive = true;\n    this._setIsFocused(true);\n    this._updateWidthActive();\n    this._slider._updateDimensions();\n\n    // Does nothing if a step is defined because we\n    // want the value to snap to the values on input.\n    if (!this._slider.step) {\n      this._updateThumbUIByPointerEvent(event, {withAnimation: true});\n    }\n\n    if (!this.disabled) {\n      this._handleValueCorrection(event);\n      this.dragStart.emit({source: this, parent: this._slider, value: this.value});\n    }\n  }\n\n  /**\n   * Corrects the value of the slider on pointer up/down.\n   *\n   * Called on pointer down and up because the value is set based\n   * on the inactive width instead of the active width.\n   */\n  private _handleValueCorrection(event: PointerEvent): void {\n    // Don't update the UI with the current value! The value on pointerdown\n    // and pointerup is calculated in the split second before the input(s)\n    // resize. See _updateWidthInactive() and _updateWidthActive() for more\n    // details.\n    this._skipUIUpdate = true;\n\n    // Note that this function gets triggered before the actual value of the\n    // slider is updated. This means if we were to set the value here, it\n    // would immediately be overwritten. Using setTimeout ensures the setting\n    // of the value happens after the value has been updated by the\n    // pointerdown event.\n    setTimeout(() => {\n      this._skipUIUpdate = false;\n      this._fixValue(event);\n    }, 0);\n  }\n\n  /** Corrects the value of the slider based on the pointer event's position. */\n  _fixValue(event: PointerEvent): void {\n    const xPos = event.clientX - this._slider._cachedLeft;\n    const width = this._slider._cachedWidth;\n    const step = this._slider.step === 0 ? 1 : this._slider.step;\n    const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n    const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n\n    // To ensure the percentage is rounded to the necessary number of decimals.\n    const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n\n    const impreciseValue =\n      fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n    const value = Math.round(impreciseValue / step) * step;\n    const prevValue = this.value;\n\n    if (value === prevValue) {\n      // Because we prevented UI updates, if it turns out that the race\n      // condition didn't happen and the value is already correct, we\n      // have to apply the ui updates now.\n      this._slider._onValueChange(this);\n      this._slider.step > 0\n        ? this._updateThumbUIByValue()\n        : this._updateThumbUIByPointerEvent(event, {withAnimation: this._slider._hasAnimation});\n      return;\n    }\n\n    this.value = value;\n    this.valueChange.emit(this.value);\n    this._onChangeFn?.(this.value);\n    this._slider._onValueChange(this);\n    this._slider.step > 0\n      ? this._updateThumbUIByValue()\n      : this._updateThumbUIByPointerEvent(event, {withAnimation: this._slider._hasAnimation});\n  }\n\n  _onPointerMove(event: PointerEvent): void {\n    // Again, does nothing if a step is defined because\n    // we want the value to snap to the values on input.\n    if (!this._slider.step && this._isActive) {\n      this._updateThumbUIByPointerEvent(event);\n    }\n  }\n\n  _onPointerUp(): void {\n    if (this._isActive) {\n      this._isActive = false;\n      if (this._platform.SAFARI) {\n        this._setIsFocused(false);\n      }\n      this.dragEnd.emit({source: this, parent: this._slider, value: this.value});\n\n      // This setTimeout is to prevent the pointerup from triggering a value\n      // change on the input based on the inactive width. It's not clear why\n      // but for some reason on IOS this race condition is even more common so\n      // the timeout needs to be increased.\n      setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n    }\n  }\n\n  _clamp(v: number): number {\n    const min = this._tickMarkOffset;\n    const max = this._slider._cachedWidth - this._tickMarkOffset;\n    return Math.max(Math.min(v, max), min);\n  }\n\n  _calcTranslateXByValue(): number {\n    if (this._slider._isRtl) {\n      return (\n        (1 - this.percentage) * (this._slider._cachedWidth - this._tickMarkOffset * 2) +\n        this._tickMarkOffset\n      );\n    }\n    return (\n      this.percentage * (this._slider._cachedWidth - this._tickMarkOffset * 2) +\n      this._tickMarkOffset\n    );\n  }\n\n  _calcTranslateXByPointerEvent(event: PointerEvent): number {\n    return event.clientX - this._slider._cachedLeft;\n  }\n\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive(): void {}\n\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive(): void {\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    this._hostElement.style.width = `calc(100% + ${\n      this._slider._inputPadding - this._tickMarkOffset * 2\n    }px)`;\n    this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n  }\n\n  _updateThumbUIByValue(options?: {withAnimation: boolean}): void {\n    this.translateX = this._clamp(this._calcTranslateXByValue());\n    this._updateThumbUI(options);\n  }\n\n  _updateThumbUIByPointerEvent(event: PointerEvent, options?: {withAnimation: boolean}): void {\n    this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n    this._updateThumbUI(options);\n  }\n\n  _updateThumbUI(options?: {withAnimation: boolean}) {\n    this._slider._setTransition(!!options?.withAnimation);\n    this._slider._onTranslateXChange(this);\n  }\n\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value: any): void {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n    }\n  }\n\n  /**\n   * Registers a callback to be invoked when the input's value changes from user input.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnChange(fn: any): void {\n    this._onChangeFn = fn;\n    this._isControlInitialized = true;\n  }\n\n  /**\n   * Registers a callback to be invoked when the input is blurred by the user.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnTouched(fn: any): void {\n    this._onTouchedFn = fn;\n  }\n\n  /**\n   * Sets the disabled state of the slider.\n   * @param isDisabled The new disabled state\n   * @docs-private\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  }\n\n  focus(): void {\n    this._hostElement.focus();\n  }\n\n  blur(): void {\n    this._hostElement.blur();\n  }\n}\n\n@Directive({\n  selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n  exportAs: 'matSliderRangeThumb',\n  providers: [\n    MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR,\n    {provide: MAT_SLIDER_RANGE_THUMB, useExisting: MatSliderRangeThumb},\n  ],\n  standalone: true,\n})\nexport class MatSliderRangeThumb extends MatSliderThumb implements _MatSliderRangeThumb {\n  /** @docs-private */\n  getSibling(): _MatSliderRangeThumb | undefined {\n    if (!this._sibling) {\n      this._sibling = this._slider._getInput(this._isEndThumb ? _MatThumb.START : _MatThumb.END) as\n        | MatSliderRangeThumb\n        | undefined;\n    }\n    return this._sibling;\n  }\n  private _sibling: MatSliderRangeThumb | undefined;\n\n  /**\n   * Returns the minimum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMinPos(): number {\n    const sibling = this.getSibling();\n    if (!this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._tickMarkOffset;\n  }\n\n  /**\n   * Returns the maximum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMaxPos(): number {\n    const sibling = this.getSibling();\n    if (this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._slider._cachedWidth - this._tickMarkOffset;\n  }\n\n  _setIsLeftThumb(): void {\n    this._isLeftThumb =\n      (this._isEndThumb && this._slider._isRtl) || (!this._isEndThumb && !this._slider._isRtl);\n  }\n\n  /** Whether this slider corresponds to the input on the left hand side. */\n  _isLeftThumb: boolean;\n\n  /** Whether this slider corresponds to the input with greater value. */\n  _isEndThumb: boolean;\n\n  constructor(\n    _ngZone: NgZone,\n    @Inject(MAT_SLIDER) _slider: _MatSlider,\n    _elementRef: ElementRef<HTMLInputElement>,\n    override readonly _cdr: ChangeDetectorRef,\n  ) {\n    super(_ngZone, _elementRef, _cdr, _slider);\n    this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n    this._setIsLeftThumb();\n    this.thumbPosition = this._isEndThumb ? _MatThumb.END : _MatThumb.START;\n  }\n\n  override _getDefaultValue(): number {\n    return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n  }\n\n  override _onInput(): void {\n    super._onInput();\n    this._updateSibling();\n    if (!this._isActive) {\n      this._updateWidthInactive();\n    }\n  }\n\n  override _onNgControlValueChange(): void {\n    super._onNgControlValueChange();\n    this.getSibling()?._updateMinMax();\n  }\n\n  override _onPointerDown(event: PointerEvent): void {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    if (this._sibling) {\n      this._sibling._updateWidthActive();\n      this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n    }\n    super._onPointerDown(event);\n  }\n\n  override _onPointerUp(): void {\n    super._onPointerUp();\n    if (this._sibling) {\n      setTimeout(() => {\n        this._sibling!._updateWidthInactive();\n        this._sibling!._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n      });\n    }\n  }\n\n  override _onPointerMove(event: PointerEvent): void {\n    super._onPointerMove(event);\n    if (!this._slider.step && this._isActive) {\n      this._updateSibling();\n    }\n  }\n\n  override _fixValue(event: PointerEvent): void {\n    super._fixValue(event);\n    this._sibling?._updateMinMax();\n  }\n\n  override _clamp(v: number): number {\n    return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n  }\n\n  _updateMinMax(): void {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    if (this._isEndThumb) {\n      this.min = Math.max(this._slider.min, sibling.value);\n      this.max = this._slider.max;\n    } else {\n      this.min = this._slider.min;\n      this.max = Math.min(this._slider.max, sibling.value);\n    }\n  }\n\n  override _updateWidthActive(): void {\n    const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n    const maxWidth =\n      this._slider._cachedWidth + this._slider._inputPadding - minWidth - this._tickMarkOffset * 2;\n    const percentage =\n      this._slider.min < this._slider.max\n        ? (this.max - this.min) / (this._slider.max - this._slider.min)\n        : 1;\n    const width = maxWidth * percentage + minWidth;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n  }\n\n  override _updateWidthInactive(): void {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    const maxWidth = this._slider._cachedWidth - this._tickMarkOffset * 2;\n    const midValue = this._isEndThumb\n      ? this.value - (this.value - sibling.value) / 2\n      : this.value + (sibling.value - this.value) / 2;\n\n    const _percentage = this._isEndThumb\n      ? (this.max - midValue) / (this._slider.max - this._slider.min)\n      : (midValue - this.min) / (this._slider.max - this._slider.min);\n\n    const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n\n    // Extend the native input width by the radius of the ripple\n    let ripplePadding = this._slider._rippleRadius;\n\n    // If one of the inputs is maximally sized (the value of both thumbs is\n    // equal to the min or max), make that input take up all of the width and\n    // make the other unselectable.\n    if (percentage === 1) {\n      ripplePadding = 48;\n    } else if (percentage === 0) {\n      ripplePadding = 0;\n    }\n\n    const width = maxWidth * percentage + ripplePadding;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = '0px';\n\n    if (this._isLeftThumb) {\n      this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n      this._hostElement.style.right = 'auto';\n    } else {\n      this._hostElement.style.left = 'auto';\n      this._hostElement.style.right = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n    }\n  }\n\n  _updateStaticStyles(): void {\n    this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n  }\n\n  private _updateSibling(): void {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    sibling._updateMinMax();\n    if (this._isActive) {\n      sibling._updateWidthActive();\n    } else {\n      sibling._updateWidthInactive();\n    }\n  }\n\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  override writeValue(value: any): void {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n      this._updateWidthInactive();\n      this._updateSibling();\n    }\n  }\n\n  override _setValue(value: string) {\n    super._setValue(value);\n    this._updateWidthInactive();\n    this._updateSibling();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '@angular/material/core';\nimport {MatSlider} from './slider';\nimport {MatSliderVisualThumb} from './slider-thumb';\nimport {MatSliderThumb, MatSliderRangeThumb} from './slider-input';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatRippleModule,\n    MatSlider,\n    MatSliderThumb,\n    MatSliderRangeThumb,\n    MatSliderVisualThumb,\n  ],\n  exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb],\n})\nexport class MatSliderModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;AAWA;;;AAGG;AACH,IAAY,SAGX,CAAA;AAHD,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS,CAAA;AACT,IAAA,SAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO,CAAA;AACT,CAAC,EAHW,SAAS,KAAT,SAAS,GAGpB,EAAA,CAAA,CAAA,CAAA;AAED;AACA,IAAY,YAGX,CAAA;AAHD,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACV,IAAA,YAAA,CAAA,YAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY,CAAA;AACd,CAAC,EAHW,YAAY,KAAZ,YAAY,GAGvB,EAAA,CAAA,CAAA,CAAA;AAED;;;;;AAKG;AACI,MAAM,UAAU,GAAG,IAAI,cAAc,CAAK,YAAY,CAAC,CAAC;AAE/D;;;;AAIG;AACI,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAAK,iBAAiB,CAAC,CAAC;AAE1E;;;;AAIG;AACI,MAAM,sBAAsB,GAAG,IAAI,cAAc,CAAK,sBAAsB,CAAC,CAAC;AAErF;;;;AAIG;AACI,MAAM,uBAAuB,GAAG,IAAI,cAAc,CAAK,uBAAuB,CAAC,CAAC;AAcvF;;;;AAIG;MACU,eAAe,CAAA;AAS3B;;AChDD;;;;;;AAMG;MAcU,oBAAoB,CAAA;AAiD/B,IAAA,WAAA,CACW,IAAuB,EACf,OAAe,EAChC,WAAoC,EACR,OAAmB,EAAA;QAHtC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAmB;QACf,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QAEJ,IAAO,CAAA,OAAA,GAAP,OAAO,CAAY;;QAjBzC,IAAU,CAAA,UAAA,GAAY,KAAK,CAAC;;QAGpC,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;;QAGlB,IAAwB,CAAA,wBAAA,GAAY,KAAK,CAAC;AAKlC,QAAA,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AA0C7B,QAAA,IAAA,CAAA,cAAc,GAAG,CAAC,KAAmB,KAAU;AACrD,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;gBAChC,OAAO;aACR;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAAC;AACvD,YAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACnE,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;YAE5B,IAAI,SAAS,EAAE;gBACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;iBAAM;AACL,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACxC;AACH,SAAC,CAAC;QAEM,IAAa,CAAA,aAAA,GAAG,MAAW;AACjC,YAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACxB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACzC,SAAC,CAAC;QAEM,IAAQ,CAAA,QAAA,GAAG,MAAW;;;AAG5B,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACvC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;AAChE,SAAC,CAAC;QAEM,IAAO,CAAA,OAAA,GAAG,MAAW;;AAE3B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACxC;;AAED,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;YACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;AACnE,SAAC,CAAC;AAEM,QAAA,IAAA,CAAA,YAAY,GAAG,CAAC,KAAmB,KAAU;AACnD,YAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,OAAO;aACR;AACD,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC3B,SAAC,CAAC;QAEM,IAAU,CAAA,UAAA,GAAG,MAAW;AAC9B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;;AAExC,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;AACjC,gBAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACxC;;;AAID,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;gBACzB,IAAI,CAAC,gBAAgB,EAAE,CAAC;aACzB;AACH,SAAC,CAAC;AAhGA,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC;KAC/C;IAED,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAE,CAAC;QAChE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;AACrD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;;;AAIlC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,KAAK,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3D,KAAK,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACzD,KAAK,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACrD,KAAK,CAAC,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3D,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/C,KAAK,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/C,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;AACT,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC;QAElC,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;YAC9D,KAAK,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5D,KAAK,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;YACxD,KAAK,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAC9D,KAAK,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClD,KAAK,CAAC,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SACjD;KACF;;IAmEO,gBAAgB,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAChD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,CAAC,CAAC;YAC7E,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAC5E;KACF;;IAGO,gBAAgB,GAAA;;QAEtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAChD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,EAAE,IAAI,CAAC,CAAC;YACnF,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAC5E;KACF;;IAGO,iBAAiB,GAAA;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,EAAC,aAAa,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAC,CAAC,CAAC;YAClF,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;SAC9E;KACF;;AAGO,IAAA,gBAAgB,CAAC,SAAqB,EAAA;AAC5C,QAAA,OAAO,SAAS,EAAE,KAAK,KAAK,WAAW,CAAC,SAAS,IAAI,SAAS,EAAE,KAAK,KAAK,WAAW,CAAC,OAAO,CAAC;KAC/F;;IAGO,WAAW,CACjB,SAAgC,EAChC,wBAAkC,EAAA;AAElC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB,OAAO;SACR;QACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;AAC3B,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACzB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CACpC,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CACzE,CAAC;YACF,OAAO,CAAC,mBAAmB,EAAE,CAAC;SAC/B;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,QAAQ,IAAI,CAAC,wBAAwB,EAAE;YAC5E,OAAO;SACR;AACD,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,GAAG,SAAS;AACzF,YAAA,QAAQ,EAAE,IAAI;AACd,YAAA,UAAU,EAAE,IAAI;AACjB,SAAA,CAAC,CAAC;KACJ;AAED;;;AAGG;AACK,IAAA,WAAW,CAAC,SAAqB,EAAA;QACvC,SAAS,EAAE,OAAO,EAAE,CAAC;AAErB,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC9B,OAAO;SACR;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE;YAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO,CAAC,mBAAmB,EAAE,CAAC;SAC/B;KACF;;IAGD,mBAAmB,GAAA;QACjB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;KACtE;;IAGD,mBAAmB,GAAA;QACjB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,mCAAmC,CAAC,CAAC;KACzE;IAED,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAC3B,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CACzE,CAAC;KACH;;IAGD,2BAA2B,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,aAAa,CAAC;KACrD;;IAGD,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;KACjC;IAED,mBAAmB,GAAA;QACjB,QACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC;AAC3C,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAC5C;KACH;AAtQU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,mGAqDrB,UAAU,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AArDT,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,6PAJpB,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAE,oBAAoB,EAAC,CAAC,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAevE,SAAS,EChEtB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,yBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,gZASA,mWD0CY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAER,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAbhC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,yBAAyB,EAG7B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,+CAA+C;qBACzD,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,EAC1B,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAA,oBAAsB,EAAC,CAAC,cACtE,IAAI,EAAA,OAAA,EACP,CAAC,SAAS,CAAC,EAAA,QAAA,EAAA,gZAAA,EAAA,MAAA,EAAA,CAAA,2SAAA,CAAA,EAAA,CAAA;;0BAuDjB,MAAM;2BAAC,UAAU,CAAA;yCAnDX,QAAQ,EAAA,CAAA;sBAAhB,KAAK;gBAGG,aAAa,EAAA,CAAA;sBAArB,KAAK;gBAGG,kBAAkB,EAAA,CAAA;sBAA1B,KAAK;gBAGyB,OAAO,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS,CAAA;gBAGD,KAAK,EAAA,CAAA;sBAAvB,SAAS;uBAAC,MAAM,CAAA;gBAIjB,wBAAwB,EAAA,CAAA;sBADvB,SAAS;uBAAC,yBAAyB,CAAA;;;AEtBtC;AACA;AACA;AACA;AAEA;;;AAGG;MAqBU,SAAS,CAAA;;AAepB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,CAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEnD,IAAI,QAAQ,EAAE;AACZ,YAAA,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;SACpC;QACD,IAAI,UAAU,EAAE;AACd,YAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;SACtC;KACF;;AAID,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IACD,IAAI,QAAQ,CAAC,CAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,wBAAwB,EAAE,CAAC;KACjC;;AAQD,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;IACD,IAAI,GAAG,CAAC,CAAS,EAAA;AACf,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SACtB;KACF;AAWO,IAAA,UAAU,CAAC,GAAW,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAChB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC9F,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;AAEO,IAAA,eAAe,CAAC,GAA+B,EAAA;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB,CAAC;AAE3E,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AAEvC,QAAA,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACzB,QAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AACnD,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAExD,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAClC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;AAEhC,QAAA,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;cACb,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU,CAAC;cAC1D,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAE/D,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAC/B;AAED,QAAA,IAAI,aAAa,KAAK,UAAU,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SACjC;KACF;AAEO,IAAA,kBAAkB,CAAC,GAAW,EAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC;AAE7B,YAAA,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;YAChB,KAAK,CAAC,qBAAqB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAE3B,YAAA,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aAC5B;SACF;KACF;;AAGD,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;IACD,IAAI,GAAG,CAAC,CAAS,EAAA;AACf,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SACtB;KACF;AAGO,IAAA,UAAU,CAAC,GAAW,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AAChB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC9F,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;AAEO,IAAA,eAAe,CAAC,GAA+B,EAAA;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB,CAAC;AAE3E,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AAEvC,QAAA,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;AACvB,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACnD,QAAA,QAAQ,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC;QAEhC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QAChC,UAAU,CAAC,oBAAoB,EAAE,CAAC;AAElC,QAAA,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;cACb,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,QAAQ,CAAC;cAC1D,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAE/D,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAC/B;AAED,QAAA,IAAI,aAAa,KAAK,UAAU,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SACjC;KACF;AAEO,IAAA,kBAAkB,CAAC,GAAW,EAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC;AAE7B,YAAA,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;YAChB,KAAK,CAAC,qBAAqB,EAAE,CAAC;AAC9B,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAE3B,YAAA,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aAC5B;SACF;KACF;;AAGD,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IACD,IAAI,IAAI,CAAC,CAAS,EAAA;AAChB,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACvC,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACxB;KACF;AAGO,IAAA,WAAW,CAAC,IAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAClB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACrE,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;IAEO,gBAAgB,GAAA;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB,CAAC;AAE3E,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC;AAEvC,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;AAExC,QAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AAE3B,QAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAC3B,QAAA,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAE7B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,YAAA,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AAChC,YAAA,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;SACrC;AAED,QAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;AACrD,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAErD,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAClC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QAEhC,QAAQ,CAAC,KAAK,GAAG,cAAc;cAC3B,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,QAAQ,CAAC;cAC1D,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAE/D,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;SAC/B;AAED,QAAA,IAAI,aAAa,KAAK,UAAU,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SACjC;KACF;IAEO,mBAAmB,GAAA;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC;AAE7B,YAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AACxB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,gBAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;aAC3B;YAED,KAAK,CAAC,qBAAqB,EAAE,CAAC;AAE9B,YAAA,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aAC5B;SACF;KACF;IA4DD,WACW,CAAA,OAAe,EACf,IAAuB,EACvB,WAAoC,EACxB,IAAoB,EAGhC,oBAA0C,EACR,aAAsB,EAAA;QAPxD,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAmB;QACvB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAyB;QACxB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAgB;QAGhC,IAAoB,CAAA,oBAAA,GAApB,oBAAoB,CAAsB;QAlS7C,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;QAW3B,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAInC,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;QAavB,IAAI,CAAA,IAAA,GAAW,CAAC,CAAC;;QAQzB,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;QA8DvB,IAAI,CAAA,IAAA,GAAW,GAAG,CAAC;QA8DnB,IAAK,CAAA,KAAA,GAAW,CAAC,CAAC;AAiE1B;;;;AAIG;QACM,IAAW,CAAA,WAAA,GAA8B,CAAC,KAAa,KAAK,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC;QAmBhF,IAAa,CAAA,aAAA,GAAW,EAAE,CAAC;;;QAKjB,IAAuB,CAAA,uBAAA,GAAW,EAAE,CAAC;;QAGrC,IAAqB,CAAA,qBAAA,GAAW,EAAE,CAAC;QAO7C,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;;QAG1B,IAAM,CAAA,MAAA,GAAY,KAAK,CAAC;QAEhB,IAAmB,CAAA,mBAAA,GAAY,KAAK,CAAC;AAE7C;;;AAGG;QACH,IAAmB,CAAA,mBAAA,GAAW,CAAC,CAAC;QAEhC,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;QAEvB,IAAY,CAAA,YAAA,GAAyC,IAAI,CAAC;AAE1D,QAAA,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;;QAkBrC,IAAW,CAAA,WAAA,GAAW,CAAC,CAAC;;QA4PhB,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;AAlQtC,QAAA,IAAI,CAAC,eAAe,GAAG,aAAa,KAAK,gBAAgB,CAAC;AAC1D,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACpF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;KACzC;IAOD,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC1B;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;AAE1B,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,eAAe,CACb,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAE,EAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAChC,CAAC;SACH;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC;AAE3D,QAAA,IAAI,CAAC,QAAQ;cACT,IAAI,CAAC,YAAY,CAAC,MAA8B,EAAE,MAA8B,CAAC;AACnF,cAAE,IAAI,CAAC,eAAe,CAAC,MAAO,CAAC,CAAC;AAElC,QAAA,IAAI,CAAC,cAAc,CAAC,MAAO,CAAC,CAAC;QAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3B;AAEO,IAAA,eAAe,CAAC,MAAuB,EAAA;QAC7C,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;AAErC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,MAAM,CAAC,qBAAqB,EAAE,CAAC;KAChC;IAEO,YAAY,CAAC,MAA4B,EAAE,MAA4B,EAAA;QAC7E,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,CAAC,MAAM,EAAE,CAAC;QAEhB,MAAM,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,CAAC,MAAM,EAAE,CAAC;QAEhB,MAAM,CAAC,aAAa,EAAE,CAAC;QACvB,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvB,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC7B,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAE7B,IAAI,CAAC,wBAAwB,EAAE,CAAC;AAEhC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAEhC,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAC/B,MAAM,CAAC,qBAAqB,EAAE,CAAC;KAChC;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC1C,QAAA,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;KAC7B;;IAGO,YAAY,GAAA;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACvE,IAAI,CAAC,iBAAiB,EAAE,CAAC;KAC1B;IAEO,iBAAiB,GAAA;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB,CAAC;QAE3E,QAAQ,CAAC,eAAe,EAAE,CAAC;QAC3B,UAAU,CAAC,eAAe,EAAE,CAAC;AAE7B,QAAA,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,sBAAsB,EAAE,CAAC;AACxD,QAAA,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,sBAAsB,EAAE,CAAC;QAE5D,QAAQ,CAAC,mBAAmB,EAAE,CAAC;QAC/B,UAAU,CAAC,mBAAmB,EAAE,CAAC;QAEjC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;QAChC,UAAU,CAAC,oBAAoB,EAAE,CAAC;QAElC,QAAQ,CAAC,qBAAqB,EAAE,CAAC;QACjC,UAAU,CAAC,qBAAqB,EAAE,CAAC;KACpC;IAEO,oBAAoB,GAAA;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAE,CAAC;QAC7C,KAAK,CAAC,qBAAqB,EAAE,CAAC;KAC/B;;IAGO,kBAAkB,GAAA;QACxB,IAAI,OAAO,cAAc,KAAK,WAAW,IAAI,CAAC,cAAc,EAAE;YAC5D,OAAO;SACR;AAED,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,MAAK;AAC7C,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;oBACpB,OAAO;iBACR;AACD,gBAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,oBAAA,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBACjC;gBACD,IAAI,CAAC,SAAS,EAAE,CAAC;AACnB,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AAC/D,SAAC,CAAC,CAAC;KACJ;;IAGO,SAAS,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;KAC7F;AAEO,IAAA,SAAS,CAAC,aAAA,GAA2B,SAAS,CAAC,GAAG,EAAA;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5C,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,IAAI,CAAC,GAAG,CAAC;SACjB;QACD,OAAO,KAAK,CAAC,KAAK,CAAC;KACpB;IAEO,WAAW,GAAA;QACjB,OAAO,CAAC,EACN,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,aAAa,CAC/F,CAAC;KACH;;IAGD,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC;AAC/D,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,IAAI,CAAC;KAChF;;AAGD,IAAA,qBAAqB,CAAC,MAKrB,EAAA;QACC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,CAAC;AAEzD,QAAA,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC9B,QAAA,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAChC,QAAA,UAAU,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;AACpD,QAAA,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;KACzC;;AAGD,IAAA,sBAAsB,CAAC,KAAa,EAAA;;AAElC,QAAA,MAAM,UAAU,GAAG,KAAK,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACrF,OAAO,CAAA,WAAA,EAAc,UAAU,CAAA,EAAA,CAAI,CAAC;KACrC;;AAID,IAAA,mBAAmB,CAAC,MAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;AAED,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC5B,QAAA,IAAI,CAAC,yBAAyB,CAAC,MAA8B,CAAC,CAAC;KAChE;IAED,+BAA+B,CAC7B,MAA4B,EAC5B,MAA4B,EAAA;AAE5B,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;QAED,MAAM,CAAC,qBAAqB,EAAE,CAAC;QAC/B,MAAM,CAAC,qBAAqB,EAAE,CAAC;KAChC;AAED,IAAA,cAAc,CAAC,MAAuB,EAAA;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;AAED,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3B;IAED,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;KAC1B;IAED,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC;YACrE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB,CAAC;YAEvE,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAC/B,MAAM,CAAC,qBAAqB,EAAE,CAAC;YAE/B,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAC7B,MAAM,CAAC,mBAAmB,EAAE,CAAC;YAE7B,MAAM,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,CAAC,aAAa,EAAE,CAAC;YAEvB,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAC9B,MAAM,CAAC,oBAAoB,EAAE,CAAC;SAC/B;aAAM;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAC7C,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,qBAAqB,EAAE,CAAC;aAChC;SACF;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3B;;IAMO,qBAAqB,GAAA;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,EAAE;AAC5B,YAAA,OAAO,KAAK,CAAC;SACd;QACD,OAAO,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC;KACzD;AAED;;;AAGG;AACK,IAAA,iCAAiC,CAAC,MAA4B,EAAA;AACpE,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAG,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACzD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3D,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;AACrE,QAAA,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;KAC1F;;AAGO,IAAA,yBAAyB,CAAC,MAA4B,EAAA;QAC5D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACxC,OAAO;SACR;QACD,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,qBAAqB,EAAE,EAAE;AACxD,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC;AAC3C,YAAA,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC;SAChD;KACF;;;;;;;;AAUD,IAAA,cAAc,CAAC,MAAuB,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,OAAO;SACR;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAC1B,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CACxE,CAAC;AACH,QAAA,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,WAAA,EAAc,MAAM,CAAC,UAAU,CAAA,GAAA,CAAK,CAAC;KAC3E;;;;;;;;AAUD,IAAA,uBAAuB,CAAC,MAAuB,EAAA;AAC7C,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,OAAO;SACR;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAEjD,QAAA,IAAI,CAAC,mBAAmB;cACpB,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC;cAChC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAElE,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK;AACtC,mBAAG,IAAI,CAAC,uBAAuB,GAAG,SAAS;mBACxC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC,CAAC;YAE7C,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YACzD,SAAS,CAAC,MAAM,GAAG,CAAC;kBAChB,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,gCAAgC,CAAC;kBACxE,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,gCAAgC,CAAC,CAAC;SACjF;KACF;;IAGO,wBAAwB,GAAA;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAE/C,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;SACtC;QACD,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;SACtC;KACF;;;;;;;;;;;IAaO,sBAAsB,GAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC7C,OAAO;SACR;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAC3D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACpD,QAAA,MAAM,UAAU,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACjE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,GAAG,UAAU,GAAG,CAAC,CAAC;KAC/D;;;;;;;;;;;;;;;AAiBD,IAAA,cAAc,CAAC,MAAuB,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,OAAO;SACR;AAED,QAAA,IAAI,CAAC,QAAQ;AACX,cAAE,IAAI,CAAC,mBAAmB,CAAC,MAA8B,CAAC;AAC1D,cAAE,IAAI,CAAC,sBAAsB,CAAC,MAAyB,CAAC,CAAC;KAC5D;AAEO,IAAA,mBAAmB,CAAC,MAA4B,EAAA;AACtD,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;QACpC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAClC,OAAO;SACR;AAED,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;QAE9F,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAI,EAAA,CAAA;AACpD,gBAAA,eAAe,EAAE,OAAO;gBACxB,SAAS,EAAE,CAAU,OAAA,EAAA,gBAAgB,CAAG,CAAA,CAAA;AACzC,aAAA,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,CAAA,EAAG,OAAO,CAAC,UAAU,CAAI,EAAA,CAAA;AAC/B,gBAAA,KAAK,EAAE,MAAM;AACb,gBAAA,eAAe,EAAE,MAAM;gBACvB,SAAS,EAAE,CAAU,OAAA,EAAA,gBAAgB,CAAG,CAAA,CAAA;AACzC,aAAA,CAAC,CAAC;SACJ;KACF;AAEO,IAAA,sBAAsB,CAAC,MAAuB,EAAA;AACpD,QAAA,IAAI,CAAC,MAAM;AACT,cAAE,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,KAAK,EAAE,KAAK;AACZ,gBAAA,eAAe,EAAE,OAAO;AACxB,gBAAA,SAAS,EAAE,CAAU,OAAA,EAAA,CAAC,GAAG,MAAM,CAAC,cAAc,CAAG,CAAA,CAAA;aAClD,CAAC;AACJ,cAAE,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,KAAK;AACX,gBAAA,KAAK,EAAE,MAAM;AACb,gBAAA,eAAe,EAAE,MAAM;AACvB,gBAAA,SAAS,EAAE,CAAA,OAAA,EAAU,MAAM,CAAC,cAAc,CAAG,CAAA,CAAA;AAC9C,aAAA,CAAC,CAAC;KACR;;;;;;;;;IAWD,iBAAiB,GAAA;QACf,IACE,CAAC,IAAI,CAAC,aAAa;YACnB,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,IAAI,CAAC,GAAG,KAAK,SAAS;AACtB,YAAA,IAAI,CAAC,GAAG,KAAK,SAAS,EACtB;YACA,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC;AAEzF,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;SAC3B;KACF;AAEO,IAAA,yBAAyB,CAAC,IAAY,EAAA;AAC5C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC/B,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACnE,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACrE,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,WAAW,EAAE,CAAC;AAE1C,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC;AAC/B,aAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AACzB,aAAA,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC3D;AAEO,IAAA,sBAAsB,CAAC,IAAY,EAAA;AACzC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAEnD,MAAM,2BAA2B,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5F,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9E,MAAM,wBAAwB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACvF,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,2BAA2B,CAAC;AACjD,aAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;aAC3B,MAAM,CACL,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAC1C,KAAK,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC5D,CAAC;KACL;;AAGD,IAAA,SAAS,CAAC,aAAwB,EAAA;QAChC,IAAI,aAAa,KAAK,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;YAClD,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;AACD,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;YACxB,OAAO,aAAa,KAAK,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;SACnF;QACD,OAAO;KACR;;AAGD,IAAA,SAAS,CAAC,aAAwB,EAAA;QAChC,OAAO,aAAa,KAAK,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,IAAK,GAAG,IAAI,CAAC,OAAO,EAAE,KAAM,CAAC;KACrF;AAED,IAAA,cAAc,CAAC,aAAsB,EAAA;AACnC,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC;AACnF,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAC7C,+BAA+B,EAC/B,IAAI,CAAC,aAAa,CACnB,CAAC;KACH;;IAGD,sBAAsB,CAAC,KAAmB,EAAE,IAAa,EAAA;AACvD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAC9B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AAChC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;AAChC,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AACnC,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KAChE;8GAn1BU,SAAS,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,cAAA,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAgUV,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAEb,qBAAqB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAlUhC,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAeD,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAmBhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,qDAWhB,gBAAgB,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAIhB,eAAe,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAiBf,gBAAgB,CAqDhB,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAAA,eAAe,CA8Df,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,iYAzLvB,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAY5C,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAGb,sBAAsB,EANzB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,uBAAuB,EClFvC,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,2iCAoCA,o6aDuCY,oBAAoB,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,eAAA,EAAA,oBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAEnB,SAAS,EAAA,UAAA,EAAA,CAAA;kBApBrB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EAGhB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,2BAA2B;AACpC,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,gCAAgC,EAAE,eAAe;AACjD,wBAAA,iCAAiC,EAAE,iBAAiB;qBACrD,EACS,QAAA,EAAA,WAAW,EACJ,eAAA,EAAA,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAAA,SAAA,EAC1B,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAW,SAAA,EAAC,CAAC,EAAA,UAAA,EAC9C,IAAI,EAAA,OAAA,EACP,CAAC,oBAAoB,CAAC,EAAA,QAAA,EAAA,2iCAAA,EAAA,MAAA,EAAA,CAAA,42aAAA,CAAA,EAAA,CAAA;;0BAgU5B,QAAQ;;0BACR,QAAQ;;0BACR,MAAM;2BAAC,yBAAyB,CAAA;;0BAEhC,QAAQ;;0BAAI,MAAM;2BAAC,qBAAqB,CAAA;yCAhUjB,YAAY,EAAA,CAAA;sBAArC,SAAS;uBAAC,aAAa,CAAA;gBAGe,OAAO,EAAA,CAAA;sBAA7C,YAAY;uBAAC,uBAAuB,CAAA;gBAGL,MAAM,EAAA,CAAA;sBAArC,YAAY;uBAAC,gBAAgB,CAAA;gBAI9B,OAAO,EAAA,CAAA;sBADN,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,sBAAsB,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC,CAAA;gBAKzD,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAoBhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAYpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKhC,GAAG,EAAA,CAAA;sBADN,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAcnC,KAAK,EAAA,CAAA;sBADJ,KAAK;gBAKN,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAsDhC,GAAG,EAAA,CAAA;sBADN,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBA+D/B,IAAI,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBAgF1B,WAAW,EAAA,CAAA;sBAAnB,KAAK;;AAilBR;AACA,SAAS,eAAe,CACtB,OAAgB,EAChB,eAAuD,EACvD,iBAAmC,EAAA;AAEnC,IAAA,MAAM,UAAU,GACd,CAAC,OAAO,IAAI,iBAAiB,EAAE,YAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,CAAC;AAClF,IAAA,MAAM,QAAQ,GAAG,eAAe,CAAC,YAAY,CAAC,YAAY,CACxD,OAAO,GAAG,mBAAmB,GAAG,gBAAgB,CACjD,CAAC;AAEF,IAAA,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,EAAE;AAC5B,QAAA,oCAAoC,EAAE,CAAC;KACxC;AACH,CAAC;AAED,SAAS,oCAAoC,GAAA;AAC3C,IAAA,MAAM,KAAK,CAAC,CAAA;;;;;;;;;;;;;;AAcV,GAAA,CAAA,CAAC,CAAC;AACN;;AE95BA;;;AAGG;AACI,MAAM,+BAA+B,GAAQ;AAClD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;AAC7C,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;AAGG;AACI,MAAM,qCAAqC,GAAQ;AACxD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC;AAClD,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;;;;;;AAOG;MAqBU,cAAc,CAAA;AACzB,IAAA,IACI,KAAK,GAAA;QACP,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KACpD;IACD,IAAI,KAAK,CAAC,KAAa,EAAA;AACrB,QAAA,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACjC,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC;YACjC,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;AACD,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;KAC7B;AAED;;;AAGG;AACO,IAAA,SAAS,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;KAClC;AAaD;;;AAGG;AACH,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AACxC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;YACxC,OAAO,IAAI,CAAC,WAAW,CAAC;SACzB;AACD,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;AAClC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,CAAS,EAAA;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;KACtB;;AAUD,IAAA,IAAI,GAAG,GAAA;QACL,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KAClD;IACD,IAAI,GAAG,CAAC,CAAS,EAAA;QACf,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3B;;AAGD,IAAA,IAAI,GAAG,GAAA;QACL,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;KAClD;IACD,IAAI,GAAG,CAAC,CAAS,EAAA;QACf,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3B;AAED,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACnD;IACD,IAAI,IAAI,CAAC,CAAS,EAAA;QAChB,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;KAC3B;;AAGD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;KACrD;IACD,IAAI,QAAQ,CAAC,CAAU,EAAA;AACrB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAE1B,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SACvC;KACF;;AAGD,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AACxC,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;SACpC;QACD,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAChF;;AAGD,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;SACpC;AACD,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;AAC1B,YAAA,OAAO,CAAC,CAAC;SACV;QACD,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;KACpD;;AAqBO,IAAA,aAAa,CAAC,CAAU,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KACrB;AA6CD,IAAA,WAAA,CACW,OAAe,EACf,WAAyC,EACzC,IAAuB,EACF,OAAmB,EAAA;QAHxC,IAAO,CAAA,OAAA,GAAP,OAAO,CAAQ;QACf,IAAW,CAAA,WAAA,GAAX,WAAW,CAA8B;QACzC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAmB;QACF,IAAO,CAAA,OAAA,GAAP,OAAO,CAAY;;AAnKhC,QAAA,IAAA,CAAA,WAAW,GAAyB,IAAI,YAAY,EAAU,CAAC;;AAG/D,QAAA,IAAA,CAAA,SAAS,GAC1B,IAAI,YAAY,EAAsB,CAAC;;AAGtB,QAAA,IAAA,CAAA,OAAO,GACxB,IAAI,YAAY,EAAsB,CAAC;AAqBzC;;;AAGG;AACH,QAAA,IAAA,CAAA,aAAa,GAAc,SAAS,CAAC,GAAG,CAAC;;AAgEzC,QAAA,IAAA,CAAA,UAAU,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;;QAGxB,IAAW,CAAA,WAAA,GAAW,CAAC,CAAC;;QAGxB,IAAe,CAAA,eAAA,GAAG,CAAC,CAAC;;QAGpB,IAAS,CAAA,SAAA,GAAY,KAAK,CAAC;;QAG3B,IAAU,CAAA,UAAA,GAAY,KAAK,CAAC;AAO5B;;;;;AAKG;QACK,IAAmB,CAAA,mBAAA,GAAY,KAAK,CAAC;;AAS1B,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAQ,CAAC;AAEpD;;;;;AAKG;QACH,IAAa,CAAA,aAAA,GAAY,KAAK,CAAC;;AAMvB,QAAA,IAAA,CAAA,YAAY,GAAe,MAAK,GAAG,CAAC;AAE5C;;;;;;;AAOG;QACO,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;AAEhC,QAAA,IAAA,CAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AAQnC,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,aAAa,CAAC;AAC9C,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClF,YAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClF,YAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChF,SAAC,CAAC,CAAC;KACJ;IAED,WAAW,GAAA;QACT,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1E,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;AACtE,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;KACzB;;IAGD,SAAS,GAAA;QACP,IAAI,CAAC,oBAAoB,EAAE,CAAC;;QAG5B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;;AAE3C,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;KACnB;;IAGD,MAAM,GAAA;QACJ,IAAI,CAAC,qBAAqB,EAAE,CAAC;KAC9B;IAED,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAChC,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AACpC,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;SACtC;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;YAC7C,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClC,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;SAC3B;KACF;IAED,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,GAAG,CAAC;KACjB;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;KACrB;IAED,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACnC,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KAC1B;IAED,SAAS,GAAA;QACP,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;AAGlC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,qBAAqB,CAAC,EAAC,aAAa,EAAE,IAAI,EAAC,CAAC,CAAC;SACnD;KACF;IAED,QAAQ,GAAA;QACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;;;QAG/B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,qBAAqB,CAAC,EAAC,aAAa,EAAE,IAAI,EAAC,CAAC,CAAC;SACnD;AACD,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;KACnC;IAED,uBAAuB,GAAA;;;QAGrB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACvC,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAa,CAAC,QAAQ,CAAC;KACrD;AAED,IAAA,cAAc,CAAC,KAAmB,EAAA;QAChC,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,OAAO;SACR;;;AAID,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;YACtB,MAAM,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAC/D,KAAK,EACL,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAChF,CAAC;AAEF,YAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC;YACvC,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,OAAO;SACR;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;;;AAIjC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,EAAC,aAAa,EAAE,IAAI,EAAC,CAAC,CAAC;SACjE;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC,CAAC;SAC9E;KACF;AAED;;;;;AAKG;AACK,IAAA,sBAAsB,CAAC,KAAmB,EAAA;;;;;AAKhD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;;;;;;QAO1B,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACvB,EAAE,CAAC,CAAC,CAAC;KACP;;AAGD,IAAA,SAAS,CAAC,KAAmB,EAAA;QAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AACtD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;QAC1E,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;;AAGzE,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;QAErE,MAAM,cAAc,GAClB,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;AAC7E,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;AACvD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;AAE7B,QAAA,IAAI,KAAK,KAAK,SAAS,EAAE;;;;AAIvB,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;AACnB,kBAAE,IAAI,CAAC,qBAAqB,EAAE;AAC9B,kBAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAC,CAAC,CAAC;YAC1F,OAAO;SACR;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;AACnB,cAAE,IAAI,CAAC,qBAAqB,EAAE;AAC9B,cAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAC,CAAC,CAAC;KAC3F;AAED,IAAA,cAAc,CAAC,KAAmB,EAAA;;;QAGhC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxC,YAAA,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAC;SAC1C;KACF;IAED,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AACvB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC,CAAC;;;;;YAM3E,UAAU,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;SAC5E;KACF;AAED,IAAA,MAAM,CAAC,CAAS,EAAA;AACd,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC;AAC7D,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;KACxC;IAED,sBAAsB,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACvB,QACE,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC9E,IAAI,CAAC,eAAe,EACpB;SACH;AACD,QAAA,QACE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YACxE,IAAI,CAAC,eAAe,EACpB;KACH;AAED,IAAA,6BAA6B,CAAC,KAAmB,EAAA;QAC/C,OAAO,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;KACjD;AAED;;;AAGG;AACH,IAAA,kBAAkB,MAAW;AAE7B;;;AAGG;IACH,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,CAAK,EAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,CAAC;QACtE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,eAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,GAAG,CACtD,KAAK,CAAC;AACN,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC;KAC1F;AAED,IAAA,qBAAqB,CAAC,OAAkC,EAAA;AACtD,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;KAC9B;IAED,4BAA4B,CAAC,KAAmB,EAAE,OAAkC,EAAA;AAClF,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAC;AACzE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;KAC9B;AAED,IAAA,cAAc,CAAC,OAAkC,EAAA;QAC/C,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;KACxC;AAED;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;QACnB,IAAI,IAAI,CAAC,qBAAqB,IAAI,KAAK,KAAK,IAAI,EAAE;AAChD,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;KACF;AAED;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;KACnC;AAED;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;KACxB;AAED;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;KAC5B;IAED,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;KAC3B;IAED,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;KAC1B;AAhgBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,mGAkMf,UAAU,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAlMT,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EACN,eAAe,CAPvB,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,aAAA,EAAA,OAAA,EAAA,YAAA,EAAA,MAAA,EAAA,WAAA,EAAA,OAAA,EAAA,YAAA,EAAA,EAAA,UAAA,EAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EAAA;YACT,+BAA+B;AAC/B,YAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAC;AACzD,SAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,cAAc,EAAA,UAAA,EAAA,CAAA;kBApB1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,MAAM,EAAE,OAAO;AACf,wBAAA,uBAAuB,EAAE,cAAc;AACvC,wBAAA,UAAU,EAAE,aAAa;AACzB,wBAAA,SAAS,EAAE,YAAY;;;AAGvB,wBAAA,QAAQ,EAAE,WAAW;AACrB,wBAAA,SAAS,EAAE,YAAY;AACxB,qBAAA;AACD,oBAAA,SAAS,EAAE;wBACT,+BAA+B;AAC/B,wBAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,gBAAgB,EAAC;AACzD,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAmMI,MAAM;2BAAC,UAAU,CAAA;yCAhMhB,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC,CAAA;gBA8BhB,WAAW,EAAA,CAAA;sBAA7B,MAAM;gBAGY,SAAS,EAAA,CAAA;sBAA3B,MAAM;gBAIY,OAAO,EAAA,CAAA;sBAAzB,MAAM;;AAseH,MAAO,mBAAoB,SAAQ,cAAc,CAAA;;IAErD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,CAE5E,CAAC;SACf;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAGD;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,EAAE;YACjC,OAAO,OAAO,CAAC,UAAU,CAAC;SAC3B;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;AAED;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;AAClC,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,EAAE;YAChC,OAAO,OAAO,CAAC,UAAU,CAAC;SAC3B;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC;KACzD;IAED,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,YAAY;YACf,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC5F;AAQD,IAAA,WAAA,CACE,OAAe,EACK,OAAmB,EACvC,WAAyC,EACvB,IAAuB,EAAA;QAEzC,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAFzB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAmB;QAGzC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,EAAE,CAAC;AACvB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC;KACzE;IAEQ,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;KACxE;IAEQ,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE,CAAC;QACjB,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;KACF;IAEQ,uBAAuB,GAAA;QAC9B,KAAK,CAAC,uBAAuB,EAAE,CAAC;AAChC,QAAA,IAAI,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,CAAC;KACpC;AAEQ,IAAA,cAAc,CAAC,KAAmB,EAAA;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;SACpF;AACD,QAAA,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KAC7B;IAEQ,YAAY,GAAA;QACnB,KAAK,CAAC,YAAY,EAAE,CAAC;AACrB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,QAAS,CAAC,oBAAoB,EAAE,CAAC;gBACtC,IAAI,CAAC,QAAS,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,wCAAwC,CAAC,CAAC;AACzF,aAAC,CAAC,CAAC;SACJ;KACF;AAEQ,IAAA,cAAc,CAAC,KAAmB,EAAA;AACzC,QAAA,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;KACF;AAEQ,IAAA,SAAS,CAAC,KAAmB,EAAA;AACpC,QAAA,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;KAChC;AAEQ,IAAA,MAAM,CAAC,CAAS,EAAA;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;KAClE;IAED,aAAa,GAAA;AACX,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;AACD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;AAC5B,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;SACtD;KACF;IAEQ,kBAAkB,GAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC;QACjF,MAAM,QAAQ,GACZ,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC/F,QAAA,MAAM,UAAU,GACd,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;cAC/B,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;cAC7D,CAAC,CAAC;AACR,QAAA,MAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAC;QAC/C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,EAAA,CAAI,CAAC;AAC7C,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,CAAK,EAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,CAAC;KACvE;IAEQ,oBAAoB,GAAA;AAC3B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;AACD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AACtE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW;AAC/B,cAAE,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC;AAC/C,cAAE,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AAElD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW;cAChC,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;cAC7D,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAElE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC,CAAC;;AAGzE,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;;;;AAK/C,QAAA,IAAI,UAAU,KAAK,CAAC,EAAE;YACpB,aAAa,GAAG,EAAE,CAAC;SACpB;AAAM,aAAA,IAAI,UAAU,KAAK,CAAC,EAAE;YAC3B,aAAa,GAAG,CAAC,CAAC;SACnB;AAED,QAAA,MAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,GAAG,aAAa,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,EAAA,CAAI,CAAC;QAC7C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;AAExC,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC;YACzF,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;SACxC;aAAM;YACL,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC;AACtC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI,CAAC;SAC3F;KACF;IAED,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,yBAAyB,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;KACnF;IAEO,cAAc,GAAA;AACpB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QACD,OAAO,CAAC,aAAa,EAAE,CAAC;AACxB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,CAAC,kBAAkB,EAAE,CAAC;SAC9B;aAAM;YACL,OAAO,CAAC,oBAAoB,EAAE,CAAC;SAChC;KACF;AAED;;;;AAIG;AACM,IAAA,UAAU,CAAC,KAAU,EAAA;QAC5B,IAAI,IAAI,CAAC,qBAAqB,IAAI,KAAK,KAAK,IAAI,EAAE;AAChD,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;KACF;AAEQ,IAAA,SAAS,CAAC,KAAa,EAAA;AAC9B,QAAA,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;KACvB;AAvNU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,wCAiDpB,UAAU,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAjDT,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EANnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sDAAA,EAAA,SAAA,EAAA;YACT,qCAAqC;AACrC,YAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,mBAAmB,EAAC;AACpE,SAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAT/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sDAAsD;AAChE,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,SAAS,EAAE;wBACT,qCAAqC;AACrC,wBAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,qBAAqB,EAAC;AACpE,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAkDI,MAAM;2BAAC,UAAU,CAAA;;;MC1nBT,eAAe,CAAA;8GAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAf,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YATxB,eAAe;YACf,eAAe;YACf,SAAS;YACT,cAAc;YACd,mBAAmB;AACnB,YAAA,oBAAoB,CAEZ,EAAA,OAAA,EAAA,CAAA,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAA,EAAA,CAAA,CAAA,EAAA;AAE7C,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YATxB,eAAe;YACf,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAQN,eAAe,EAAA,UAAA,EAAA,CAAA;kBAX3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,SAAS;wBACT,cAAc;wBACd,mBAAmB;wBACnB,oBAAoB;AACrB,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAC;AAC1D,iBAAA,CAAA;;;ACxBD;;AAEG;;;;"}