{"version": 3, "file": "tree.mjs", "sources": ["../../../../../../src/material/tree/node.ts", "../../../../../../src/material/tree/padding.ts", "../../../../../../src/material/tree/outlet.ts", "../../../../../../src/material/tree/tree.ts", "../../../../../../src/material/tree/toggle.ts", "../../../../../../src/material/tree/tree-module.ts", "../../../../../../src/material/tree/data-source/flat-data-source.ts", "../../../../../../src/material/tree/data-source/nested-data-source.ts", "../../../../../../src/material/tree/tree_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  CDK_TREE_NODE_OUTLET_NODE,\n  CdkNestedTreeNode,\n  CdkTree,\n  CdkTreeNode,\n  CdkTreeNodeDef,\n} from '@angular/cdk/tree';\nimport {\n  AfterContentInit,\n  Attribute,\n  Directive,\n  ElementRef,\n  Input,\n  IterableDiffers,\n  OnDestroy,\n  OnInit,\n  booleanAttribute,\n  numberAttribute,\n} from '@angular/core';\n\n/**\n * Wrapper for the CdkTree node with Material design styles.\n */\n@Directive({\n  selector: 'mat-tree-node',\n  exportAs: 'matTreeNode',\n  providers: [{provide: CdkTreeNode, useExisting: MatTreeNode}],\n  host: {\n    'class': 'mat-tree-node',\n  },\n  standalone: true,\n})\nexport class MatTreeNode<T, K = T> extends CdkTreeNode<T, K> implements OnInit, <PERSON><PERSON><PERSON>roy {\n  /** Whether the node is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Tabindex of the node. */\n  @Input({\n    transform: (value: unknown) => (value == null ? 0 : numberAttribute(value)),\n  })\n  tabIndex: number;\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    tree: CdkTree<T, K>,\n    @Attribute('tabindex') tabIndex: string,\n  ) {\n    super(elementRef, tree);\n    this.tabIndex = Number(tabIndex) || 0;\n  }\n\n  // This is a workaround for https://github.com/angular/angular/issues/23091\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  override ngOnInit() {\n    super.ngOnInit();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n}\n\n/**\n * Wrapper for the CdkTree node definition with Material design styles.\n * Captures the node's template and a when predicate that describes when this node should be used.\n */\n@Directive({\n  selector: '[matTreeNodeDef]',\n  inputs: [{name: 'when', alias: 'matTreeNodeDefWhen'}],\n  providers: [{provide: CdkTreeNodeDef, useExisting: MatTreeNodeDef}],\n  standalone: true,\n})\nexport class MatTreeNodeDef<T> extends CdkTreeNodeDef<T> {\n  @Input('matTreeNode') data: T;\n}\n\n/**\n * Wrapper for the CdkTree nested node with Material design styles.\n */\n@Directive({\n  selector: 'mat-nested-tree-node',\n  exportAs: 'matNestedTreeNode',\n  providers: [\n    {provide: CdkNestedTreeNode, useExisting: MatNestedTreeNode},\n    {provide: CdkTreeNode, useExisting: MatNestedTreeNode},\n    {provide: CDK_TREE_NODE_OUTLET_NODE, useExisting: MatNestedTreeNode},\n  ],\n  host: {\n    'class': 'mat-nested-tree-node',\n  },\n  standalone: true,\n})\nexport class MatNestedTreeNode<T, K = T>\n  extends CdkNestedTreeNode<T, K>\n  implements AfterContentInit, OnDestroy, OnInit\n{\n  @Input('matNestedTreeNode') node: T;\n\n  /** Whether the node is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Tabindex for the node. */\n  @Input()\n  get tabIndex(): number {\n    return this.disabled ? -1 : this._tabIndex;\n  }\n  set tabIndex(value: number) {\n    // If the specified tabIndex value is null or undefined, fall back to the default value.\n    this._tabIndex = value != null ? value : 0;\n  }\n  private _tabIndex: number;\n\n  constructor(\n    elementRef: ElementRef<HTMLElement>,\n    tree: CdkTree<T, K>,\n    differs: IterableDiffers,\n    @Attribute('tabindex') tabIndex: string,\n  ) {\n    super(elementRef, tree, differs);\n    this.tabIndex = Number(tabIndex) || 0;\n  }\n\n  // This is a workaround for https://github.com/angular/angular/issues/19145\n  // In aot mode, the lifecycle hooks from parent class are not called.\n  // TODO(tinayuangao): Remove when the angular issue #19145 is fixed\n  override ngOnInit() {\n    super.ngOnInit();\n  }\n\n  override ngAfterContentInit() {\n    super.ngAfterContentInit();\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CdkTreeNodePadding} from '@angular/cdk/tree';\nimport {Directive, Input, numberAttribute} from '@angular/core';\n\n/**\n * Wrapper for the CdkTree padding with Material design styles.\n */\n@Directive({\n  selector: '[matTreeNodePadding]',\n  providers: [{provide: CdkTreeNodePadding, useExisting: MatTreeNodePadding}],\n  standalone: true,\n})\nexport class MatTreeNodePadding<T, K = T> extends CdkTreeNodePadding<T, K> {\n  /** The level of depth of the tree node. The padding will be `level * indent` pixels. */\n  @Input({alias: 'matTreeNodePadding', transform: numberAttribute})\n  override get level(): number {\n    return this._level;\n  }\n  override set level(value: number) {\n    this._setLevelInput(value);\n  }\n\n  /** The indent for each level. Default number 40px from material design menu sub-menu spec. */\n  @Input('matTreeNodePaddingIndent')\n  override get indent(): number | string {\n    return this._indent;\n  }\n  override set indent(indent: number | string) {\n    this._setIndentInput(indent);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {CDK_TREE_NODE_OUTLET_NODE, CdkTreeNodeOutlet} from '@angular/cdk/tree';\nimport {Directive, Inject, Optional, ViewContainerRef} from '@angular/core';\n\n/**\n * Outlet for nested CdkNode. Put `[matTreeNodeOutlet]` on a tag to place children dataNodes\n * inside the outlet.\n */\n@Directive({\n  selector: '[matTreeNodeOutlet]',\n  providers: [\n    {\n      provide: CdkTreeNodeOutlet,\n      useExisting: MatTreeNodeOutlet,\n    },\n  ],\n  standalone: true,\n})\nexport class MatTreeNodeOutlet implements CdkTreeNodeOutlet {\n  constructor(\n    public viewContainer: ViewContainerRef,\n    @Inject(CDK_TREE_NODE_OUTLET_NODE) @Optional() public _node?: any,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CdkTree} from '@angular/cdk/tree';\nimport {ChangeDetectionStrategy, Component, ViewChild, ViewEncapsulation} from '@angular/core';\nimport {MatTreeNodeOutlet} from './outlet';\n\n/**\n * Wrapper for the CdkTable with Material design styles.\n */\n@Component({\n  selector: 'mat-tree',\n  exportAs: 'matTree',\n  template: `<ng-container matTreeNodeOutlet></ng-container>`,\n  host: {\n    'class': 'mat-tree',\n    'role': 'tree',\n  },\n  styleUrl: 'tree.css',\n  encapsulation: ViewEncapsulation.None,\n  // See note on CdkTree for explanation on why this uses the default change detection strategy.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  providers: [{provide: CdkTree, useExisting: MatTree}],\n  standalone: true,\n  imports: [MatTreeNodeOutlet],\n})\nexport class MatTree<T, K = T> extends CdkTree<T, K> {\n  // Outlets within the tree's template where the dataNodes will be inserted.\n  // We need an initializer here to avoid a TS error. The value will be set in `ngAfterViewInit`.\n  @ViewChild(MatTreeNodeOutlet, {static: true}) override _nodeOutlet: MatTreeNodeOutlet =\n    undefined!;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CdkTreeNodeToggle} from '@angular/cdk/tree';\nimport {Directive} from '@angular/core';\n\n/**\n * Wrapper for the CdkTree's toggle with Material design styles.\n */\n@Directive({\n  selector: '[matTreeNodeToggle]',\n  providers: [{provide: CdkTreeNodeToggle, useExisting: MatTreeNodeToggle}],\n  inputs: [{name: 'recursive', alias: 'matTreeNodeToggleRecursive'}],\n  standalone: true,\n})\nexport class MatTreeNodeToggle<T, K = T> extends CdkTreeNodeToggle<T, K> {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {CdkTreeModule} from '@angular/cdk/tree';\nimport {MatCommonModule} from '@angular/material/core';\nimport {MatNestedTreeNode, MatTreeNodeDef, MatTreeNode} from './node';\nimport {MatTree} from './tree';\nimport {MatTreeNodeToggle} from './toggle';\nimport {MatTreeNodeOutlet} from './outlet';\nimport {MatTreeNodePadding} from './padding';\n\nconst MAT_TREE_DIRECTIVES = [\n  MatNestedTreeNode,\n  MatTreeNodeDef,\n  MatTreeNodePadding,\n  MatTreeNodeToggle,\n  MatTree,\n  MatTreeNode,\n  MatTreeNodeOutlet,\n];\n\n@NgModule({\n  imports: [CdkTreeModule, MatCommonModule, ...MAT_TREE_DIRECTIVES],\n  exports: [MatCommonModule, MAT_TREE_DIRECTIVES],\n})\nexport class MatTreeModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CollectionViewer, DataSource} from '@angular/cdk/collections';\nimport {FlatTreeControl, TreeControl} from '@angular/cdk/tree';\nimport {BehaviorSubject, merge, Observable} from 'rxjs';\nimport {map, take} from 'rxjs/operators';\n\n/**\n * Tree flattener to convert a normal type of node to node with children & level information.\n * Transform nested nodes of type `T` to flattened nodes of type `F`.\n *\n * For example, the input data of type `T` is nested, and contains its children data:\n *   SomeNode: {\n *     key: 'Fruits',\n *     children: [\n *       NodeOne: {\n *         key: 'Apple',\n *       },\n *       NodeTwo: {\n *        key: 'Pear',\n *      }\n *    ]\n *  }\n *  After flattener flatten the tree, the structure will become\n *  SomeNode: {\n *    key: 'Fruits',\n *    expandable: true,\n *    level: 1\n *  },\n *  NodeOne: {\n *    key: 'Apple',\n *    expandable: false,\n *    level: 2\n *  },\n *  NodeTwo: {\n *   key: 'Pear',\n *   expandable: false,\n *   level: 2\n * }\n * and the output flattened type is `F` with additional information.\n */\nexport class MatTreeFlattener<T, F, K = F> {\n  constructor(\n    public transformFunction: (node: T, level: number) => F,\n    public getLevel: (node: F) => number,\n    public isExpandable: (node: F) => boolean,\n    public getChildren: (node: T) => Observable<T[]> | T[] | undefined | null,\n  ) {}\n\n  _flattenNode(node: T, level: number, resultNodes: F[], parentMap: boolean[]): F[] {\n    const flatNode = this.transformFunction(node, level);\n    resultNodes.push(flatNode);\n\n    if (this.isExpandable(flatNode)) {\n      const childrenNodes = this.getChildren(node);\n      if (childrenNodes) {\n        if (Array.isArray(childrenNodes)) {\n          this._flattenChildren(childrenNodes, level, resultNodes, parentMap);\n        } else {\n          childrenNodes.pipe(take(1)).subscribe(children => {\n            this._flattenChildren(children, level, resultNodes, parentMap);\n          });\n        }\n      }\n    }\n    return resultNodes;\n  }\n\n  _flattenChildren(children: T[], level: number, resultNodes: F[], parentMap: boolean[]): void {\n    children.forEach((child, index) => {\n      let childParentMap: boolean[] = parentMap.slice();\n      childParentMap.push(index != children.length - 1);\n      this._flattenNode(child, level + 1, resultNodes, childParentMap);\n    });\n  }\n\n  /**\n   * Flatten a list of node type T to flattened version of node F.\n   * Please note that type T may be nested, and the length of `structuredData` may be different\n   * from that of returned list `F[]`.\n   */\n  flattenNodes(structuredData: T[]): F[] {\n    let resultNodes: F[] = [];\n    structuredData.forEach(node => this._flattenNode(node, 0, resultNodes, []));\n    return resultNodes;\n  }\n\n  /**\n   * Expand flattened node with current expansion status.\n   * The returned list may have different length.\n   */\n  expandFlattenedNodes(nodes: F[], treeControl: TreeControl<F, K>): F[] {\n    let results: F[] = [];\n    let currentExpand: boolean[] = [];\n    currentExpand[0] = true;\n\n    nodes.forEach(node => {\n      let expand = true;\n      for (let i = 0; i <= this.getLevel(node); i++) {\n        expand = expand && currentExpand[i];\n      }\n      if (expand) {\n        results.push(node);\n      }\n      if (this.isExpandable(node)) {\n        currentExpand[this.getLevel(node) + 1] = treeControl.isExpanded(node);\n      }\n    });\n    return results;\n  }\n}\n\n/**\n * Data source for flat tree.\n * The data source need to handle expansion/collapsion of the tree node and change the data feed\n * to `MatTree`.\n * The nested tree nodes of type `T` are flattened through `MatTreeFlattener`, and converted\n * to type `F` for `MatTree` to consume.\n */\nexport class MatTreeFlatDataSource<T, F, K = F> extends DataSource<F> {\n  private readonly _flattenedData = new BehaviorSubject<F[]>([]);\n  private readonly _expandedData = new BehaviorSubject<F[]>([]);\n\n  get data() {\n    return this._data.value;\n  }\n  set data(value: T[]) {\n    this._data.next(value);\n    this._flattenedData.next(this._treeFlattener.flattenNodes(this.data));\n    this._treeControl.dataNodes = this._flattenedData.value;\n  }\n  private readonly _data = new BehaviorSubject<T[]>([]);\n\n  constructor(\n    private _treeControl: FlatTreeControl<F, K>,\n    private _treeFlattener: MatTreeFlattener<T, F, K>,\n    initialData?: T[],\n  ) {\n    super();\n\n    if (initialData) {\n      // Assign the data through the constructor to ensure that all of the logic is executed.\n      this.data = initialData;\n    }\n  }\n\n  connect(collectionViewer: CollectionViewer): Observable<F[]> {\n    return merge(\n      collectionViewer.viewChange,\n      this._treeControl.expansionModel.changed,\n      this._flattenedData,\n    ).pipe(\n      map(() => {\n        this._expandedData.next(\n          this._treeFlattener.expandFlattenedNodes(this._flattenedData.value, this._treeControl),\n        );\n        return this._expandedData.value;\n      }),\n    );\n  }\n\n  disconnect() {\n    // no op\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CollectionViewer, DataSource} from '@angular/cdk/collections';\nimport {BehaviorSubject, merge, Observable} from 'rxjs';\nimport {map} from 'rxjs/operators';\n\n/**\n * Data source for nested tree.\n *\n * The data source for nested tree doesn't have to consider node flattener, or the way to expand\n * or collapse. The expansion/collapsion will be handled by TreeControl and each non-leaf node.\n */\nexport class MatTreeNestedDataSource<T> extends DataSource<T> {\n  /**\n   * Data for the nested tree\n   */\n  get data() {\n    return this._data.value;\n  }\n  set data(value: T[]) {\n    this._data.next(value);\n  }\n  private readonly _data = new BehaviorSubject<T[]>([]);\n\n  connect(collectionViewer: CollectionViewer): Observable<T[]> {\n    return merge(...([collectionViewer.viewChange, this._data] as Observable<unknown>[])).pipe(\n      map(() => this.data),\n    );\n  }\n\n  disconnect() {\n    // no op\n  }\n}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;AA4BA;;AAEG;AAUG,MAAO,WAAsB,SAAQ,WAAiB,CAAA;AAW1D,IAAA,WAAA,CACE,UAAmC,EACnC,IAAmB,EACI,QAAgB,EAAA;AAEvC,QAAA,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;;QAb1B,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;QAcxB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACvC;;;IAIQ,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE,CAAC;KAClB;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;KACrB;AA5BU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAW,mEAcT,UAAU,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAdZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,WAAW,EAEH,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAKtB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,2DAblE,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAC,CAAC,EAAA,QAAA,EAAA,CAAA,aAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAMlD,WAAW,EAAA,UAAA,EAAA,CAAA;kBATvB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,QAAQ,EAAE,aAAa;oBACvB,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAa,WAAA,EAAC,CAAC;AAC7D,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,eAAe;AACzB,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAeI,SAAS;2BAAC,UAAU,CAAA;yCAXvB,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAOpC,QAAQ,EAAA,CAAA;sBAHP,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA;wBACL,SAAS,EAAE,CAAC,KAAc,MAAM,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC;AAC5E,qBAAA,CAAA;;AAuBH;;;AAGG;AAOG,MAAO,cAAkB,SAAQ,cAAiB,CAAA;8GAA3C,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,EAAA,IAAA,EAAA,CAAA,aAAA,EAAA,MAAA,CAAA,EAAA,EAAA,SAAA,EAHd,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGxD,cAAc,EAAA,UAAA,EAAA,CAAA;kBAN1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;oBAC5B,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,oBAAoB,EAAC,CAAC;oBACrD,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAgB,cAAA,EAAC,CAAC;AACnE,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAEuB,IAAI,EAAA,CAAA;sBAAzB,KAAK;uBAAC,aAAa,CAAA;;AAGtB;;AAEG;AAcG,MAAO,iBACX,SAAQ,iBAAuB,CAAA;;AAU/B,IAAA,IACI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;KAC5C;IACD,IAAI,QAAQ,CAAC,KAAa,EAAA;;AAExB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC;KAC5C;AAGD,IAAA,WAAA,CACE,UAAmC,EACnC,IAAmB,EACnB,OAAwB,EACD,QAAgB,EAAA;AAEvC,QAAA,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;;QAnBnC,IAAQ,CAAA,QAAA,GAAY,KAAK,CAAC;QAoBxB,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACvC;;;;IAKQ,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE,CAAC;KAClB;IAEQ,kBAAkB,GAAA;QACzB,KAAK,CAAC,kBAAkB,EAAE,CAAC;KAC5B;IAEQ,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE,CAAC;KACrB;AA5CU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kGAyBf,UAAU,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAzBZ,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAOT,gBAAgB,CAjBxB,EAAA,QAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AAC5D,YAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACtD,YAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,EAAE,iBAAiB,EAAC;AACrE,SAAA,EAAA,QAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAMU,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAb7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,QAAQ,EAAE,mBAAmB;AAC7B,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,mBAAmB,EAAC;AAC5D,wBAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,mBAAmB,EAAC;AACtD,wBAAA,EAAC,OAAO,EAAE,yBAAyB,EAAE,WAAW,mBAAmB,EAAC;AACrE,qBAAA;AACD,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,sBAAsB;AAChC,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BA0BI,SAAS;2BAAC,UAAU,CAAA;yCArBK,IAAI,EAAA,CAAA;sBAA/B,KAAK;uBAAC,mBAAmB,CAAA;gBAI1B,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC,CAAA;gBAKhC,QAAQ,EAAA,CAAA;sBADX,KAAK;;;ACtGR;;AAEG;AAMG,MAAO,kBAA6B,SAAQ,kBAAwB,CAAA;;AAExE,IAAA,IACa,KAAK,GAAA;QAChB,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IACD,IAAa,KAAK,CAAC,KAAa,EAAA;AAC9B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KAC5B;;AAGD,IAAA,IACa,MAAM,GAAA;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IACD,IAAa,MAAM,CAAC,MAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;KAC9B;8GAjBU,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAlB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,kBAAkB,EAEmB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,oBAAA,EAAA,OAAA,EAAA,eAAe,CALpD,EAAA,MAAA,EAAA,CAAA,0BAAA,EAAA,QAAA,CAAA,EAAA,EAAA,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGhE,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;oBAChC,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,WAAW,EAAoB,kBAAA,EAAC,CAAC;AAC3E,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;8BAIc,KAAK,EAAA,CAAA;sBADjB,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,oBAAoB,EAAE,SAAS,EAAE,eAAe,EAAC,CAAA;gBAUnD,MAAM,EAAA,CAAA;sBADlB,KAAK;uBAAC,0BAA0B,CAAA;;;ACnBnC;;;AAGG;MAWU,iBAAiB,CAAA;IAC5B,WACS,CAAA,aAA+B,EACgB,KAAW,EAAA;QAD1D,IAAa,CAAA,aAAA,GAAb,aAAa,CAAkB;QACgB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAM;KAC/D;AAJO,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,kDAGlB,yBAAyB,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAHxB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,EARjB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,iBAAiB;AAC1B,gBAAA,WAAW,EAAE,iBAAiB;AAC/B,aAAA;AACF,SAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAGU,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAV7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,iBAAiB;AAC1B,4BAAA,WAAW,EAAmB,iBAAA;AAC/B,yBAAA;AACF,qBAAA;AACD,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;0BAII,MAAM;2BAAC,yBAAyB,CAAA;;0BAAG,QAAQ;;;ACfhD;;AAEG;AAkBG,MAAO,OAAkB,SAAQ,OAAa,CAAA;AAjBpD,IAAA,WAAA,GAAA;;;;QAoByD,IAAW,CAAA,WAAA,GAChE,SAAU,CAAC;AACd,KAAA;8GALY,OAAO,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;AAAP,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,6HAJP,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAC,CAAC,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,aAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAO1C,iBAAiB,EAjBlB,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,SAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,+CAAA,CAAiD,4gBAYjD,iBAAiB,EAAA,QAAA,EAAA,qBAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FAEhB,OAAO,EAAA,UAAA,EAAA,CAAA;kBAjBnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EACV,QAAA,EAAA,SAAS,EACT,QAAA,EAAA,CAAA,+CAAA,CAAiD,EACrD,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,UAAU;AACnB,wBAAA,MAAM,EAAE,MAAM;qBACf,EAEc,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,eAAA,EAGpB,uBAAuB,CAAC,OAAO,EACrC,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAA,OAAS,EAAC,CAAC,cACzC,IAAI,EAAA,OAAA,EACP,CAAC,iBAAiB,CAAC,EAAA,MAAA,EAAA,CAAA,ocAAA,CAAA,EAAA,CAAA;8BAK2B,WAAW,EAAA,CAAA;sBAAjE,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,iBAAiB,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAA;;;ACxB9C;;AAEG;AAOG,MAAO,iBAA4B,SAAQ,iBAAuB,CAAA;8GAA3D,iBAAiB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAjB,iBAAiB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,4BAAA,EAAA,WAAA,CAAA,EAAA,EAAA,SAAA,EAJjB,CAAC,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,iBAAiB,EAAC,CAAC,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAI9D,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAN7B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,qBAAqB;oBAC/B,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAmB,iBAAA,EAAC,CAAC;oBACzE,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,4BAA4B,EAAC,CAAC;AAClE,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;;ACDD,MAAM,mBAAmB,GAAG;IAC1B,iBAAiB;IACjB,cAAc;IACd,kBAAkB;IAClB,iBAAiB;IACjB,OAAO;IACP,WAAW;IACX,iBAAiB;CAClB,CAAC;MAMW,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAb,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAHd,OAAA,EAAA,CAAA,aAAa,EAAE,eAAe,EAVxC,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,iBAAiB,CAAA,EAAA,OAAA,EAAA,CAKP,eAAe,EAXzB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;YAClB,iBAAiB;YACjB,OAAO;YACP,WAAW;YACX,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;AAON,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,EAHd,OAAA,EAAA,CAAA,aAAa,EAAE,eAAe,EAC9B,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAEd,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,GAAG,mBAAmB,CAAC;AACjE,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;AAChD,iBAAA,CAAA;;;AClBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCG;MACU,gBAAgB,CAAA;AAC3B,IAAA,WAAA,CACS,iBAAgD,EAChD,QAA6B,EAC7B,YAAkC,EAClC,WAAkE,EAAA;QAHlE,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB,CAA+B;QAChD,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAqB;QAC7B,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAsB;QAClC,IAAW,CAAA,WAAA,GAAX,WAAW,CAAuD;KACvE;AAEJ,IAAA,YAAY,CAAC,IAAO,EAAE,KAAa,EAAE,WAAgB,EAAE,SAAoB,EAAA;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACrD,QAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAE3B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;YAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,aAAa,EAAE;AACjB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;oBAChC,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;iBACrE;qBAAM;AACL,oBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,IAAG;wBAC/C,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACjE,qBAAC,CAAC,CAAC;iBACJ;aACF;SACF;AACD,QAAA,OAAO,WAAW,CAAC;KACpB;AAED,IAAA,gBAAgB,CAAC,QAAa,EAAE,KAAa,EAAE,WAAgB,EAAE,SAAoB,EAAA;QACnF,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAI;AAChC,YAAA,IAAI,cAAc,GAAc,SAAS,CAAC,KAAK,EAAE,CAAC;YAClD,cAAc,CAAC,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAClD,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AACnE,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;AACH,IAAA,YAAY,CAAC,cAAmB,EAAA;QAC9B,IAAI,WAAW,GAAQ,EAAE,CAAC;QAC1B,cAAc,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5E,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;;AAGG;IACH,oBAAoB,CAAC,KAAU,EAAE,WAA8B,EAAA;QAC7D,IAAI,OAAO,GAAQ,EAAE,CAAC;QACtB,IAAI,aAAa,GAAc,EAAE,CAAC;AAClC,QAAA,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAExB,QAAA,KAAK,CAAC,OAAO,CAAC,IAAI,IAAG;YACnB,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC7C,gBAAA,MAAM,GAAG,MAAM,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC;aACrC;YACD,IAAI,MAAM,EAAE;AACV,gBAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACpB;AACD,YAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC3B,gBAAA,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aACvE;AACH,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,OAAO,CAAC;KAChB;AACF,CAAA;AAED;;;;;;AAMG;AACG,MAAO,qBAAmC,SAAQ,UAAa,CAAA;AAInE,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;KACzB;IACD,IAAI,IAAI,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvB,QAAA,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;KACzD;AAGD,IAAA,WAAA,CACU,YAAmC,EACnC,cAAyC,EACjD,WAAiB,EAAA;AAEjB,QAAA,KAAK,EAAE,CAAC;QAJA,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAuB;QACnC,IAAc,CAAA,cAAA,GAAd,cAAc,CAA2B;AAflC,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC,CAAC;AAC9C,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC,CAAC;AAU7C,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC,CAAC;QASpD,IAAI,WAAW,EAAE;;AAEf,YAAA,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;SACzB;KACF;AAED,IAAA,OAAO,CAAC,gBAAkC,EAAA;QACxC,OAAO,KAAK,CACV,gBAAgB,CAAC,UAAU,EAC3B,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,EACxC,IAAI,CAAC,cAAc,CACpB,CAAC,IAAI,CACJ,GAAG,CAAC,MAAK;YACP,IAAI,CAAC,aAAa,CAAC,IAAI,CACrB,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CACvF,CAAC;AACF,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;SACjC,CAAC,CACH,CAAC;KACH;IAED,UAAU,GAAA;;KAET;AACF;;AC9JD;;;;;AAKG;AACG,MAAO,uBAA2B,SAAQ,UAAa,CAAA;AAA7D,IAAA,WAAA,GAAA;;AAUmB,QAAA,IAAA,CAAA,KAAK,GAAG,IAAI,eAAe,CAAM,EAAE,CAAC,CAAC;KAWvD;AApBC;;AAEG;AACH,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;KACzB;IACD,IAAI,IAAI,CAAC,KAAU,EAAA;AACjB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACxB;AAGD,IAAA,OAAO,CAAC,gBAAkC,EAAA;QACxC,OAAO,KAAK,CAAC,GAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAA2B,CAAC,CAAC,IAAI,CACxF,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CACrB,CAAC;KACH;IAED,UAAU,GAAA;;KAET;AACF;;ACvCD;;AAEG;;;;"}