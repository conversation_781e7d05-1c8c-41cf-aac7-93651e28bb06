{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/chips/testing/chip-avatar-harness.ts", "../../../../../../../src/material/chips/testing/chip-remove-harness.ts", "../../../../../../../src/material/chips/testing/chip-harness.ts", "../../../../../../../src/material/chips/testing/chip-input-harness.ts", "../../../../../../../src/material/chips/testing/chip-option-harness.ts", "../../../../../../../src/material/chips/testing/chip-listbox-harness.ts", "../../../../../../../src/material/chips/testing/chip-edit-input-harness.ts", "../../../../../../../src/material/chips/testing/chip-row-harness.ts", "../../../../../../../src/material/chips/testing/chip-grid-harness.ts", "../../../../../../../src/material/chips/testing/chip-set-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ChipAvatarHarnessFilters} from './chip-harness-filters';\n\n/** Ha<PERSON>ss for interacting with a standard Material chip avatar in tests. */\nexport class MatChipAvatarHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-chip-avatar';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip avatar with specific\n   * attributes.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipAvatarHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipAvatarHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ChipRemoveHarnessFilters} from './chip-harness-filters';\n\n/** Harness for interacting with a standard Material chip remove button in tests. */\nexport class MatChipRemoveHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-chip-remove';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip remove with specific\n   * attributes.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipRemoveHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipRemoveHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Clicks the remove button. */\n  async click(): Promise<void> {\n    return (await this.host()).click();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n  <PERSON>Key,\n} from '@angular/cdk/testing';\nimport {MatChipAvatarHarness} from './chip-avatar-harness';\nimport {\n  ChipAvatarHarnessFilters,\n  ChipHarnessFilters,\n  ChipRemoveHarnessFilters,\n} from './chip-harness-filters';\nimport {MatChipRemoveHarness} from './chip-remove-harness';\n\n/** Harness for interacting with a mat-chip in tests. */\nexport class MatChipHarness extends ContentContainerComponentHarness {\n  protected _primaryAction = this.locatorFor('.mdc-evolution-chip__action--primary');\n\n  static hostSelector = '.mat-mdc-basic-chip, .mat-mdc-chip';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip with specific attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, (harness, label) => {\n        return HarnessPredicate.stringMatches(harness.getText(), label);\n      })\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /** Gets a promise for the text content the option. */\n  async getText(): Promise<string> {\n    return (await this.host()).text({\n      exclude: '.mat-mdc-chip-avatar, .mat-mdc-chip-trailing-icon, .mat-icon',\n    });\n  }\n\n  /** Whether the chip is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-chip-disabled');\n  }\n\n  /** Delete a chip from the set. */\n  async remove(): Promise<void> {\n    const hostEl = await this.host();\n    await hostEl.sendKeys(TestKey.DELETE);\n  }\n\n  /**\n   * Gets the remove button inside of a chip.\n   * @param filter Optionally filters which chips are included.\n   */\n  async getRemoveButton(filter: ChipRemoveHarnessFilters = {}): Promise<MatChipRemoveHarness> {\n    return this.locatorFor(MatChipRemoveHarness.with(filter))();\n  }\n\n  /**\n   * Gets the avatar inside a chip.\n   * @param filter Optionally filters which avatars are included.\n   */\n  async getAvatar(filter: ChipAvatarHarnessFilters = {}): Promise<MatChipAvatarHarness | null> {\n    return this.locatorForOptional(MatChipAvatarHarness.with(filter))();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  TestKey,\n} from '@angular/cdk/testing';\nimport {ChipInputHarnessFilters} from './chip-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a grid's chip input in tests. */\nexport class MatChipInputHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-chip-input';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip input with specific\n   * attributes.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipInputHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipInputHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('value', options.value, async (harness, value) => {\n        return (await harness.getValue()) === value;\n      })\n      .addOption('placeholder', options.placeholder, async (harness, placeholder) => {\n        return (await harness.getPlaceholder()) === placeholder;\n      })\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /** Whether the input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Whether the input is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Gets the value of the input. */\n  async getValue(): Promise<string> {\n    // The \"value\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('value');\n  }\n\n  /** Gets the placeholder of the input. */\n  async getPlaceholder(): Promise<string> {\n    return await (await this.host()).getProperty<string>('placeholder');\n  }\n\n  /**\n   * Focuses the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /**\n   * Blurs the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the input is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /**\n   * Sets the value of the input. The value will be set by simulating\n   * keypresses that correspond to the given value.\n   */\n  async setValue(newValue: string): Promise<void> {\n    const inputEl = await this.host();\n    await inputEl.clear();\n\n    // We don't want to send keys for the value if the value is an empty\n    // string in order to clear the value. Sending keys with an empty string\n    // still results in unnecessary focus events.\n    if (newValue) {\n      await inputEl.sendKeys(newValue);\n    }\n  }\n\n  /** Sends a chip separator key to the input element. */\n  async sendSeparatorKey(key: TestKey | string): Promise<void> {\n    const inputEl = await this.host();\n    return inputEl.sendKeys(key);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarnessConstructor, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatChipHarness} from './chip-harness';\nimport {ChipOptionHarnessFilters} from './chip-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a mat-chip-option in tests. */\nexport class MatChipOptionHarness extends MatChipHarness {\n  static override hostSelector = '.mat-mdc-chip-option';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip option with specific\n   * attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static override with<T extends MatChipHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipOptionHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(MatChipOptionHarness, options)\n      .addOption('text', options.text, (harness, label) =>\n        HarnessPredicate.stringMatches(harness.getText(), label),\n      )\n      .addOption(\n        'selected',\n        options.selected,\n        async (harness, selected) => (await harness.isSelected()) === selected,\n      ) as unknown as HarnessPredicate<T>;\n  }\n\n  /** Whether the chip is selected. */\n  async isSelected(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-chip-selected');\n  }\n\n  /** Selects the given chip. Only applies if it's selectable. */\n  async select(): Promise<void> {\n    if (!(await this.isSelected())) {\n      await this.toggle();\n    }\n  }\n\n  /** Deselects the given chip. Only applies if it's selectable. */\n  async deselect(): Promise<void> {\n    if (await this.isSelected()) {\n      await this.toggle();\n    }\n  }\n\n  /** Toggles the selected state of the given chip. */\n  async toggle(): Promise<void> {\n    return (await this._primaryAction()).click();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n  parallel,\n} from '@angular/cdk/testing';\nimport {ChipListboxHarnessFilters, ChipOptionHarnessFilters} from './chip-harness-filters';\nimport {MatChipOptionHarness} from './chip-option-harness';\n\n/** Harness for interacting with a mat-chip-listbox in tests. */\nexport class MatChipListboxHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-chip-listbox';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip listbox with specific\n   * attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipListboxHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipListboxHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'disabled',\n      options.disabled,\n      async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      },\n    );\n  }\n\n  /** Gets whether the chip listbox is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Gets whether the chip listbox is required. */\n  async isRequired(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-required')) === 'true';\n  }\n\n  /** Gets whether the chip listbox is in multi selection mode. */\n  async isMultiple(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-multiselectable')) === 'true';\n  }\n\n  /** Gets whether the orientation of the chip list. */\n  async getOrientation(): Promise<'horizontal' | 'vertical'> {\n    const orientation = await (await this.host()).getAttribute('aria-orientation');\n    return orientation === 'vertical' ? 'vertical' : 'horizontal';\n  }\n\n  /**\n   * Gets the list of chips inside the chip list.\n   * @param filter Optionally filters which chips are included.\n   */\n  async getChips(filter: ChipOptionHarnessFilters = {}): Promise<MatChipOptionHarness[]> {\n    return this.locatorForAll(MatChipOptionHarness.with(filter))();\n  }\n\n  /**\n   * Selects a chip inside the chip list.\n   * @param filter An optional filter to apply to the child chips.\n   *    All the chips matching the filter will be selected.\n   */\n  async selectChips(filter: ChipOptionHarnessFilters = {}): Promise<void> {\n    const chips = await this.getChips(filter);\n    if (!chips.length) {\n      throw Error(`Cannot find chip matching filter ${JSON.stringify(filter)}`);\n    }\n    await parallel(() => chips.map(chip => chip.select()));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ChipEditInputHarnessFilters} from './chip-harness-filters';\n\n/** Harness for interacting with an editable chip's input in tests. */\nexport class MatChipEditInputHarness extends ComponentHarness {\n  static hostSelector = '.mat-chip-edit-input';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip edit input with specific\n   * attributes.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipEditInputHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipEditInputHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Sets the value of the input. */\n  async setValue(value: string): Promise<void> {\n    const host = await this.host();\n\n    // @breaking-change 16.0.0 Remove this null check once `setContenteditableValue`\n    // becomes a required method.\n    if (!host.setContenteditableValue) {\n      throw new Error(\n        'Cannot set chip edit input value, because test ' +\n          'element does not implement the `setContenteditableValue` method.',\n      );\n    }\n\n    return host.setContenteditableValue(value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {TestKey} from '@angular/cdk/testing';\nimport {MatChipEditInputHarness} from './chip-edit-input-harness';\nimport {MatChipHarness} from './chip-harness';\nimport {ChipEditInputHarnessFilters} from './chip-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with a mat-chip-row in tests. */\nexport class MatChipRowHarness extends MatChipHarness {\n  static override hostSelector = '.mat-mdc-chip-row';\n\n  /** Whether the chip is editable. */\n  async isEditable(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-chip-editable');\n  }\n\n  /** Whether the chip is currently being edited. */\n  async isEditing(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-chip-editing');\n  }\n\n  /** Sets the chip row into an editing state, if it is editable. */\n  async startEditing(): Promise<void> {\n    if (!(await this.isEditable())) {\n      throw new Error('Cannot begin editing a chip that is not editable.');\n    }\n    return (await this.host()).dispatchEvent('dblclick');\n  }\n\n  /** Stops editing the chip, if it was in the editing state. */\n  async finishEditing(): Promise<void> {\n    if (await this.isEditing()) {\n      await (await this.host()).sendKeys(TestKey.ENTER);\n    }\n  }\n\n  /** Gets the edit input inside the chip row. */\n  async getEditInput(filter: ChipEditInputHarnessFilters = {}): Promise<MatChipEditInputHarness> {\n    return this.locatorFor(MatChipEditInputHarness.with(filter))();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {\n  ChipGridHarnessFilters,\n  ChipInputHarnessFilters,\n  ChipRowHarnessFilters,\n} from './chip-harness-filters';\nimport {MatChipInputHarness} from './chip-input-harness';\nimport {MatChipRowHarness} from './chip-row-harness';\n\n/** Harness for interacting with a mat-chip-grid in tests. */\nexport class MatChipGridHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-chip-grid';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip grid with specific attributes.\n   * @param options Options for filtering which chip grid instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipGridHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipGridHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'disabled',\n      options.disabled,\n      async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      },\n    );\n  }\n\n  /** Gets whether the chip grid is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Gets whether the chip grid is required. */\n  async isRequired(): Promise<boolean> {\n    return await (await this.host()).hasClass('mat-mdc-chip-list-required');\n  }\n\n  /** Gets whether the chip grid is invalid. */\n  async isInvalid(): Promise<boolean> {\n    return (await (await this.host()).getAttribute('aria-invalid')) === 'true';\n  }\n\n  /** Gets promise of the harnesses for the chip rows. */\n  getRows(filter: ChipRowHarnessFilters = {}): Promise<MatChipRowHarness[]> {\n    return this.locatorForAll(MatChipRowHarness.with(filter))();\n  }\n\n  /** Gets promise of the chip text input harness. */\n  getInput(filter: ChipInputHarnessFilters = {}): Promise<MatChipInputHarness | null> {\n    return this.locatorFor(MatChipInputHarness.with(filter))();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {MatChipHarness} from './chip-harness';\nimport {ChipHarnessFilters, ChipSetHarnessFilters} from './chip-harness-filters';\n\n/** Harness for interacting with a mat-chip-set in tests. */\nexport class MatChipSetHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-chip-set';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a chip set with specific attributes.\n   * @param options Options for filtering which chip set instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatChipSetHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ChipSetHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Gets promise of the harnesses for the chips. */\n  async getChips(filter: ChipHarnessFilters = {}): Promise<MatChipHarness[]> {\n    return await this.locatorForAll(MatChipHarness.with(filter))();\n  }\n}\n"], "names": [], "mappings": ";;AAeA;AACM,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;aACjD,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAoC,EAAE,EAAA;AAEtC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;;ACfH;AACM,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;aACjD,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAoC,EAAE,EAAA;AAEtC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;KACpC;;;ACbH;AACM,MAAO,cAAe,SAAQ,gCAAgC,CAAA;AAApE,IAAA,WAAA,GAAA;;AACY,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC;KAuDpF;aArDQ,IAAY,CAAA,YAAA,GAAG,oCAAH,CAAwC,EAAA;AAE3D;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAA8B,EAAE,EAAA;AAEhC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;AACvC,aAAA,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;YAClD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;AAClE,SAAC,CAAC;AACD,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CAAC,CAAC;KACN;;AAGD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC;AAC9B,YAAA,OAAO,EAAE,8DAA8D;AACxE,SAAA,CAAC,CAAC;KACJ;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACjC,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KACvC;AAED;;;AAGG;AACH,IAAA,MAAM,eAAe,CAAC,MAAA,GAAmC,EAAE,EAAA;AACzD,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC7D;AAED;;;AAGG;AACH,IAAA,MAAM,SAAS,CAAC,MAAA,GAAmC,EAAE,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KACrE;;;AC9DH;AACM,MAAO,mBAAoB,SAAQ,gBAAgB,CAAA;aAChD,IAAY,CAAA,YAAA,GAAG,qBAAqB,CAAC,EAAA;AAE5C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAmC,EAAE,EAAA;AAErC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;AACvC,aAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,OAAO,EAAE,KAAK,KAAI;YAC1D,OAAO,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC9C,SAAC,CAAC;AACD,aAAA,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,OAAO,EAAE,WAAW,KAAI;YAC5E,OAAO,CAAC,MAAM,OAAO,CAAC,cAAc,EAAE,MAAM,WAAW,CAAC;AAC1D,SAAC,CAAC;AACD,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CAAC,CAAC;KACN;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,QAAQ,GAAA;;AAEZ,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC,CAAC;KAC/D;;AAGD,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,aAAa,CAAC,CAAC;KACrE;AAED;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC;KACpC;AAED;;;AAGG;AACH,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC;KACnC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC;KACxC;AAED;;;AAGG;IACH,MAAM,QAAQ,CAAC,QAAgB,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClC,QAAA,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;;;;QAKtB,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAClC;KACF;;IAGD,MAAM,gBAAgB,CAAC,GAAqB,EAAA;AAC1C,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClC,QAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KAC9B;;;AC5FH;AACM,MAAO,oBAAqB,SAAQ,cAAc,CAAA;aACtC,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAEtD;;;;;AAKG;AACH,IAAA,OAAgB,IAAI,CAElB,OAAA,GAAoC,EAAE,EAAA;AAEtC,QAAA,OAAO,IAAI,gBAAgB,CAAC,oBAAoB,EAAE,OAAO,CAAC;aACvD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,KAC9C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CACzD;aACA,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CACrC,CAAC;KACvC;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,MAAM,GAAA;QACV,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;;AAGD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE;AAC3B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;;AAGD,IAAA,MAAM,MAAM,GAAA;QACV,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;KAC9C;;;AC1CH;AACM,MAAO,qBAAsB,SAAQ,gBAAgB,CAAA;aAClD,IAAY,CAAA,YAAA,GAAG,uBAAuB,CAAC,EAAA;AAE9C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAqC,EAAE,EAAA;QAEvC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAI;YAC1B,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CACF,CAAC;KACH;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC;KAC7E;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC;KAC7E;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,sBAAsB,CAAC,MAAM,MAAM,CAAC;KACpF;;AAGD,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC;QAC/E,OAAO,WAAW,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY,CAAC;KAC/D;AAED;;;AAGG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAA,GAAmC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAChE;AAED;;;;AAIG;AACH,IAAA,MAAM,WAAW,CAAC,MAAA,GAAmC,EAAE,EAAA;QACrD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,MAAM,KAAK,CAAC,CAAA,iCAAA,EAAoC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA,CAAC,CAAC;SAC3E;AACD,QAAA,MAAM,QAAQ,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KACxD;;;ACjEH;AACM,MAAO,uBAAwB,SAAQ,gBAAgB,CAAA;aACpD,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC,EAAA;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAuC,EAAE,EAAA;AAEzC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;IAGD,MAAM,QAAQ,CAAC,KAAa,EAAA;AAC1B,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;;;AAI/B,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,MAAM,IAAI,KAAK,CACb,iDAAiD;AAC/C,gBAAA,kEAAkE,CACrE,CAAC;SACH;AAED,QAAA,OAAO,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;KAC5C;;;ACjCH;AACM,MAAO,iBAAkB,SAAQ,cAAc,CAAA;aACnC,IAAY,CAAA,YAAA,GAAG,mBAAmB,CAAC,EAAA;;AAGnD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,uBAAuB,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,sBAAsB,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,YAAY,GAAA;QAChB,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;SACtE;AACD,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;KACtD;;AAGD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE;AAC1B,YAAA,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACnD;KACF;;AAGD,IAAA,MAAM,YAAY,CAAC,MAAA,GAAsC,EAAE,EAAA;AACzD,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,uBAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAChE;;;ACxBH;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;aAC/C,IAAY,CAAA,YAAA,GAAG,oBAAoB,CAAC,EAAA;AAE3C;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAkC,EAAE,EAAA;QAEpC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAI;YAC1B,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ,CAAC;AACnD,SAAC,CACF,CAAC;KACH;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM,CAAC;KAC7E;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,4BAA4B,CAAC,CAAC;KACzE;;AAGD,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,cAAc,CAAC,MAAM,MAAM,CAAC;KAC5E;;IAGD,OAAO,CAAC,SAAgC,EAAE,EAAA;AACxC,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC7D;;IAGD,QAAQ,CAAC,SAAkC,EAAE,EAAA;AAC3C,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC5D;;;AClDH;AACM,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;aAC9C,IAAY,CAAA,YAAA,GAAG,mBAAmB,CAAC,EAAA;AAE1C;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAiC,EAAE,EAAA;AAEnC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGD,IAAA,MAAM,QAAQ,CAAC,MAAA,GAA6B,EAAE,EAAA;AAC5C,QAAA,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAChE;;;;;"}