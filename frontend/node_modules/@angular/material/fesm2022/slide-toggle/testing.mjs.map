{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/slide-toggle/testing/slide-toggle-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {SlideToggleHarnessFilters} from './slide-toggle-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a mat-slide-toggle in tests. */\nexport class MatSlideToggleHarness extends ComponentHarness {\n  private _label = this.locatorFor('label');\n  _nativeElement = this.locatorFor('button');\n  static hostSelector = '.mat-mdc-slide-toggle';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a slide-toggle w/ specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `selector` finds a slide-toggle whose host element matches the given selector.\n   *   - `label` finds a slide-toggle with specific label text.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatSlideToggleHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: SlideToggleHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return (\n      new HarnessPredicate(this, options)\n        .addOption('label', options.label, (harness, label) =>\n          HarnessPredicate.stringMatches(harness.getLabelText(), label),\n        )\n        // We want to provide a filter option for \"name\" because the name of the slide-toggle is\n        // only set on the underlying input. This means that it's not possible for developers\n        // to retrieve the harness of a specific checkbox with name through a CSS selector.\n        .addOption(\n          'name',\n          options.name,\n          async (harness, name) => (await harness.getName()) === name,\n        )\n        .addOption(\n          'checked',\n          options.checked,\n          async (harness, checked) => (await harness.isChecked()) == checked,\n        )\n        .addOption(\n          'disabled',\n          options.disabled,\n          async (harness, disabled) => (await harness.isDisabled()) == disabled,\n        )\n    );\n  }\n\n  /** Toggle the checked state of the slide-toggle. */\n  async toggle(): Promise<void> {\n    return (await this._nativeElement()).click();\n  }\n\n  /** Whether the slide-toggle is checked. */\n  async isChecked(): Promise<boolean> {\n    const checked = (await this._nativeElement()).getAttribute('aria-checked');\n    return coerceBooleanProperty(await checked);\n  }\n\n  /** Whether the slide-toggle is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const disabled = (await this._nativeElement()).getAttribute('disabled');\n    return coerceBooleanProperty(await disabled);\n  }\n\n  /** Whether the slide-toggle is required. */\n  async isRequired(): Promise<boolean> {\n    const ariaRequired = await (await this._nativeElement()).getAttribute('aria-required');\n    return ariaRequired === 'true';\n  }\n\n  /** Whether the slide-toggle is valid. */\n  async isValid(): Promise<boolean> {\n    const invalid = (await this.host()).hasClass('ng-invalid');\n    return !(await invalid);\n  }\n\n  /** Gets the slide-toggle's name. */\n  async getName(): Promise<string | null> {\n    return (await this._nativeElement()).getAttribute('name');\n  }\n\n  /** Gets the slide-toggle's aria-label. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this._nativeElement()).getAttribute('aria-label');\n  }\n\n  /** Gets the slide-toggle's aria-labelledby. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this._nativeElement()).getAttribute('aria-labelledby');\n  }\n\n  /** Gets the slide-toggle's label text. */\n  async getLabelText(): Promise<string> {\n    return (await this._label()).text();\n  }\n\n  /** Focuses the slide-toggle. */\n  async focus(): Promise<void> {\n    return (await this._nativeElement()).focus();\n  }\n\n  /** Blurs the slide-toggle. */\n  async blur(): Promise<void> {\n    return (await this._nativeElement()).blur();\n  }\n\n  /** Whether the slide-toggle is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this._nativeElement()).isFocused();\n  }\n\n  /**\n   * Puts the slide-toggle in a checked state by toggling it if it is currently unchecked, or doing\n   * nothing if it is already checked.\n   */\n  async check(): Promise<void> {\n    if (!(await this.isChecked())) {\n      await this.toggle();\n    }\n  }\n\n  /**\n   * Puts the slide-toggle in an unchecked state by toggling it if it is currently checked, or doing\n   * nothing if it is already unchecked.\n   */\n  async uncheck(): Promise<void> {\n    if (await this.isChecked()) {\n      await this.toggle();\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,qBAAsB,SAAQ,gBAAgB,CAAA;AAA3D,IAAA,WAAA,GAAA;;AACU,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAC1C,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;KA2H5C;aA1HQ,IAAY,CAAA,YAAA,GAAG,uBAAH,CAA2B,EAAA;AAE9C;;;;;;AAMG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAqC,EAAE,EAAA;AAEvC,QAAA,QACE,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;aAChC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,CAC9D;;;;aAIA,SAAS,CACR,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,CAC5D;aACA,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,KAAK,OAAO,CACnE;aACA,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,KAAK,QAAQ,CACtE,EACH;KACH;;AAGD,IAAA,MAAM,MAAM,GAAA;QACV,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;KAC9C;;AAGD,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC;AAC3E,QAAA,OAAO,qBAAqB,CAAC,MAAM,OAAO,CAAC,CAAC;KAC7C;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;AACxE,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC,CAAC;KAC9C;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC,CAAC;QACvF,OAAO,YAAY,KAAK,MAAM,CAAC;KAChC;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;AAC3D,QAAA,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC;KACzB;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;KAC3D;;AAGD,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;KACjE;;AAGD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC,CAAC;KACtE;;AAGD,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC;KACrC;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,EAAE,CAAC;KAC9C;;AAGD,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC;KAC7C;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE,SAAS,EAAE,CAAC;KAClD;AAED;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,IAAI,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;AAED;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE;AAC1B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;SACrB;KACF;;;;;"}