{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/badge/testing/badge-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatBadgePosition, MatBadgeSize} from '@angular/material/badge';\nimport {BadgeHarnessFilters} from './badge-harness-filters';\n\n/** Harness for interacting with a standard Material badge in tests. */\nexport class MatBadgeHarness extends ComponentHarness {\n  static hostSelector = '.mat-badge';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a badge with specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `text` finds a badge host with a particular text.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: BadgeHarnessFilters = {}): HarnessPredicate<MatBadgeHarness> {\n    return new HarnessPredicate(MatBadgeHarness, options).addOption(\n      'text',\n      options.text,\n      (harness, text) => HarnessPredicate.stringMatches(harness.getText(), text),\n    );\n  }\n\n  private _badgeElement = this.locatorFor('.mat-badge-content');\n\n  /** Gets a promise for the badge text. */\n  async getText(): Promise<string> {\n    return (await this._badgeElement()).text();\n  }\n\n  /** Gets whether the badge is overlapping the content. */\n  async isOverlapping(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-badge-overlap');\n  }\n\n  /** Gets the position of the badge. */\n  async getPosition(): Promise<MatBadgePosition> {\n    const host = await this.host();\n    let result = '';\n\n    if (await host.hasClass('mat-badge-above')) {\n      result += 'above';\n    } else if (await host.hasClass('mat-badge-below')) {\n      result += 'below';\n    }\n\n    if (await host.hasClass('mat-badge-before')) {\n      result += ' before';\n    } else if (await host.hasClass('mat-badge-after')) {\n      result += ' after';\n    }\n\n    return result.trim() as MatBadgePosition;\n  }\n\n  /** Gets the size of the badge. */\n  async getSize(): Promise<MatBadgeSize> {\n    const host = await this.host();\n\n    if (await host.hasClass('mat-badge-small')) {\n      return 'small';\n    } else if (await host.hasClass('mat-badge-large')) {\n      return 'large';\n    }\n\n    return 'medium';\n  }\n\n  /** Gets whether the badge is hidden. */\n  async isHidden(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-badge-hidden');\n  }\n\n  /** Gets whether the badge is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-badge-disabled');\n  }\n}\n"], "names": [], "mappings": ";;AAYA;AACM,MAAO,eAAgB,SAAQ,gBAAgB,CAAA;AAArD,IAAA,WAAA,GAAA;;AAiBU,QAAA,IAAA,CAAA,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;KAsD/D;aAtEQ,IAAY,CAAA,YAAA,GAAG,YAAH,CAAgB,EAAA;AAEnC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA+B,EAAE,EAAA;AAC3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,SAAS,CAC7D,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAC3E,CAAC;KACH;;AAKD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC;KAC5C;;AAGD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,mBAAmB,CAAC,CAAC;KAC1D;;AAGD,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YAC1C,MAAM,IAAI,OAAO,CAAC;SACnB;aAAM,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACjD,MAAM,IAAI,OAAO,CAAC;SACnB;QAED,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAC3C,MAAM,IAAI,SAAS,CAAC;SACrB;aAAM,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACjD,MAAM,IAAI,QAAQ,CAAC;SACpB;AAED,QAAA,OAAO,MAAM,CAAC,IAAI,EAAsB,CAAC;KAC1C;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AAC1C,YAAA,OAAO,OAAO,CAAC;SAChB;aAAM,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;AACjD,YAAA,OAAO,OAAO,CAAC;SAChB;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;;AAGD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAC;KACzD;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,oBAAoB,CAAC,CAAC;KAC3D;;;;;"}