{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/tooltip/testing/tooltip-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {TooltipHarnessFilters} from './tooltip-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a mat-tooltip in tests. */\nexport class MatTooltipHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-tooltip-trigger';\n\n  private _optionalPanel = this.documentRootLocatorFactory().locatorForOptional('.mat-mdc-tooltip');\n  private _hiddenClass = 'mat-mdc-tooltip-hide';\n  private _disabledClass = 'mat-mdc-tooltip-disabled';\n  private _showAnimationName = 'mat-mdc-tooltip-show';\n  private _hideAnimationName = 'mat-mdc-tooltip-hide';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a tooltip trigger with specific\n   * attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatTooltipHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: TooltipHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Shows the tooltip. */\n  async show(): Promise<void> {\n    const host = await this.host();\n\n    // We need to dispatch both `touchstart` and a hover event, because the tooltip binds\n    // different events depending on the device. The `changedTouches` is there in case the\n    // element has ripples.\n    await host.dispatchEvent('touchstart', {changedTouches: []});\n    await host.hover();\n    const panel = await this._optionalPanel();\n    await panel?.dispatchEvent('animationend', {animationName: this._showAnimationName});\n  }\n\n  /** Hides the tooltip. */\n  async hide(): Promise<void> {\n    const host = await this.host();\n\n    // We need to dispatch both `touchstart` and a hover event, because\n    // the tooltip binds different events depending on the device.\n    await host.dispatchEvent('touchend');\n    await host.mouseAway();\n    const panel = await this._optionalPanel();\n    await panel?.dispatchEvent('animationend', {animationName: this._hideAnimationName});\n  }\n\n  /** Gets whether the tooltip is open. */\n  async isOpen(): Promise<boolean> {\n    const panel = await this._optionalPanel();\n    return !!panel && !(await panel.hasClass(this._hiddenClass));\n  }\n\n  /** Gets whether the tooltip is disabled */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    return host.hasClass(this._disabledClass);\n  }\n\n  /** Gets a promise for the tooltip panel's text. */\n  async getTooltipText(): Promise<string> {\n    const panel = await this._optionalPanel();\n    return panel ? panel.text() : '';\n  }\n}\n"], "names": [], "mappings": ";;AAeA;AACM,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;AAAvD,IAAA,WAAA,GAAA;;QAGU,IAAc,CAAA,cAAA,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QAC1F,IAAY,CAAA,YAAA,GAAG,sBAAsB,CAAC;QACtC,IAAc,CAAA,cAAA,GAAG,0BAA0B,CAAC;QAC5C,IAAkB,CAAA,kBAAA,GAAG,sBAAsB,CAAC;QAC5C,IAAkB,CAAA,kBAAA,GAAG,sBAAsB,CAAC;KAyDrD;aA/DQ,IAAY,CAAA,YAAA,GAAG,0BAAH,CAA8B,EAAA;AAQjD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAiC,EAAE,EAAA;AAEnC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGD,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;;;;AAK/B,QAAA,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,EAAC,cAAc,EAAE,EAAE,EAAC,CAAC,CAAC;AAC7D,QAAA,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC1C,QAAA,MAAM,KAAK,EAAE,aAAa,CAAC,cAAc,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAC,CAAC,CAAC;KACtF;;AAGD,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;;;AAI/B,QAAA,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACrC,QAAA,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC1C,QAAA,MAAM,KAAK,EAAE,aAAa,CAAC,cAAc,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,kBAAkB,EAAC,CAAC,CAAC;KACtF;;AAGD,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC1C,QAAA,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;KAC9D;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;KAC3C;;AAGD,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;AAC1C,QAAA,OAAO,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;KAClC;;;;;"}