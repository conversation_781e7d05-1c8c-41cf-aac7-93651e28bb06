{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../src/material/radio/testing/radio-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {RadioButtonHarnessFilters, RadioGroupHarnessFilters} from './radio-harness-filters';\n\n/** Harness for interacting with an MDC-based mat-radio-group in tests. */\nexport class MatRadioGroupHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatRadioGroup` instance. */\n  static hostSelector = '.mat-mdc-radio-group';\n\n  private _buttonClass = MatRadioButtonHarness;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a radio group with specific\n   * attributes.\n   * @param options Options for filtering which radio group instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatRadioGroupHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: RadioGroupHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options).addOption(\n      'name',\n      options.name,\n      MatRadioGroupHarness._checkRadioGroupName,\n    );\n  }\n\n  /** Gets the name of the radio-group. */\n  async getName(): Promise<string | null> {\n    const hostName = await this._getGroupNameFromHost();\n    // It's not possible to always determine the \"name\" of a radio-group by reading\n    // the attribute. This is because the radio-group does not set the \"name\" as an\n    // element attribute if the \"name\" value is set through a binding.\n    if (hostName !== null) {\n      return hostName;\n    }\n    // In case we couldn't determine the \"name\" of a radio-group by reading the\n    // \"name\" attribute, we try to determine the \"name\" of the group by going\n    // through all radio buttons.\n    const radioNames = await this._getNamesFromRadioButtons();\n    if (!radioNames.length) {\n      return null;\n    }\n    if (!this._checkRadioNamesInGroupEqual(radioNames)) {\n      throw Error('Radio buttons in radio-group have mismatching names.');\n    }\n    return radioNames[0]!;\n  }\n\n  /** Gets the id of the radio-group. */\n  async getId(): Promise<string | null> {\n    return (await this.host()).getProperty<string | null>('id');\n  }\n\n  /** Gets the checked radio-button in a radio-group. */\n  async getCheckedRadioButton(): Promise<MatRadioButtonHarness | null> {\n    for (let radioButton of await this.getRadioButtons()) {\n      if (await radioButton.isChecked()) {\n        return radioButton;\n      }\n    }\n    return null;\n  }\n\n  /** Gets the checked value of the radio-group. */\n  async getCheckedValue(): Promise<string | null> {\n    const checkedRadio = await this.getCheckedRadioButton();\n    if (!checkedRadio) {\n      return null;\n    }\n    return checkedRadio.getValue();\n  }\n\n  /**\n   * Gets a list of radio buttons which are part of the radio-group.\n   * @param filter Optionally filters which radio buttons are included.\n   */\n  async getRadioButtons(filter?: RadioButtonHarnessFilters): Promise<MatRadioButtonHarness[]> {\n    return this.locatorForAll(this._buttonClass.with(filter))();\n  }\n\n  /**\n   * Checks a radio button in this group.\n   * @param filter An optional filter to apply to the child radio buttons. The first tab matching\n   *     the filter will be selected.\n   */\n  async checkRadioButton(filter?: RadioButtonHarnessFilters): Promise<void> {\n    const radioButtons = await this.getRadioButtons(filter);\n    if (!radioButtons.length) {\n      throw Error(`Could not find radio button matching ${JSON.stringify(filter)}`);\n    }\n    return radioButtons[0].check();\n  }\n\n  /** Gets the name attribute of the host element. */\n  private async _getGroupNameFromHost() {\n    return (await this.host()).getAttribute('name');\n  }\n\n  /** Gets a list of the name attributes of all child radio buttons. */\n  private async _getNamesFromRadioButtons(): Promise<string[]> {\n    const groupNames: string[] = [];\n    for (let radio of await this.getRadioButtons()) {\n      const radioName = await radio.getName();\n      if (radioName !== null) {\n        groupNames.push(radioName);\n      }\n    }\n    return groupNames;\n  }\n\n  /** Checks if the specified radio names are all equal. */\n  private _checkRadioNamesInGroupEqual(radioNames: string[]): boolean {\n    let groupName: string | null = null;\n    for (let radioName of radioNames) {\n      if (groupName === null) {\n        groupName = radioName;\n      } else if (groupName !== radioName) {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Checks if a radio-group harness has the given name. Throws if a radio-group with\n   * matching name could be found but has mismatching radio-button names.\n   */\n  protected static async _checkRadioGroupName(harness: MatRadioGroupHarness, name: string) {\n    // Check if there is a radio-group which has the \"name\" attribute set\n    // to the expected group name. It's not possible to always determine\n    // the \"name\" of a radio-group by reading the attribute. This is because\n    // the radio-group does not set the \"name\" as an element attribute if the\n    // \"name\" value is set through a binding.\n    if ((await harness._getGroupNameFromHost()) === name) {\n      return true;\n    }\n    // Check if there is a group with radio-buttons that all have the same\n    // expected name. This implies that the group has the given name. It's\n    // not possible to always determine the name of a radio-group through\n    // the attribute because there is\n    const radioNames = await harness._getNamesFromRadioButtons();\n    if (radioNames.indexOf(name) === -1) {\n      return false;\n    }\n    if (!harness._checkRadioNamesInGroupEqual(radioNames)) {\n      throw Error(\n        `The locator found a radio-group with name \"${name}\", but some ` +\n          `radio-button's within the group have mismatching names, which is invalid.`,\n      );\n    }\n    return true;\n  }\n}\n\n/** Harness for interacting with an MDC-based mat-radio-button in tests. */\nexport class MatRadioButtonHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatRadioButton` instance. */\n  static hostSelector = '.mat-mdc-radio-button';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a radio button with specific\n   * attributes.\n   * @param options Options for filtering which radio button instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatRadioButtonHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: RadioButtonHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('label', options.label, (harness, label) =>\n        HarnessPredicate.stringMatches(harness.getLabelText(), label),\n      )\n      .addOption('name', options.name, async (harness, name) => (await harness.getName()) === name)\n      .addOption(\n        'checked',\n        options.checked,\n        async (harness, checked) => (await harness.isChecked()) == checked,\n      );\n  }\n\n  protected _textLabel = this.locatorFor('label');\n  protected _clickLabel = this._textLabel;\n  private _input = this.locatorFor('input');\n\n  /** Whether the radio-button is checked. */\n  async isChecked(): Promise<boolean> {\n    const checked = (await this._input()).getProperty<boolean>('checked');\n    return coerceBooleanProperty(await checked);\n  }\n\n  /** Whether the radio-button is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const disabled = (await this._input()).getAttribute('disabled');\n    return coerceBooleanProperty(await disabled);\n  }\n\n  /** Whether the radio-button is required. */\n  async isRequired(): Promise<boolean> {\n    const required = (await this._input()).getAttribute('required');\n    return coerceBooleanProperty(await required);\n  }\n\n  /** Gets the radio-button's name. */\n  async getName(): Promise<string | null> {\n    return (await this._input()).getAttribute('name');\n  }\n\n  /** Gets the radio-button's id. */\n  async getId(): Promise<string | null> {\n    return (await this.host()).getProperty<string>('id');\n  }\n\n  /**\n   * Gets the value of the radio-button. The radio-button value will be converted to a string.\n   *\n   * Note: This means that for radio-button's with an object as a value `[object Object]` is\n   * intentionally returned.\n   */\n  async getValue(): Promise<string | null> {\n    return (await this._input()).getProperty('value');\n  }\n\n  /** Gets the radio-button's label text. */\n  async getLabelText(): Promise<string> {\n    return (await this._textLabel()).text();\n  }\n\n  /** Focuses the radio-button. */\n  async focus(): Promise<void> {\n    return (await this._input()).focus();\n  }\n\n  /** Blurs the radio-button. */\n  async blur(): Promise<void> {\n    return (await this._input()).blur();\n  }\n\n  /** Whether the radio-button is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this._input()).isFocused();\n  }\n\n  /**\n   * Puts the radio-button in a checked state by clicking it if it is currently unchecked,\n   * or doing nothing if it is already checked.\n   */\n  async check(): Promise<void> {\n    if (!(await this.isChecked())) {\n      return (await this._clickLabel()).click();\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AAA1D,IAAA,WAAA,GAAA;;QAIU,IAAY,CAAA,YAAA,GAAG,qBAAqB,CAAC;KAiJ9C;;aAnJQ,IAAY,CAAA,YAAA,GAAG,sBAAH,CAA0B,EAAA;AAI7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAoC,EAAE,EAAA;QAEtC,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,SAAS,CAClD,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,oBAAoB,CAAC,oBAAoB,CAC1C,CAAC;KACH;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;;;;AAIpD,QAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;AACrB,YAAA,OAAO,QAAQ,CAAC;SACjB;;;;AAID,QAAA,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;AAC1D,QAAA,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AACtB,YAAA,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,EAAE;AAClD,YAAA,MAAM,KAAK,CAAC,sDAAsD,CAAC,CAAC;SACrE;AACD,QAAA,OAAO,UAAU,CAAC,CAAC,CAAE,CAAC;KACvB;;AAGD,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAgB,IAAI,CAAC,CAAC;KAC7D;;AAGD,IAAA,MAAM,qBAAqB,GAAA;QACzB,KAAK,IAAI,WAAW,IAAI,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE;AACpD,YAAA,IAAI,MAAM,WAAW,CAAC,SAAS,EAAE,EAAE;AACjC,gBAAA,OAAO,WAAW,CAAC;aACpB;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAGD,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACxD,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,OAAO,YAAY,CAAC,QAAQ,EAAE,CAAC;KAChC;AAED;;;AAGG;IACH,MAAM,eAAe,CAAC,MAAkC,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;KAC7D;AAED;;;;AAIG;IACH,MAAM,gBAAgB,CAAC,MAAkC,EAAA;QACvD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACxB,MAAM,KAAK,CAAC,CAAA,qCAAA,EAAwC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA,CAAC,CAAC;SAC/E;AACD,QAAA,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;KAChC;;AAGO,IAAA,MAAM,qBAAqB,GAAA;AACjC,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;KACjD;;AAGO,IAAA,MAAM,yBAAyB,GAAA;QACrC,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,KAAK,IAAI,KAAK,IAAI,MAAM,IAAI,CAAC,eAAe,EAAE,EAAE;AAC9C,YAAA,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;AACxC,YAAA,IAAI,SAAS,KAAK,IAAI,EAAE;AACtB,gBAAA,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC5B;SACF;AACD,QAAA,OAAO,UAAU,CAAC;KACnB;;AAGO,IAAA,4BAA4B,CAAC,UAAoB,EAAA;QACvD,IAAI,SAAS,GAAkB,IAAI,CAAC;AACpC,QAAA,KAAK,IAAI,SAAS,IAAI,UAAU,EAAE;AAChC,YAAA,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,SAAS,GAAG,SAAS,CAAC;aACvB;AAAM,iBAAA,IAAI,SAAS,KAAK,SAAS,EAAE;AAClC,gBAAA,OAAO,KAAK,CAAC;aACd;SACF;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;AAGG;AACO,IAAA,aAAa,oBAAoB,CAAC,OAA6B,EAAE,IAAY,EAAA;;;;;;QAMrF,IAAI,CAAC,MAAM,OAAO,CAAC,qBAAqB,EAAE,MAAM,IAAI,EAAE;AACpD,YAAA,OAAO,IAAI,CAAC;SACb;;;;;AAKD,QAAA,MAAM,UAAU,GAAG,MAAM,OAAO,CAAC,yBAAyB,EAAE,CAAC;QAC7D,IAAI,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AACnC,YAAA,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAAC,UAAU,CAAC,EAAE;AACrD,YAAA,MAAM,KAAK,CACT,CAA8C,2CAAA,EAAA,IAAI,CAAc,YAAA,CAAA;AAC9D,gBAAA,CAAA,yEAAA,CAA2E,CAC9E,CAAC;SACH;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAGH;AACM,MAAO,qBAAsB,SAAQ,gBAAgB,CAAA;AAA3D,IAAA,WAAA,GAAA;;AA0BY,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACtC,QAAA,IAAA,CAAA,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC;AAChC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KAqE3C;;aA/FQ,IAAY,CAAA,YAAA,GAAG,uBAAH,CAA2B,EAAA;AAE9C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAqC,EAAE,EAAA;AAEvC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;aACvC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC,CAC9D;aACA,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;aAC5F,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,KAAK,OAAO,CACnE,CAAC;KACL;;AAOD,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAU,SAAS,CAAC,CAAC;AACtE,QAAA,OAAO,qBAAqB,CAAC,MAAM,OAAO,CAAC,CAAC;KAC7C;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;AAChE,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC,CAAC;KAC9C;;AAGD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC;AAChE,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC,CAAC;KAC9C;;AAGD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;KACnD;;AAGD,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,IAAI,CAAC,CAAC;KACtD;AAED;;;;;AAKG;AACH,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;KACnD;;AAGD,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC;KACzC;;AAGD,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;KACtC;;AAGD,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC;KACrC;;AAGD,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC;KAC1C;AAED;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,IAAI,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;YAC7B,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC;SAC3C;KACF;;;;;"}