{"version": 3, "file": "toolbar.mjs", "sources": ["../../../../../../src/material/toolbar/toolbar.ts", "../../../../../../src/material/toolbar/toolbar.html", "../../../../../../src/material/toolbar/toolbar-module.ts", "../../../../../../src/material/toolbar/toolbar_public_index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Platform} from '@angular/cdk/platform';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  Component,\n  ContentChildren,\n  Directive,\n  ElementRef,\n  Inject,\n  Input,\n  QueryList,\n  ViewEncapsulation,\n} from '@angular/core';\n\n@Directive({\n  selector: 'mat-toolbar-row',\n  exportAs: 'matToolbarRow',\n  host: {'class': 'mat-toolbar-row'},\n  standalone: true,\n})\nexport class MatToolbarRow {}\n\n@Component({\n  selector: 'mat-toolbar',\n  exportAs: 'matToolbar',\n  templateUrl: 'toolbar.html',\n  styleUrl: 'toolbar.css',\n  host: {\n    'class': 'mat-toolbar',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n    '[class.mat-toolbar-multiple-rows]': '_toolbarRows.length > 0',\n    '[class.mat-toolbar-single-row]': '_toolbarRows.length === 0',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  standalone: true,\n})\nexport class MatToolbar implements AfterViewInit {\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /** Palette color of the toolbar. */\n  @Input() color?: string | null;\n\n  private _document: Document;\n\n  /** Reference to all toolbar row elements that have been projected. */\n  @ContentChildren(MatToolbarRow, {descendants: true}) _toolbarRows: QueryList<MatToolbarRow>;\n\n  constructor(\n    protected _elementRef: ElementRef,\n    private _platform: Platform,\n    @Inject(DOCUMENT) document?: any,\n  ) {\n    // TODO: make the document a required param when doing breaking changes.\n    this._document = document;\n  }\n\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._checkToolbarMixedModes();\n      this._toolbarRows.changes.subscribe(() => this._checkToolbarMixedModes());\n    }\n  }\n\n  /**\n   * Throws an exception when developers are attempting to combine the different toolbar row modes.\n   */\n  private _checkToolbarMixedModes() {\n    if (this._toolbarRows.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      // Check if there are any other DOM nodes that can display content but aren't inside of\n      // a <mat-toolbar-row> element.\n      const isCombinedUsage = Array.from<HTMLElement>(this._elementRef.nativeElement.childNodes)\n        .filter(node => !(node.classList && node.classList.contains('mat-toolbar-row')))\n        .filter(node => node.nodeType !== (this._document ? this._document.COMMENT_NODE : 8))\n        .some(node => !!(node.textContent && node.textContent.trim()));\n\n      if (isCombinedUsage) {\n        throwToolbarMixedModesError();\n      }\n    }\n  }\n}\n\n/**\n * Throws an exception when attempting to combine the different toolbar row modes.\n * @docs-private\n */\nexport function throwToolbarMixedModesError() {\n  throw Error(\n    'MatToolbar: Attempting to combine different toolbar modes. ' +\n      'Either specify multiple `<mat-toolbar-row>` elements explicitly or just place content ' +\n      'inside of a `<mat-toolbar>` for a single row.',\n  );\n}\n", "<ng-content></ng-content>\n<ng-content select=\"mat-toolbar-row\"></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '@angular/material/core';\nimport {<PERSON><PERSON>oolbar, MatToolbarRow} from './toolbar';\n\n@NgModule({\n  imports: [MatCommonModule, MatToolbar, MatToolbarRow],\n  exports: [MatToolbar, MatToolbarRow, MatCommonModule],\n})\nexport class MatToolbarModule {}\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;MA6Ba,aAAa,CAAA;8GAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,eAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA,CAAA,EAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBANzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,QAAQ,EAAE,eAAe;AACzB,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,iBAAiB,EAAC;AAClC,oBAAA,UAAU,EAAE,IAAI;AACjB,iBAAA,CAAA;;MAkBY,UAAU,CAAA;AAUrB,IAAA,WAAA,CACY,WAAuB,EACzB,SAAmB,EACT,QAAc,EAAA;QAFtB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAY;QACzB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAU;;AAI3B,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;KAC3B;IAED,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,uBAAuB,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;SAC3E;KACF;AAED;;AAEG;IACK,uBAAuB,GAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;;;AAG/E,YAAA,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAc,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC;iBACvF,MAAM,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;iBAC/E,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;iBACpF,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAEjE,IAAI,eAAe,EAAE;AACnB,gBAAA,2BAA2B,EAAE,CAAC;aAC/B;SACF;KACF;AA1CU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,UAAU,oEAaX,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA,EAAA;kGAbP,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,iCAAA,EAAA,yBAAA,EAAA,8BAAA,EAAA,2BAAA,EAAA,EAAA,cAAA,EAAA,aAAA,EAAA,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,SAAA,EAQJ,aAAa,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECtDhC,mFAEA,EAAA,MAAA,EAAA,CAAA,0sDAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA;;2FD4Ca,UAAU,EAAA,UAAA,EAAA,CAAA;kBAftB,SAAS;+BACE,aAAa,EAAA,QAAA,EACb,YAAY,EAGhB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,aAAa;AACtB,wBAAA,SAAS,EAAE,6BAA6B;AACxC,wBAAA,mCAAmC,EAAE,yBAAyB;AAC9D,wBAAA,gCAAgC,EAAE,2BAA2B;qBAC9D,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,cACzB,IAAI,EAAA,QAAA,EAAA,mFAAA,EAAA,MAAA,EAAA,CAAA,0sDAAA,CAAA,EAAA,CAAA;;0BAeb,MAAM;2BAAC,QAAQ,CAAA;yCAVT,KAAK,EAAA,CAAA;sBAAb,KAAK;gBAK+C,YAAY,EAAA,CAAA;sBAAhE,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,aAAa,EAAE,EAAC,WAAW,EAAE,IAAI,EAAC,CAAA;;AAqCrD;;;AAGG;SACa,2BAA2B,GAAA;IACzC,MAAM,KAAK,CACT,6DAA6D;QAC3D,wFAAwF;AACxF,QAAA,+CAA+C,CAClD,CAAC;AACJ;;MErFa,gBAAgB,CAAA;8GAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;+GAAhB,gBAAgB,EAAA,OAAA,EAAA,CAHjB,eAAe,EAAE,UAAU,EAAE,aAAa,CAAA,EAAA,OAAA,EAAA,CAC1C,UAAU,EAAE,aAAa,EAAE,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;+GAEzC,gBAAgB,EAAA,OAAA,EAAA,CAHjB,eAAe,EACY,eAAe,CAAA,EAAA,CAAA,CAAA,EAAA;;2FAEzC,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAJ5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,aAAa,CAAC;AACrD,oBAAA,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,eAAe,CAAC;AACtD,iBAAA,CAAA;;;ACfD;;AAEG;;;;"}