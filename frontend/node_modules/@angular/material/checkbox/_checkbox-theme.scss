@use '@material/checkbox/checkbox-theme' as mdc-checkbox-theme;
@use '@material/form-field/form-field-theme' as mdc-form-field-theme;
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/checkbox' as tokens-mdc-checkbox;
@use '../core/tokens/m2/mdc/form-field' as tokens-mdc-form-field;
@use '../core/tokens/m2/mat/checkbox' as tokens-mat-checkbox;

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-checkbox.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-checkbox-theme.theme(tokens-mdc-checkbox.get-unthemable-tokens());
      @include token-utils.create-token-values(
        tokens-mat-checkbox.$prefix, tokens-mat-checkbox.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-checkbox.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the checkbox: primary, secondary, tertiary, or
///     error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-checkbox-theme.theme(tokens-mdc-checkbox.get-color-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-checkbox.$prefix, tokens-mat-checkbox.get-color-tokens($theme));
    }

    .mat-mdc-checkbox {
      @include mdc-form-field-theme.theme(tokens-mdc-form-field.get-color-tokens($theme));

      &.mat-primary {
        @include mdc-checkbox-theme.theme(tokens-mdc-checkbox.get-color-tokens($theme, primary));
      }

      &.mat-warn {
        @include mdc-checkbox-theme.theme(tokens-mdc-checkbox.get-color-tokens($theme, warn));
      }
    }
  }
}

/// Outputs typography theme styles for the mat-checkbox.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-checkbox-theme.theme(tokens-mdc-checkbox.get-typography-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-checkbox.$prefix, tokens-mat-checkbox.get-typography-tokens($theme));
    }

    .mat-mdc-checkbox {
      @include mdc-form-field-theme.theme(tokens-mdc-form-field.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-checkbox.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  $density-scale: inspection.get-theme-density($theme);

  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      @include mdc-checkbox-theme.theme(tokens-mdc-checkbox.get-density-tokens($theme));
      @include token-utils.create-token-values(
        tokens-mat-checkbox.$prefix, tokens-mat-checkbox.get-density-tokens($theme));
    }
  }
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-checkbox.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the checkbox: primary, secondary, tertiary, or
///     error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-checkbox') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  $mdc-checkbox-tokens: token-utils.get-tokens-for(
      $tokens, tokens-mdc-checkbox.$prefix, $options...);
  // Don't pass $options here, since the mdc-form-field doesn't support color options,
  // only the mdc-checkbox does.
  $mdc-form-field-tokens: token-utils.get-tokens-for($tokens, tokens-mdc-form-field.$prefix);
  $mat-checkbox-tokens: token-utils.get-tokens-for($tokens, tokens-mat-checkbox.$prefix);
  @include mdc-checkbox-theme.theme($mdc-checkbox-tokens);
  @include mdc-form-field-theme.theme($mdc-form-field-tokens);
  @include token-utils.create-token-values(tokens-mat-checkbox.$prefix, $mat-checkbox-tokens);
}
