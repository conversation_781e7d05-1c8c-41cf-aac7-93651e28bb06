@use '@material/switch/switch-theme' as mdc-switch-theme;
@use '@material/form-field/form-field-theme' as mdc-form-field-theme;
@use '../core/style/sass-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/theming/validation';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use '../core/tokens/m2/mdc/form-field' as tokens-mdc-form-field;
@use '../core/tokens/m2/mat/switch' as tokens-mat-switch;
@use '../core/tokens/m2/mdc/switch' as tokens-mdc-switch;

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, base));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      $mat-tokens: tokens-mat-switch.get-unthemable-tokens();
      $mdc-tokens: tokens-mdc-switch.get-unthemable-tokens();
      @include mdc-switch-theme.theme($mdc-tokens);
      @include token-utils.create-token-values(tokens-mat-switch.$prefix, $mat-tokens);
    }
  }
}

/// Outputs color theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the slide-toggle: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin color($theme, $options...) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, color), $options...);
  }
  @else {
    $is-dark: inspection.get-theme-type($theme) == dark;
    $mat-tokens: tokens-mat-switch.get-color-tokens($theme);
    $mdc-tokens: tokens-mdc-switch.get-color-tokens($theme);

    // Add values for MDC slide toggles tokens
    @include sass-utils.current-selector-or-root() {
      @include mdc-switch-theme.theme($mdc-tokens);
      @include token-utils.create-token-values(tokens-mat-switch.$prefix, $mat-tokens);

      // TODO(wagnermaciel): Use our token system to define this css variable.
    --mdc-switch-disabled-label-text-color: #{inspection.get-theme-color(
      $theme,
      foreground,
      disabled-text
    )};

    .mat-mdc-slide-toggle {
        @include mdc-form-field-theme.theme(tokens-mdc-form-field.get-color-tokens($theme));

        // Change the color palette related tokens to accent or warn if applicable
        &.mat-accent {
          @include mdc-switch-theme.theme(
              tokens-mdc-switch.private-get-color-palette-color-tokens($theme, accent));
        }

        &.mat-warn {
          @include mdc-switch-theme.theme(
              tokens-mdc-switch.private-get-color-palette-color-tokens($theme, warn));
        }
      }
    }
  }
}

/// Outputs typography theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, typography));
  }
  @else {
    $mat-tokens: tokens-mat-switch.get-typography-tokens($theme);
    $mdc-tokens: tokens-mdc-switch.get-typography-tokens($theme);

    // Add values for MDC slide toggle tokens
    @include sass-utils.current-selector-or-root() {
      @include mdc-switch-theme.theme($mdc-tokens);
      @include token-utils.create-token-values(tokens-mat-switch.$prefix, $mat-tokens);

      .mat-mdc-slide-toggle {
        @include mdc-form-field-theme.theme(tokens-mdc-form-field.get-typography-tokens($theme));
      }
    }
  }
}

/// Outputs density theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include _theme-from-tokens(inspection.get-theme-tokens($theme, density));
  }
  @else {
    @include sass-utils.current-selector-or-root() {
      $mat-tokens: tokens-mat-switch.get-density-tokens($theme);
      $mdc-tokens: tokens-mdc-switch.get-density-tokens($theme);
      @include mdc-switch-theme.theme(tokens-mdc-switch.get-density-tokens($theme));
    }
  }
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate styles for.
/// @param {ArgList} Additional optional arguments (only supported for M3 themes):
///   $color-variant: The color variant to use for the slide-toggle: primary, secondary, tertiary,
///      or error (If not specified, default primary color will be used).
@mixin theme($theme, $options...) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-slide-toggle') {
    @if inspection.get-theme-version($theme) == 1 {
      @include _theme-from-tokens(inspection.get-theme-tokens($theme), $options...);
    }
    @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}

@mixin _theme-from-tokens($tokens, $options...) {
  @include validation.selector-defined(
      'Calls to Angular Material theme mixins with an M3 theme must be wrapped in a selector');
  // Don't pass $options here, since the mdc-form-field doesn't support color options,
  // only the mdc-switch does.
  $mdc-form-field-tokens: token-utils.get-tokens-for($tokens, tokens-mdc-form-field.$prefix);
  $mat-switch-tokens: token-utils.get-tokens-for($tokens, tokens-mat-switch.$prefix);
  $mdc-switch-tokens: token-utils.get-tokens-for($tokens, tokens-mdc-switch.$prefix, $options...);

  @include mdc-form-field-theme.theme($mdc-form-field-tokens);
  @include mdc-switch-theme.theme($mdc-switch-tokens);
  @include token-utils.create-token-values(tokens-mat-switch.$prefix, $mat-switch-tokens);
}
