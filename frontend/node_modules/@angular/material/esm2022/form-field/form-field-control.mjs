import { Directive } from '@angular/core';
import * as i0 from "@angular/core";
/** An interface which allows a control to work inside of a `MatFormField`. */
export class MatFormFieldControl {
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "17.2.0", ngImport: i0, type: MatFormFieldControl, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }
    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: "14.0.0", version: "17.2.0", type: MatFormFieldControl, ngImport: i0 }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "17.2.0", ngImport: i0, type: MatFormFieldControl, decorators: [{
            type: Directive
        }] });
//# sourceMappingURL=data:application/json;base64,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