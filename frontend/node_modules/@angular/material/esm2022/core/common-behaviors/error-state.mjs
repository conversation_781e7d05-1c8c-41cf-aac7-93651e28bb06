/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/**
 * Class that tracks the error state of a component.
 * @docs-private
 */
export class _ErrorStateTracker {
    constructor(_defaultMatcher, ngControl, _parentFormGroup, _parentForm, _stateChanges) {
        this._defaultMatcher = _defaultMatcher;
        this.ngControl = ngControl;
        this._parentFormGroup = _parentFormGroup;
        this._parentForm = _parentForm;
        this._stateChanges = _stateChanges;
        /** Whether the tracker is currently in an error state. */
        this.errorState = false;
    }
    /** Updates the error state based on the provided error state matcher. */
    updateErrorState() {
        const oldState = this.errorState;
        const parent = this._parentFormGroup || this._parentForm;
        const matcher = this.matcher || this._defaultMatcher;
        const control = this.ngControl ? this.ngControl.control : null;
        const newState = matcher?.isErrorState(control, parent) ?? false;
        if (newState !== oldState) {
            this.errorState = newState;
            this._stateChanges.next();
        }
    }
}
export function mixinErrorState(base) {
    return class extends base {
        /** Whether the component is in an error state. */
        get errorState() {
            return this._getTracker().errorState;
        }
        set errorState(value) {
            this._getTracker().errorState = value;
        }
        /** An object used to control the error state of the component. */
        get errorStateMatcher() {
            return this._getTracker().matcher;
        }
        set errorStateMatcher(value) {
            this._getTracker().matcher = value;
        }
        /** Updates the error state based on the provided error state matcher. */
        updateErrorState() {
            this._getTracker().updateErrorState();
        }
        _getTracker() {
            if (!this._tracker) {
                this._tracker = new _ErrorStateTracker(this._defaultErrorStateMatcher, this.ngControl, this._parentFormGroup, this._parentForm, this.stateChanges);
            }
            return this._tracker;
        }
        constructor(...args) {
            super(...args);
        }
    };
}
//# sourceMappingURL=data:application/json;base64,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