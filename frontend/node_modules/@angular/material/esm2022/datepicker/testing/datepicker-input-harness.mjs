/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { parallel, TestKey } from '@angular/cdk/testing';
import { MatDatepickerInputHarnessBase, getInputPredicate } from './datepicker-input-harness-base';
import { closeCalendar, getCalendarId, getCalendar, } from './datepicker-trigger-harness-base';
/** Harness for interacting with a standard Material datepicker inputs in tests. */
export class MatDatepickerInputHarness extends MatDatepickerInputHarnessBase {
    static { this.hostSelector = '.mat-datepicker-input'; }
    /**
     * Gets a `HarnessPredicate` that can be used to search for a `MatDatepickerInputHarness`
     * that meets certain criteria.
     * @param options Options for filtering which input instances are considered a match.
     * @return a `HarnessPredicate` configured with the given options.
     */
    static with(options = {}) {
        return getInputPredicate(MatDatepickerInputHarness, options);
    }
    /** Gets whether the calendar associated with the input is open. */
    async isCalendarOpen() {
        // `aria-owns` is set only if there's an open datepicker so we can use it as an indicator.
        const host = await this.host();
        return (await host.getAttribute('aria-owns')) != null;
    }
    /** Opens the calendar associated with the input. */
    async openCalendar() {
        const [isDisabled, hasCalendar] = await parallel(() => [this.isDisabled(), this.hasCalendar()]);
        if (!isDisabled && hasCalendar) {
            // Alt + down arrow is the combination for opening the calendar with the keyboard.
            const host = await this.host();
            return host.sendKeys({ alt: true }, TestKey.DOWN_ARROW);
        }
    }
    /** Closes the calendar associated with the input. */
    async closeCalendar() {
        if (await this.isCalendarOpen()) {
            await closeCalendar(getCalendarId(this.host()), this.documentRootLocatorFactory());
            // This is necessary so that we wait for the closing animation to finish in touch UI mode.
            await this.forceStabilize();
        }
    }
    /** Whether a calendar is associated with the input. */
    async hasCalendar() {
        return (await getCalendarId(this.host())) != null;
    }
    /**
     * Gets the `MatCalendarHarness` that is associated with the trigger.
     * @param filter Optionally filters which calendar is included.
     */
    async getCalendar(filter = {}) {
        return getCalendar(filter, this.host(), this.documentRootLocatorFactory());
    }
}
//# sourceMappingURL=data:application/json;base64,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