/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var MatTestDialogOpener_1;
import { __decorate, __metadata } from "tslib";
import { ChangeDetectionStrategy, Component, NgModule, ViewEncapsulation, } from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
/** Test component that immediately opens a dialog when bootstrapped. */
let MatTestDialogOpener = class MatTestDialogOpener {
    static { MatTestDialogOpener_1 = this; }
    /** Static method that prepares this class to open the provided component. */
    static withComponent(component, config) {
        MatTestDialogOpener_1.component = component;
        MatTestDialogOpener_1.config = config;
        return MatTestDialogOpener_1;
    }
    constructor(dialog) {
        this.dialog = dialog;
        if (!MatTestDialogOpener_1.component) {
            throw new Error(`MatTestDialogOpener does not have a component provided.`);
        }
        this.dialogRef = this.dialog.open(MatTestDialogOpener_1.component, MatTestDialogOpener_1.config || {});
        this._afterClosedSubscription = this.dialogRef.afterClosed().subscribe(result => {
            this.closedResult = result;
        });
    }
    ngOnDestroy() {
        this._afterClosedSubscription.unsubscribe();
        MatTestDialogOpener_1.component = undefined;
        MatTestDialogOpener_1.config = undefined;
    }
};
MatTestDialogOpener = MatTestDialogOpener_1 = __decorate([
    Component({
        selector: 'mat-test-dialog-opener',
        template: '',
        changeDetection: ChangeDetectionStrategy.OnPush,
        encapsulation: ViewEncapsulation.None,
        standalone: true,
    }),
    __metadata("design:paramtypes", [MatDialog])
], MatTestDialogOpener);
export { MatTestDialogOpener };
let MatTestDialogOpenerModule = class MatTestDialogOpenerModule {
};
MatTestDialogOpenerModule = __decorate([
    NgModule({
        imports: [MatDialogModule, NoopAnimationsModule, MatTestDialogOpener],
    })
], MatTestDialogOpenerModule);
export { MatTestDialogOpenerModule };
//# sourceMappingURL=data:application/json;base64,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