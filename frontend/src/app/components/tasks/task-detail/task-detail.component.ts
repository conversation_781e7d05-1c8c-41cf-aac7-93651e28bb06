import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { TaskService, Task, TaskComment, LinkedItem } from '../../../services/task.service';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-task-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatFormFieldModule,
    MatInputModule,
    MatListModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatMenuModule
  ],
  template: `
    <div class="task-detail-container" *ngIf="!loading">
      <!-- Task Header -->
      <div class="task-header">
        <button mat-icon-button (click)="goBack()">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="task-title-section">
          <h1>{{ task?.title }}</h1>
          <mat-chip [class]="'status-' + (task?.status?.toLowerCase().replace(' ', '-') || '')">
            {{ task?.status }}
          </mat-chip>
        </div>
        <div class="task-actions">
          <button mat-raised-button color="primary" [routerLink]="['/tasks', task?.id, 'edit']">
            <mat-icon>edit</mat-icon>
            Edit
          </button>
          <button mat-raised-button color="warn" (click)="deleteTask()" *ngIf="canDeleteTasks">
            <mat-icon>delete</mat-icon>
            Delete
          </button>
        </div>
      </div>

      <!-- Task Details -->
      <div class="task-content">
        <div class="main-content">
          <mat-card class="task-info-card">
            <mat-card-header>
              <mat-card-title>Task Information</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="task-field">
                <strong>Description:</strong>
                <p>{{ task?.description || 'No description provided' }}</p>
              </div>
              <div class="task-meta">
                <div class="meta-item" *ngIf="task?.assignee_name">
                  <mat-icon>person</mat-icon>
                  <span><strong>Assignee:</strong> {{ task?.assignee_name }}</span>
                </div>
                <div class="meta-item" *ngIf="task?.creator_name">
                  <mat-icon>person_outline</mat-icon>
                  <span><strong>Created by:</strong> {{ task?.creator_name }}</span>
                </div>
                <div class="meta-item" *ngIf="task?.due_date">
                  <mat-icon>schedule</mat-icon>
                  <span><strong>Due:</strong> {{ task?.due_date | date:'medium' }}</span>
                </div>
                <div class="meta-item">
                  <mat-icon>access_time</mat-icon>
                  <span><strong>Created:</strong> {{ task?.created_at | date:'medium' }}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Comments Section -->
          <mat-card class="comments-card">
            <mat-card-header>
              <mat-card-title>Comments ({{ task?.comments?.length || 0 }})</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <!-- Add Comment Form -->
              <form [formGroup]="commentForm" (ngSubmit)="addComment()" class="comment-form">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Add a comment</mat-label>
                  <textarea matInput formControlName="content" rows="3" placeholder="Write your comment..."></textarea>
                </mat-form-field>
                <div class="comment-actions">
                  <button mat-raised-button color="primary" type="submit" 
                          [disabled]="commentForm.invalid || addingComment">
                    <mat-spinner diameter="16" *ngIf="addingComment"></mat-spinner>
                    <span *ngIf="!addingComment">Add Comment</span>
                  </button>
                </div>
              </form>

              <!-- Comments List -->
              <div class="comments-list" *ngIf="task?.comments && task?.comments?.length > 0">
                <div class="comment" *ngFor="let comment of task?.comments">
                  <div class="comment-header">
                    <strong>{{ comment.author_name }}</strong>
                    <span class="comment-time">{{ comment.timestamp | date:'medium' }}</span>
                  </div>
                  <p class="comment-content">{{ comment.content }}</p>
                </div>
              </div>

              <div class="no-comments" *ngIf="!task?.comments || task?.comments?.length === 0">
                <p>No comments yet. Be the first to comment!</p>
              </div>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
          <!-- Linked Items -->
          <mat-card class="linked-items-card">
            <mat-card-header>
              <mat-card-title>Linked Items ({{ task?.linkedItems?.length || 0 }})</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <!-- Add Link Form -->
              <form [formGroup]="linkForm" (ngSubmit)="addLinkedItem()" class="link-form">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Title</mat-label>
                  <input matInput formControlName="title" placeholder="Link title">
                </mat-form-field>
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>URL</mat-label>
                  <input matInput formControlName="link" placeholder="https://...">
                </mat-form-field>
                <button mat-raised-button color="primary" type="submit" 
                        [disabled]="linkForm.invalid || addingLink" class="full-width">
                  <mat-spinner diameter="16" *ngIf="addingLink"></mat-spinner>
                  <span *ngIf="!addingLink">Add Link</span>
                </button>
              </form>

              <!-- Links List -->
              <mat-list *ngIf="task?.linkedItems && task?.linkedItems?.length > 0">
                <mat-list-item *ngFor="let item of task?.linkedItems">
                  <mat-icon matListIcon>link</mat-icon>
                  <div matLine>
                    <a [href]="item.link" target="_blank" rel="noopener">{{ item.title }}</a>
                  </div>
                  <div matLine class="link-url">{{ item.link }}</div>
                </mat-list-item>
              </mat-list>

              <div class="no-links" *ngIf="!task?.linkedItems || task?.linkedItems?.length === 0">
                <p>No linked items yet.</p>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div class="loading-container" *ngIf="loading">
      <mat-spinner></mat-spinner>
      <p>Loading task details...</p>
    </div>
  `,
  styles: [`
    .task-detail-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .task-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e0e0e0;
    }

    .task-title-section {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .task-title-section h1 {
      margin: 0;
      font-size: 1.8rem;
    }

    .task-actions {
      display: flex;
      gap: 8px;
    }

    .task-content {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 24px;
    }

    .main-content {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .task-field {
      margin-bottom: 16px;
    }

    .task-field p {
      margin: 8px 0 0 0;
      color: #666;
    }

    .task-meta {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .meta-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }

    .meta-item mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      color: #666;
    }

    .comment-form {
      margin-bottom: 24px;
    }

    .comment-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 8px;
    }

    .comments-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .comment {
      padding: 16px;
      background-color: #f5f5f5;
      border-radius: 8px;
    }

    .comment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .comment-time {
      font-size: 12px;
      color: #666;
    }

    .comment-content {
      margin: 0;
      line-height: 1.5;
    }

    .link-form {
      margin-bottom: 16px;
    }

    .link-url {
      font-size: 12px;
      color: #666;
      word-break: break-all;
    }

    .no-comments, .no-links {
      text-align: center;
      color: #666;
      font-style: italic;
    }

    .full-width {
      width: 100%;
    }

    .loading-container {
      text-align: center;
      padding: 48px;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    .status-new { background-color: #e3f2fd; color: #1976d2; }
    .status-todo { background-color: #f3e5f5; color: #7b1fa2; }
    .status-in-progress { background-color: #fff3e0; color: #f57c00; }
    .status-blocked { background-color: #ffebee; color: #d32f2f; }
    .status-closed { background-color: #e8f5e8; color: #388e3c; }

    @media (max-width: 768px) {
      .task-content {
        grid-template-columns: 1fr;
      }
      
      .task-header {
        flex-direction: column;
        align-items: flex-start;
      }
      
      .task-title-section {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  `]
})
export class TaskDetailComponent implements OnInit {
  task: Task | null = null;
  loading = true;
  addingComment = false;
  addingLink = false;
  
  commentForm: FormGroup;
  linkForm: FormGroup;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private taskService: TaskService,
    private authService: AuthService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.commentForm = this.fb.group({
      content: ['', [Validators.required]]
    });

    this.linkForm = this.fb.group({
      title: ['', [Validators.required]],
      link: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]]
    });
  }

  ngOnInit(): void {
    const taskId = Number(this.route.snapshot.paramMap.get('id'));
    if (taskId && !isNaN(taskId)) {
      this.loadTask(taskId);
    } else {
      this.router.navigate(['/tasks']);
    }
  }

  get canDeleteTasks(): boolean {
    return this.authService.hasPermission('delete_tasks');
  }

  loadTask(id: number): void {
    this.taskService.getTask(id).subscribe({
      next: (response) => {
        this.task = response.task;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading task:', error);
        this.snackBar.open('Error loading task', 'Close', { duration: 5000 });
        this.router.navigate(['/tasks']);
      }
    });
  }

  addComment(): void {
    if (this.commentForm.invalid || !this.task) return;

    this.addingComment = true;
    const content = this.commentForm.value.content;

    this.taskService.addComment(this.task.id, content).subscribe({
      next: (response) => {
        this.addingComment = false;
        this.commentForm.reset();
        this.loadTask(this.task!.id); // Reload to get updated comments
        this.snackBar.open('Comment added successfully', 'Close', { duration: 3000 });
      },
      error: (error) => {
        this.addingComment = false;
        console.error('Error adding comment:', error);
        this.snackBar.open('Error adding comment', 'Close', { duration: 5000 });
      }
    });
  }

  addLinkedItem(): void {
    if (this.linkForm.invalid || !this.task) return;

    this.addingLink = true;
    const { title, link } = this.linkForm.value;

    this.taskService.addLinkedItem(this.task.id, link, title).subscribe({
      next: (response) => {
        this.addingLink = false;
        this.linkForm.reset();
        this.loadTask(this.task!.id); // Reload to get updated links
        this.snackBar.open('Link added successfully', 'Close', { duration: 3000 });
      },
      error: (error) => {
        this.addingLink = false;
        console.error('Error adding link:', error);
        this.snackBar.open('Error adding link', 'Close', { duration: 5000 });
      }
    });
  }

  deleteTask(): void {
    if (!this.task) return;

    if (confirm(`Are you sure you want to delete "${this.task.title}"?`)) {
      this.taskService.deleteTask(this.task.id).subscribe({
        next: () => {
          this.snackBar.open('Task deleted successfully', 'Close', { duration: 3000 });
          this.router.navigate(['/tasks']);
        },
        error: (error) => {
          console.error('Error deleting task:', error);
          this.snackBar.open('Error deleting task', 'Close', { duration: 5000 });
        }
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/tasks']);
  }
}
