import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { TaskService, Task, TaskFilters } from '../../../services/task.service';
import { AuthService, User } from '../../../services/auth.service';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-task-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatSnackBarModule,
    MatMenuModule
  ],
  template: `
    <div class="task-list-container">
      <div class="header">
        <h1>Tasks</h1>
        <button mat-raised-button color="primary" routerLink="/tasks/create">
          <mat-icon>add</mat-icon>
          Create Task
        </button>
      </div>

      <!-- Filters -->
      <mat-card class="filters-card">
        <mat-card-content>
          <form [formGroup]="filterForm" class="filters-form">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput formControlName="search" placeholder="Search tasks...">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option value="">All Statuses</mat-option>
                <mat-option value="New">New</mat-option>
                <mat-option value="ToDo">ToDo</mat-option>
                <mat-option value="In Progress">In Progress</mat-option>
                <mat-option value="Blocked">Blocked</mat-option>
                <mat-option value="Closed">Closed</mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Assignee</mat-label>
              <mat-select formControlName="assigneeId">
                <mat-option value="">All Assignees</mat-option>
                <mat-option *ngFor="let user of users" [value]="user.id">
                  {{ user.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-button type="button" (click)="clearFilters()">Clear</button>
              <button mat-raised-button color="primary" type="button" (click)="applyFilters()">Apply</button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Loading -->
      <div class="loading-container" *ngIf="loading">
        <mat-spinner></mat-spinner>
        <p>Loading tasks...</p>
      </div>

      <!-- Tasks -->
      <div class="tasks-grid" *ngIf="!loading">
        <mat-card class="task-card" *ngFor="let task of tasks" [routerLink]="['/tasks', task.id]">
          <mat-card-header>
            <mat-card-title>{{ task.title }}</mat-card-title>
            <mat-card-subtitle>
              <mat-chip [class]="'status-' + task.status.toLowerCase().replace(' ', '-')">
                {{ task.status }}
              </mat-chip>
            </mat-card-subtitle>
            <div class="spacer"></div>
            <button mat-icon-button [matMenuTriggerFor]="taskMenu" (click)="$event.stopPropagation()">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #taskMenu="matMenu">
              <button mat-menu-item [routerLink]="['/tasks', task.id, 'edit']">
                <mat-icon>edit</mat-icon>
                <span>Edit</span>
              </button>
              <button mat-menu-item (click)="deleteTask(task)" *ngIf="canDeleteTasks">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-card-header>
          
          <mat-card-content>
            <p class="task-description">{{ task.description }}</p>
            <div class="task-meta">
              <div class="assignee" *ngIf="task.assignee_name">
                <mat-icon>person</mat-icon>
                <span>{{ task.assignee_name }}</span>
              </div>
              <div class="due-date" *ngIf="task.due_date">
                <mat-icon>schedule</mat-icon>
                <span>{{ task.due_date | date:'short' }}</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Empty state -->
      <div class="empty-state" *ngIf="!loading && tasks.length === 0">
        <mat-icon>assignment</mat-icon>
        <h2>No tasks found</h2>
        <p>Create your first task to get started</p>
        <button mat-raised-button color="primary" routerLink="/tasks/create">
          Create Task
        </button>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="!loading && tasks.length > 0"
        [length]="totalTasks"
        [pageSize]="pageSize"
        [pageSizeOptions]="[10, 25, 50, 100]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </div>
  `,
  styles: [`
    .task-list-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .header h1 {
      margin: 0;
      font-size: 2rem;
    }

    .filters-card {
      margin-bottom: 24px;
    }

    .filters-form {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr auto;
      gap: 16px;
      align-items: end;
    }

    .filter-actions {
      display: flex;
      gap: 8px;
    }

    .loading-container {
      text-align: center;
      padding: 48px;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    .tasks-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .task-card {
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .task-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .task-card mat-card-header {
      display: flex;
      align-items: flex-start;
    }

    .spacer {
      flex: 1;
    }

    .task-description {
      margin: 0 0 16px 0;
      color: #666;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .task-meta {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #666;
    }

    .task-meta > div {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .task-meta mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .status-new { background-color: #e3f2fd; color: #1976d2; }
    .status-todo { background-color: #f3e5f5; color: #7b1fa2; }
    .status-in-progress { background-color: #fff3e0; color: #f57c00; }
    .status-blocked { background-color: #ffebee; color: #d32f2f; }
    .status-closed { background-color: #e8f5e8; color: #388e3c; }

    .empty-state {
      text-align: center;
      padding: 48px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state h2 {
      margin: 0 0 8px 0;
    }

    .empty-state p {
      margin: 0 0 24px 0;
    }

    @media (max-width: 768px) {
      .filters-form {
        grid-template-columns: 1fr;
      }
      
      .tasks-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TaskListComponent implements OnInit {
  tasks: Task[] = [];
  users: User[] = [];
  loading = true;
  totalTasks = 0;
  pageSize = 10;
  currentPage = 0;
  
  filterForm: FormGroup;
  currentUser: User | null = null;

  constructor(
    private taskService: TaskService,
    private authService: AuthService,
    private userService: UserService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.filterForm = this.fb.group({
      search: [''],
      status: [''],
      assigneeId: ['']
    });

    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.loadUsers();
    this.loadTasks();
    
    // Auto-apply filters on form changes
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  get canDeleteTasks(): boolean {
    return this.authService.hasPermission('delete_tasks');
  }

  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (response) => {
        this.users = response.users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });
  }

  loadTasks(): void {
    this.loading = true;
    const filters: TaskFilters = {
      ...this.filterForm.value,
      page: this.currentPage + 1,
      limit: this.pageSize
    };

    // Remove empty values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof TaskFilters] === '' || filters[key as keyof TaskFilters] === null) {
        delete filters[key as keyof TaskFilters];
      }
    });

    this.taskService.getTasks(filters).subscribe({
      next: (response) => {
        this.tasks = response.tasks;
        this.totalTasks = response.pagination?.total || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading tasks:', error);
        this.snackBar.open('Error loading tasks', 'Close', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.currentPage = 0;
    this.loadTasks();
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 0;
    this.loadTasks();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadTasks();
  }

  deleteTask(task: Task): void {
    if (confirm(`Are you sure you want to delete "${task.title}"?`)) {
      this.taskService.deleteTask(task.id).subscribe({
        next: () => {
          this.snackBar.open('Task deleted successfully', 'Close', { duration: 3000 });
          this.loadTasks();
        },
        error: (error) => {
          console.error('Error deleting task:', error);
          this.snackBar.open('Error deleting task', 'Close', { duration: 5000 });
        }
      });
    }
  }
}
