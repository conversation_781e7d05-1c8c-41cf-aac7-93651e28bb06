import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { TaskService, Task, TaskFilters } from '../../../services/task.service';
import { AuthService, User } from '../../../services/auth.service';
import { UserService } from '../../../services/user.service';

@Component({
  selector: 'app-task-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    MatSnackBarModule,
    MatMenuModule
  ],
  template: `
    <div class="task-list-container">
      <div class="header">
        <h1>Tasks</h1>
        <button mat-raised-button color="primary" routerLink="/tasks/create">
          <mat-icon>add</mat-icon>
          Create Task
        </button>
      </div>

      <!-- Filters -->
      <mat-card class="filters-card">
        <mat-card-header>
          <mat-card-title class="filter-title">
            <mat-icon>filter_list</mat-icon>
            Filter Tasks
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="filterForm" class="filters-form">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search Tasks</mat-label>
              <input matInput
                     formControlName="search"
                     placeholder="Search by title or description...">
              <mat-icon matSuffix color="primary">search</mat-icon>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option value="">
                  <mat-icon>all_inclusive</mat-icon>
                  All Statuses
                </mat-option>
                <mat-option value="New">
                  <mat-icon>fiber_new</mat-icon>
                  New
                </mat-option>
                <mat-option value="ToDo">
                  <mat-icon>list</mat-icon>
                  ToDo
                </mat-option>
                <mat-option value="In Progress">
                  <mat-icon>trending_up</mat-icon>
                  In Progress
                </mat-option>
                <mat-option value="Blocked">
                  <mat-icon>block</mat-icon>
                  Blocked
                </mat-option>
                <mat-option value="Closed">
                  <mat-icon>check_circle</mat-icon>
                  Closed
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Assignee</mat-label>
              <mat-select formControlName="assigneeId">
                <mat-option value="">
                  <mat-icon>people</mat-icon>
                  All Assignees
                </mat-option>
                <mat-option *ngFor="let user of users" [value]="user.id">
                  <mat-icon>person</mat-icon>
                  {{ user.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-stroked-button type="button" (click)="clearFilters()" class="clear-btn">
                <mat-icon>clear</mat-icon>
                Clear
              </button>
              <button mat-raised-button color="primary" type="button" (click)="applyFilters()" class="apply-btn">
                <mat-icon>done</mat-icon>
                Apply
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Loading -->
      <div class="loading-container" *ngIf="loading">
        <mat-spinner></mat-spinner>
        <p>Loading tasks...</p>
      </div>

      <!-- Tasks -->
      <div class="tasks-grid" *ngIf="!loading">
        <mat-card class="task-card" *ngFor="let task of tasks" [routerLink]="['/tasks', task.id]">
          <mat-card-header>
            <mat-card-title>{{ task.title }}</mat-card-title>
            <mat-card-subtitle>
              <mat-chip [class]="'status-' + task.status.toLowerCase().replace(' ', '-')">
                {{ task.status }}
              </mat-chip>
            </mat-card-subtitle>
            <div class="spacer"></div>
            <button mat-icon-button [matMenuTriggerFor]="taskMenu" (click)="$event.stopPropagation()">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #taskMenu="matMenu">
              <button mat-menu-item [routerLink]="['/tasks', task.id, 'edit']">
                <mat-icon>edit</mat-icon>
                <span>Edit</span>
              </button>
              <button mat-menu-item (click)="deleteTask(task)" *ngIf="canDeleteTasks">
                <mat-icon>delete</mat-icon>
                <span>Delete</span>
              </button>
            </mat-menu>
          </mat-card-header>
          
          <mat-card-content>
            <p class="task-description">{{ task.description }}</p>
            <div class="task-meta">
              <div class="assignee" *ngIf="task.assignee_name">
                <mat-icon>person</mat-icon>
                <span>{{ task.assignee_name }}</span>
              </div>
              <div class="due-date" *ngIf="task.due_date">
                <mat-icon>schedule</mat-icon>
                <span>{{ task.due_date | date:'short' }}</span>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Empty state -->
      <div class="empty-state" *ngIf="!loading && tasks.length === 0">
        <mat-icon>assignment</mat-icon>
        <h2>No tasks found</h2>
        <p>Create your first task to get started</p>
        <button mat-raised-button color="primary" routerLink="/tasks/create">
          Create Task
        </button>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="!loading && tasks.length > 0"
        [length]="totalTasks"
        [pageSize]="pageSize"
        [pageSizeOptions]="[10, 25, 50, 100]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </div>
  `,
  styles: [`
    .task-list-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
      background: #f8fafc;
      min-height: 100vh;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      padding: 24px 0;
    }

    .header h1 {
      margin: 0;
      font-size: 2.5rem;
      font-weight: 600;
      color: #1e293b;
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .header h1::before {
      content: '📋';
      font-size: 2rem;
    }

    .header button {
      height: 48px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 12px;
      padding: 0 24px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
    }

    .header button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .filters-card {
      margin-bottom: 32px;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid #e2e8f0;
      background: white;
    }

    .filter-title {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #1e293b;
      font-size: 1.25rem;
      font-weight: 600;
    }

    .filter-title mat-icon {
      color: #667eea;
    }

    .filters-form {
      display: grid;
      grid-template-columns: 2fr 1fr 1fr auto;
      gap: 20px;
      align-items: end;
    }

    .search-field {
      grid-column: 1;
    }

    .filter-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }

    .clear-btn {
      height: 40px;
      border-radius: 8px;
      color: #64748b;
      border-color: #e2e8f0;
    }

    .apply-btn {
      height: 40px;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
    }

    .loading-container {
      text-align: center;
      padding: 64px;
      background: white;
      border-radius: 16px;
      margin: 32px 0;
    }

    .loading-container p {
      margin-top: 16px;
      color: #64748b;
      font-size: 16px;
    }

    .tasks-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      gap: 24px;
      margin-bottom: 32px;
    }

    .task-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border-radius: 16px;
      border: 1px solid #e2e8f0;
      background: white;
      overflow: hidden;
      position: relative;
    }

    .task-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .task-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      border-color: #667eea;
    }

    .task-card mat-card-header {
      padding: 20px 20px 16px 20px;
      display: flex;
      align-items: flex-start;
    }

    .task-card mat-card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e293b;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .task-card mat-card-content {
      padding: 0 20px 20px 20px;
    }

    .spacer {
      flex: 1;
    }

    .task-description {
      margin: 0 0 16px 0;
      color: #64748b;
      line-height: 1.6;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .task-meta {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #64748b;
    }

    .task-meta > div {
      display: flex;
      align-items: center;
      gap: 6px;
      background: #f1f5f9;
      padding: 6px 12px;
      border-radius: 20px;
    }

    .task-meta mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    /* Status Chips with Beautiful Colors */
    .status-new {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
      font-weight: 600;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 12px;
    }

    .status-todo {
      background: linear-gradient(135deg, #8b5cf6, #7c3aed);
      color: white;
      font-weight: 600;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 12px;
    }

    .status-in-progress {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
      font-weight: 600;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 12px;
    }

    .status-blocked {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
      font-weight: 600;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 12px;
    }

    .status-closed {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      font-weight: 600;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 12px;
    }

    .empty-state {
      text-align: center;
      padding: 64px;
      background: white;
      border-radius: 16px;
      border: 2px dashed #e2e8f0;
      margin: 32px 0;
    }

    .empty-state mat-icon {
      font-size: 80px;
      width: 80px;
      height: 80px;
      margin-bottom: 24px;
      color: #cbd5e1;
    }

    .empty-state h2 {
      margin: 0 0 12px 0;
      color: #1e293b;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .empty-state p {
      margin: 0 0 32px 0;
      color: #64748b;
      font-size: 16px;
    }

    .empty-state button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      padding: 12px 24px;
      font-weight: 600;
    }

    /* Material Design Form Field Improvements */
    ::ng-deep .mat-mdc-form-field {
      width: 100%;
    }

    ::ng-deep .mat-mdc-form-field .mat-mdc-form-field-focus-overlay {
      background-color: rgba(102, 126, 234, 0.04);
    }

    ::ng-deep .mat-mdc-form-field-label {
      color: #64748b !important;
    }

    ::ng-deep .mat-mdc-form-field-label.mdc-floating-label--float-above {
      color: #667eea !important;
    }

    ::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
      color: #667eea !important;
    }

    ::ng-deep .mat-mdc-select-panel {
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }

    ::ng-deep .mat-mdc-option {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
    }

    ::ng-deep .mat-mdc-option:hover {
      background-color: rgba(102, 126, 234, 0.08);
    }

    @media (max-width: 768px) {
      .task-list-container {
        padding: 16px;
      }

      .header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .header h1 {
        font-size: 2rem;
        text-align: center;
      }

      .filters-form {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .filter-actions {
        justify-content: stretch;
      }

      .filter-actions button {
        flex: 1;
      }

      .tasks-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  `]
})
export class TaskListComponent implements OnInit {
  tasks: Task[] = [];
  users: User[] = [];
  loading = true;
  totalTasks = 0;
  pageSize = 10;
  currentPage = 0;
  
  filterForm: FormGroup;
  currentUser: User | null = null;

  constructor(
    private taskService: TaskService,
    private authService: AuthService,
    private userService: UserService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.filterForm = this.fb.group({
      search: [''],
      status: [''],
      assigneeId: ['']
    });

    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.loadUsers();
    this.loadTasks();
    
    // Auto-apply filters on form changes
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  get canDeleteTasks(): boolean {
    return this.authService.hasPermission('delete_tasks');
  }

  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (response) => {
        this.users = response.users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });
  }

  loadTasks(): void {
    this.loading = true;
    const filters: TaskFilters = {
      ...this.filterForm.value,
      page: this.currentPage + 1,
      limit: this.pageSize
    };

    // Remove empty values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof TaskFilters] === '' || filters[key as keyof TaskFilters] === null) {
        delete filters[key as keyof TaskFilters];
      }
    });

    this.taskService.getTasks(filters).subscribe({
      next: (response) => {
        this.tasks = response.tasks;
        this.totalTasks = response.pagination?.total || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading tasks:', error);
        this.snackBar.open('Error loading tasks', 'Close', { duration: 5000 });
        this.loading = false;
      }
    });
  }

  applyFilters(): void {
    this.currentPage = 0;
    this.loadTasks();
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 0;
    this.loadTasks();
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadTasks();
  }

  deleteTask(task: Task): void {
    if (confirm(`Are you sure you want to delete "${task.title}"?`)) {
      this.taskService.deleteTask(task.id).subscribe({
        next: () => {
          this.snackBar.open('Task deleted successfully', 'Close', { duration: 3000 });
          this.loadTasks();
        },
        error: (error) => {
          console.error('Error deleting task:', error);
          this.snackBar.open('Error deleting task', 'Close', { duration: 5000 });
        }
      });
    }
  }
}
