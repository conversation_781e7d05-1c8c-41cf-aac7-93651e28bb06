import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TaskService, Task } from '../../../services/task.service';
import { UserService } from '../../../services/user.service';
import { User } from '../../../services/auth.service';

@Component({
  selector: 'app-task-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="task-form-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            {{ isEditMode ? 'Edit Task' : 'Create New Task' }}
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="taskForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Task Title</mat-label>
              <input matInput
                     formControlName="title"
                     placeholder="Enter a descriptive title for the task"
                     required>
              <mat-icon matSuffix color="primary">title</mat-icon>
              <mat-error *ngIf="taskForm.get('title')?.hasError('required')">
                Task title is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput
                        formControlName="description"
                        placeholder="Provide detailed description of the task..."
                        rows="4"></textarea>
              <mat-icon matSuffix color="primary">description</mat-icon>
            </mat-form-field>

            <div class="form-row">
              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status" required>
                  <mat-option value="New">
                    <mat-icon>fiber_new</mat-icon>
                    New
                  </mat-option>
                  <mat-option value="ToDo">
                    <mat-icon>list</mat-icon>
                    ToDo
                  </mat-option>
                  <mat-option value="In Progress">
                    <mat-icon>trending_up</mat-icon>
                    In Progress
                  </mat-option>
                  <mat-option value="Blocked">
                    <mat-icon>block</mat-icon>
                    Blocked
                  </mat-option>
                  <mat-option value="Closed">
                    <mat-icon>check_circle</mat-icon>
                    Closed
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix color="primary">flag</mat-icon>
                <mat-error *ngIf="taskForm.get('status')?.hasError('required')">
                  Status is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="half-width">
                <mat-label>Assignee</mat-label>
                <mat-select formControlName="assignee_id">
                  <mat-option value="">
                    <mat-icon>person_off</mat-icon>
                    Unassigned
                  </mat-option>
                  <mat-option *ngFor="let user of users" [value]="user.id">
                    <mat-icon>person</mat-icon>
                    {{ user.name }}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix color="primary">people</mat-icon>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Due Date</mat-label>
              <input matInput
                     [matDatepicker]="picker"
                     formControlName="due_date"
                     placeholder="Select due date">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </form>

          <div class="loading-container" *ngIf="loading">
            <mat-spinner></mat-spinner>
            <p>{{ isEditMode ? 'Loading task...' : 'Loading...' }}</p>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button type="button" (click)="goBack()">Cancel</button>
          <button mat-raised-button color="primary" 
                  [disabled]="taskForm.invalid || submitting" 
                  (click)="onSubmit()">
            <mat-spinner diameter="20" *ngIf="submitting"></mat-spinner>
            <span *ngIf="!submitting">{{ isEditMode ? 'Update' : 'Create' }}</span>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .task-form-container {
      padding: 24px;
      max-width: 900px;
      margin: 0 auto;
      background: #f8fafc;
      min-height: 100vh;
    }

    .full-width {
      width: 100%;
      margin-bottom: 20px;
    }

    .half-width {
      flex: 1;
    }

    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
    }

    .loading-container {
      text-align: center;
      padding: 48px;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    mat-card {
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
      background: white;
    }

    mat-card-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 24px;
    }

    mat-card-title {
      font-size: 24px;
      font-weight: 600;
      color: white;
      margin: 0;
    }

    mat-card-content {
      padding: 32px 24px;
    }

    mat-card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      padding: 24px;
      border-top: 1px solid #e2e8f0;
      background: #f8fafc;
    }

    mat-card-actions button {
      height: 48px;
      border-radius: 12px;
      font-weight: 600;
      padding: 0 24px;
    }

    /* Material Design Form Field Improvements */
    ::ng-deep .mat-mdc-form-field-label {
      color: #64748b !important;
    }

    ::ng-deep .mat-mdc-form-field-label.mdc-floating-label--float-above {
      color: #667eea !important;
    }

    ::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
      color: #667eea !important;
    }

    ::ng-deep .mat-mdc-select-panel {
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }

    ::ng-deep .mat-mdc-option {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
    }

    ::ng-deep .mat-mdc-option:hover {
      background-color: rgba(102, 126, 234, 0.08);
    }

    @media (max-width: 768px) {
      .task-form-container {
        padding: 16px;
      }

      .form-row {
        flex-direction: column;
        gap: 16px;
      }

      mat-card-header, mat-card-content, mat-card-actions {
        padding: 16px;
      }
    }
  `]
})
export class TaskFormComponent implements OnInit {
  taskForm: FormGroup;
  users: User[] = [];
  loading = true;
  submitting = false;
  isEditMode = false;
  taskId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private taskService: TaskService,
    private userService: UserService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.taskForm = this.fb.group({
      title: ['', [Validators.required]],
      description: [''],
      status: ['New', [Validators.required]],
      assignee_id: [''],
      due_date: ['']
    });
  }

  ngOnInit(): void {
    this.taskId = Number(this.route.snapshot.paramMap.get('id'));
    this.isEditMode = !!this.taskId && !isNaN(this.taskId);

    this.loadUsers();
    
    if (this.isEditMode) {
      this.loadTask();
    } else {
      this.loading = false;
    }
  }

  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (response) => {
        this.users = response.users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.snackBar.open('Error loading users', 'Close', { duration: 5000 });
      }
    });
  }

  loadTask(): void {
    if (!this.taskId) return;

    this.taskService.getTask(this.taskId).subscribe({
      next: (response) => {
        const task = response.task;
        this.taskForm.patchValue({
          title: task.title,
          description: task.description,
          status: task.status,
          assignee_id: task.assignee_id,
          due_date: task.due_date ? new Date(task.due_date) : null
        });
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading task:', error);
        this.snackBar.open('Error loading task', 'Close', { duration: 5000 });
        this.router.navigate(['/tasks']);
      }
    });
  }

  onSubmit(): void {
    if (this.taskForm.invalid) return;

    this.submitting = true;
    const formValue = this.taskForm.value;
    
    // Format the task data
    const taskData = {
      title: formValue.title,
      description: formValue.description,
      status: formValue.status,
      assignee_id: formValue.assignee_id || null,
      due_date: formValue.due_date ? new Date(formValue.due_date).toISOString() : null
    };

    const operation = this.isEditMode 
      ? this.taskService.updateTask(this.taskId!, taskData)
      : this.taskService.createTask(taskData);

    operation.subscribe({
      next: (response) => {
        this.submitting = false;
        const message = this.isEditMode ? 'Task updated successfully' : 'Task created successfully';
        this.snackBar.open(message, 'Close', { duration: 3000 });
        this.router.navigate(['/tasks']);
      },
      error: (error) => {
        this.submitting = false;
        console.error('Error saving task:', error);
        const message = this.isEditMode ? 'Error updating task' : 'Error creating task';
        this.snackBar.open(message, 'Close', { duration: 5000 });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/tasks']);
  }
}
