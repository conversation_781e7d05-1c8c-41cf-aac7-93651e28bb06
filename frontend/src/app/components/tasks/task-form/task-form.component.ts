import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TaskService, Task } from '../../../services/task.service';
import { UserService } from '../../../services/user.service';
import { User } from '../../../services/auth.service';

@Component({
  selector: 'app-task-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="task-form-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            {{ isEditMode ? 'Edit Task' : 'Create New Task' }}
          </mat-card-title>
        </mat-card-header>

        <mat-card-content>
          <form [formGroup]="taskForm" (ngSubmit)="onSubmit()" *ngIf="!loading">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Title</mat-label>
              <input matInput formControlName="title" required>
              <mat-error *ngIf="taskForm.get('title')?.hasError('required')">
                Title is required
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" rows="4"></textarea>
            </mat-form-field>

            <div class="form-row">
              <mat-form-field appearance="outline">
                <mat-label>Status</mat-label>
                <mat-select formControlName="status" required>
                  <mat-option value="New">New</mat-option>
                  <mat-option value="ToDo">ToDo</mat-option>
                  <mat-option value="In Progress">In Progress</mat-option>
                  <mat-option value="Blocked">Blocked</mat-option>
                  <mat-option value="Closed">Closed</mat-option>
                </mat-select>
                <mat-error *ngIf="taskForm.get('status')?.hasError('required')">
                  Status is required
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Assignee</mat-label>
                <mat-select formControlName="assignee_id">
                  <mat-option value="">Unassigned</mat-option>
                  <mat-option *ngFor="let user of users" [value]="user.id">
                    {{ user.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Due Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="due_date">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </form>

          <div class="loading-container" *ngIf="loading">
            <mat-spinner></mat-spinner>
            <p>{{ isEditMode ? 'Loading task...' : 'Loading...' }}</p>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button type="button" (click)="goBack()">Cancel</button>
          <button mat-raised-button color="primary" 
                  [disabled]="taskForm.invalid || submitting" 
                  (click)="onSubmit()">
            <mat-spinner diameter="20" *ngIf="submitting"></mat-spinner>
            <span *ngIf="!submitting">{{ isEditMode ? 'Update' : 'Create' }}</span>
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .task-form-container {
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
    }

    .full-width {
      width: 100%;
      margin-bottom: 16px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .loading-container {
      text-align: center;
      padding: 48px;
    }

    .loading-container p {
      margin-top: 16px;
      color: #666;
    }

    mat-card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      padding: 16px 24px;
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TaskFormComponent implements OnInit {
  taskForm: FormGroup;
  users: User[] = [];
  loading = true;
  submitting = false;
  isEditMode = false;
  taskId: number | null = null;

  constructor(
    private fb: FormBuilder,
    private taskService: TaskService,
    private userService: UserService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.taskForm = this.fb.group({
      title: ['', [Validators.required]],
      description: [''],
      status: ['New', [Validators.required]],
      assignee_id: [''],
      due_date: ['']
    });
  }

  ngOnInit(): void {
    this.taskId = Number(this.route.snapshot.paramMap.get('id'));
    this.isEditMode = !!this.taskId && !isNaN(this.taskId);

    this.loadUsers();
    
    if (this.isEditMode) {
      this.loadTask();
    } else {
      this.loading = false;
    }
  }

  loadUsers(): void {
    this.userService.getUsers().subscribe({
      next: (response) => {
        this.users = response.users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        this.snackBar.open('Error loading users', 'Close', { duration: 5000 });
      }
    });
  }

  loadTask(): void {
    if (!this.taskId) return;

    this.taskService.getTask(this.taskId).subscribe({
      next: (response) => {
        const task = response.task;
        this.taskForm.patchValue({
          title: task.title,
          description: task.description,
          status: task.status,
          assignee_id: task.assignee_id,
          due_date: task.due_date ? new Date(task.due_date) : null
        });
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading task:', error);
        this.snackBar.open('Error loading task', 'Close', { duration: 5000 });
        this.router.navigate(['/tasks']);
      }
    });
  }

  onSubmit(): void {
    if (this.taskForm.invalid) return;

    this.submitting = true;
    const formValue = this.taskForm.value;
    
    // Format the task data
    const taskData = {
      title: formValue.title,
      description: formValue.description,
      status: formValue.status,
      assignee_id: formValue.assignee_id || null,
      due_date: formValue.due_date ? new Date(formValue.due_date).toISOString() : null
    };

    const operation = this.isEditMode 
      ? this.taskService.updateTask(this.taskId!, taskData)
      : this.taskService.createTask(taskData);

    operation.subscribe({
      next: (response) => {
        this.submitting = false;
        const message = this.isEditMode ? 'Task updated successfully' : 'Task created successfully';
        this.snackBar.open(message, 'Close', { duration: 3000 });
        this.router.navigate(['/tasks']);
      },
      error: (error) => {
        this.submitting = false;
        console.error('Error saving task:', error);
        const message = this.isEditMode ? 'Error updating task' : 'Error creating task';
        this.snackBar.open(message, 'Close', { duration: 5000 });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/tasks']);
  }
}
