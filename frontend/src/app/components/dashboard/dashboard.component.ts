import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TaskService, TaskStats } from '../../services/task.service';
import { AuthService, User } from '../../services/auth.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="dashboard-container">
      <!-- Header Section -->
      <div class="dashboard-header">
        <div class="welcome-section">
          <div class="welcome-content">
            <div class="greeting">
              <h1>Good {{ getTimeOfDay() }}, {{ currentUser?.name }}! 👋</h1>
              <p class="welcome-subtitle">Here's what's happening with your tasks today</p>
            </div>
            <div class="user-badge">
              <div class="user-avatar">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
              <div class="user-info">
                <span class="user-name">{{ currentUser?.name }}</span>
                <span class="user-role">{{ currentUser?.role }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Grid -->
      <div class="stats-section" *ngIf="!statsLoading">
        <h2 class="section-title">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
          </svg>
          Task Overview
        </h2>

        <div class="stats-grid">
          <div class="stat-card total-tasks">
            <div class="stat-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskStats?.total || 0 }}</div>
              <div class="stat-label">Total Tasks</div>
              <div class="stat-trend positive">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                +12% from last week
              </div>
            </div>
          </div>

          <div class="stat-card in-progress">
            <div class="stat-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskStats?.in_progress || 0 }}</div>
              <div class="stat-label">In Progress</div>
              <div class="stat-trend neutral">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M7 14l5-5 5 5z"/>
                </svg>
                Active tasks
              </div>
            </div>
          </div>

          <div class="stat-card completed">
            <div class="stat-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskStats?.closed || 0 }}</div>
              <div class="stat-label">Completed</div>
              <div class="stat-trend positive">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                </svg>
                +8% completion rate
              </div>
            </div>
          </div>

          <div class="stat-card overdue">
            <div class="stat-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ taskStats?.overdue || 0 }}</div>
              <div class="stat-label">Overdue</div>
              <div class="stat-trend negative" *ngIf="(taskStats?.overdue || 0) > 0">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M16 18l2.29-2.29-4.88-4.88-4 4L2 7.41 3.41 6l6 6 4-4 6.3 6.29L22 12v6z"/>
                </svg>
                Needs attention
              </div>
              <div class="stat-trend positive" *ngIf="(taskStats?.overdue || 0) === 0">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                </svg>
                All caught up!
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="actions-section">
        <h2 class="section-title">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
          </svg>
          Quick Actions
        </h2>

        <div class="actions-grid">
          <button class="action-btn primary" routerLink="/tasks/create">
            <div class="action-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
            </div>
            <div class="action-content">
              <span class="action-title">Create Task</span>
              <span class="action-subtitle">Add a new task</span>
            </div>
            <div class="action-arrow">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 17l5-5-5-5v10z"/>
              </svg>
            </div>
          </button>

          <button class="action-btn secondary" routerLink="/tasks">
            <div class="action-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
              </svg>
            </div>
            <div class="action-content">
              <span class="action-title">View All Tasks</span>
              <span class="action-subtitle">Browse task list</span>
            </div>
            <div class="action-arrow">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 17l5-5-5-5v10z"/>
              </svg>
            </div>
          </button>

          <button class="action-btn accent" routerLink="/users" *ngIf="currentUser?.role === 'Admin'">
            <div class="action-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z"/>
              </svg>
            </div>
            <div class="action-content">
              <span class="action-title">Manage Users</span>
              <span class="action-subtitle">User administration</span>
            </div>
            <div class="action-arrow">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 17l5-5-5-5v10z"/>
              </svg>
            </div>
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div class="loading-container" *ngIf="statsLoading">
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
        <p>Loading your dashboard...</p>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 24px;
      max-width: 1400px;
      margin: 0 auto;
      background: #f8fafc;
      min-height: 100vh;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .dashboard-header {
      margin-bottom: 40px;
    }

    .welcome-section {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      border-radius: 24px;
      padding: 0;
      overflow: hidden;
      position: relative;
    }

    .welcome-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
    }

    .welcome-content {
      position: relative;
      z-index: 2;
      padding: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: white;
    }

    .greeting h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 8px 0;
      background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .welcome-subtitle {
      font-size: 1.1rem;
      margin: 0;
      opacity: 0.9;
      font-weight: 400;
    }

    .user-badge {
      display: flex;
      align-items: center;
      gap: 16px;
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 20px;
      padding: 16px 24px;
    }

    .user-avatar {
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .user-avatar svg {
      width: 24px;
      height: 24px;
      color: white;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .user-name {
      font-size: 1rem;
      font-weight: 600;
      color: white;
    }

    .user-role {
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.8);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .section-title {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0 0 24px 0;
    }

    .section-title svg {
      width: 24px;
      height: 24px;
      color: #667eea;
    }

    .stats-section {
      margin-bottom: 40px;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;
    }

    .stat-card {
      background: white;
      border-radius: 20px;
      padding: 24px;
      border: 1px solid #e2e8f0;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      display: flex;
      align-items: flex-start;
      gap: 16px;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
    }

    .stat-card.total-tasks::before {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .stat-card.in-progress::before {
      background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .stat-card.completed::before {
      background: linear-gradient(135deg, #10b981, #059669);
    }

    .stat-card.overdue::before {
      background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      width: 56px;
      height: 56px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .total-tasks .stat-icon {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
    }

    .in-progress .stat-icon {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }

    .completed .stat-icon {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    .overdue .stat-icon {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
    }

    .stat-icon svg {
      width: 24px;
      height: 24px;
    }

    .stat-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.875rem;
      color: #64748b;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .stat-trend {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 0.75rem;
      font-weight: 500;
      margin-top: 8px;
    }

    .stat-trend svg {
      width: 12px;
      height: 12px;
    }

    .stat-trend.positive {
      color: #10b981;
    }

    .stat-trend.negative {
      color: #ef4444;
    }

    .stat-trend.neutral {
      color: #64748b;
    }

    .actions-section {
      margin-bottom: 40px;
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 20px;
    }

    .action-btn {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      color: inherit;
      width: 100%;
      text-align: left;
    }

    .action-btn:hover {
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.02);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .action-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .action-btn.primary .action-icon {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }

    .action-btn.secondary .action-icon {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    .action-btn.accent .action-icon {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }

    .action-icon svg {
      width: 20px;
      height: 20px;
    }

    .action-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .action-title {
      font-size: 1rem;
      font-weight: 600;
      color: #1e293b;
    }

    .action-subtitle {
      font-size: 0.875rem;
      color: #64748b;
    }

    .action-arrow {
      width: 20px;
      height: 20px;
      color: #9ca3af;
      transition: all 0.2s ease;
    }

    .action-btn:hover .action-arrow {
      color: #667eea;
      transform: translateX(4px);
    }

    .action-arrow svg {
      width: 100%;
      height: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 64px;
      background: white;
      border-radius: 20px;
      border: 1px solid #e2e8f0;
      margin: 40px 0;
    }

    .loading-spinner {
      margin-bottom: 16px;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #e2e8f0;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-container p {
      color: #64748b;
      font-size: 1rem;
      margin: 0;
    }

    @media (max-width: 768px) {
      .dashboard-container {
        padding: 16px;
      }

      .welcome-content {
        flex-direction: column;
        gap: 24px;
        text-align: center;
        padding: 32px 24px;
      }

      .greeting h1 {
        font-size: 2rem;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .actions-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }

      .action-btn {
        padding: 16px 20px;
      }

      .action-icon {
        width: 40px;
        height: 40px;
        margin-right: 12px;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  taskStats: TaskStats | null = null;
  statsLoading = true;

  constructor(
    private authService: AuthService,
    private taskService: TaskService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadTaskStats();
  }

  loadTaskStats(): void {
    this.taskService.getTaskStats().subscribe({
      next: (response) => {
        this.taskStats = response.stats;
        this.statsLoading = false;
      },
      error: (error) => {
        console.error('Error loading task stats:', error);
        this.statsLoading = false;
      }
    });
  }

  getTimeOfDay(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
  }
}
