import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TaskService, TaskStats } from '../../services/task.service';
import { AuthService, User } from '../../services/auth.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatGridListModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="dashboard-container">
      <div class="welcome-section">
        <h1>Welcome back, {{ currentUser?.name }}!</h1>
        <p class="role-badge" [class]="currentUser?.role?.toLowerCase()">
          {{ currentUser?.role }}
        </p>
      </div>

      <div class="stats-section" *ngIf="!statsLoading">
        <h2>Task Statistics</h2>
        <mat-grid-list cols="4" rowHeight="120px" gutterSize="16px" class="stats-grid">
          <mat-grid-tile>
            <mat-card class="stat-card total">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon>assignment</mat-icon>
                  <div class="stat-info">
                    <h3>{{ taskStats?.total || 0 }}</h3>
                    <p>Total Tasks</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </mat-grid-tile>

          <mat-grid-tile>
            <mat-card class="stat-card new">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon>fiber_new</mat-icon>
                  <div class="stat-info">
                    <h3>{{ taskStats?.new_tasks || 0 }}</h3>
                    <p>New Tasks</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </mat-grid-tile>

          <mat-grid-tile>
            <mat-card class="stat-card progress">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon>trending_up</mat-icon>
                  <div class="stat-info">
                    <h3>{{ taskStats?.in_progress || 0 }}</h3>
                    <p>In Progress</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </mat-grid-tile>

          <mat-grid-tile>
            <mat-card class="stat-card overdue">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon>schedule</mat-icon>
                  <div class="stat-info">
                    <h3>{{ taskStats?.overdue || 0 }}</h3>
                    <p>Overdue</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </mat-grid-tile>
        </mat-grid-list>
      </div>

      <div class="loading-section" *ngIf="statsLoading">
        <mat-spinner></mat-spinner>
        <p>Loading dashboard...</p>
      </div>

      <div class="quick-actions">
        <h2>Quick Actions</h2>
        <div class="action-cards">
          <mat-card class="action-card">
            <mat-card-content>
              <mat-icon>add_task</mat-icon>
              <h3>Create Task</h3>
              <p>Add a new task to the system</p>
              <button mat-raised-button color="primary" routerLink="/tasks/create">
                Create Task
              </button>
            </mat-card-content>
          </mat-card>

          <mat-card class="action-card">
            <mat-card-content>
              <mat-icon>list</mat-icon>
              <h3>View Tasks</h3>
              <p>Browse and manage all tasks</p>
              <button mat-raised-button color="accent" routerLink="/tasks">
                View Tasks
              </button>
            </mat-card-content>
          </mat-card>

          <mat-card class="action-card" *ngIf="currentUser?.role === 'Admin'">
            <mat-card-content>
              <mat-icon>people</mat-icon>
              <h3>Manage Users</h3>
              <p>Add and manage system users</p>
              <button mat-raised-button color="warn" routerLink="/users">
                Manage Users
              </button>
            </mat-card-content>
          </mat-card>

          <mat-card class="action-card">
            <mat-card-content>
              <mat-icon>person</mat-icon>
              <h3>My Profile</h3>
              <p>Update your profile information</p>
              <button mat-raised-button routerLink="/profile">
                View Profile
              </button>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .welcome-section {
      text-align: center;
      margin-bottom: 32px;
    }

    .welcome-section h1 {
      font-size: 2.5rem;
      margin-bottom: 8px;
      color: #333;
    }

    .role-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .role-badge.admin {
      background-color: #ff5722;
      color: white;
    }

    .role-badge.user {
      background-color: #2196f3;
      color: white;
    }

    .stats-section {
      margin-bottom: 32px;
    }

    .stats-section h2 {
      margin-bottom: 16px;
      color: #333;
    }

    .stats-grid {
      margin-bottom: 24px;
    }

    .stat-card {
      width: 100%;
      height: 100%;
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 16px;
      height: 100%;
    }

    .stat-content mat-icon {
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .stat-info h3 {
      font-size: 24px;
      margin: 0;
      font-weight: 600;
    }

    .stat-info p {
      margin: 4px 0 0 0;
      font-size: 12px;
      color: #666;
    }

    .stat-card.total {
      border-left: 4px solid #4caf50;
    }

    .stat-card.new {
      border-left: 4px solid #2196f3;
    }

    .stat-card.progress {
      border-left: 4px solid #ff9800;
    }

    .stat-card.overdue {
      border-left: 4px solid #f44336;
    }

    .loading-section {
      text-align: center;
      padding: 48px;
    }

    .loading-section p {
      margin-top: 16px;
      color: #666;
    }

    .quick-actions h2 {
      margin-bottom: 16px;
      color: #333;
    }

    .action-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .action-card {
      text-align: center;
      transition: transform 0.2s ease;
    }

    .action-card:hover {
      transform: translateY(-2px);
    }

    .action-card mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #666;
      margin-bottom: 16px;
    }

    .action-card h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .action-card p {
      margin: 0 0 16px 0;
      color: #666;
      font-size: 14px;
    }

    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
      }
      
      .action-cards {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  taskStats: TaskStats | null = null;
  statsLoading = true;

  constructor(
    private authService: AuthService,
    private taskService: TaskService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadTaskStats();
  }

  loadTaskStats(): void {
    this.taskService.getTaskStats().subscribe({
      next: (response) => {
        this.taskStats = response.stats;
        this.statsLoading = false;
      },
      error: (error) => {
        console.error('Error loading task stats:', error);
        this.statsLoading = false;
      }
    });
  }
}
