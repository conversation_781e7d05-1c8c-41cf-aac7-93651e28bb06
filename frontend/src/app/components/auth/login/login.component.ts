import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="login-container">
      <!-- Animated Background -->
      <div class="background-animation">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
          <div class="shape shape-4"></div>
          <div class="shape shape-5"></div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="login-content">
        <!-- Brand Section -->
        <div class="brand-section">
          <div class="brand-logo">
            <div class="logo-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
              </svg>
            </div>
          </div>
          <h1 class="brand-title">TaskFlow</h1>
          <p class="brand-subtitle">Modern Task Management Platform</p>
        </div>

        <!-- Login Card -->
        <div class="login-card">
          <div class="card-header">
            <h2>Welcome Back</h2>
            <p>Sign in to continue to your workspace</p>
          </div>

          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
            <!-- Email Field -->
            <div class="form-group">
              <label class="form-label">Email Address</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                  </svg>
                </div>
                <input
                  type="email"
                  formControlName="email"
                  placeholder="Enter your email address"
                  class="form-input"
                  autocomplete="email">
              </div>
              <div class="error-message" *ngIf="loginForm.get('email')?.hasError('required') && loginForm.get('email')?.touched">
                Email is required
              </div>
              <div class="error-message" *ngIf="loginForm.get('email')?.hasError('email') && loginForm.get('email')?.touched">
                Please enter a valid email address
              </div>
            </div>

            <!-- Password Field -->
            <div class="form-group">
              <label class="form-label">Password</label>
              <div class="input-wrapper">
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                  </svg>
                </div>
                <input
                  [type]="hidePassword ? 'password' : 'text'"
                  formControlName="password"
                  placeholder="Enter your password"
                  class="form-input"
                  autocomplete="current-password">
                <button
                  type="button"
                  class="password-toggle"
                  (click)="hidePassword = !hidePassword">
                  <svg *ngIf="hidePassword" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                  </svg>
                  <svg *ngIf="!hidePassword" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
                  </svg>
                </button>
              </div>
              <div class="error-message" *ngIf="loginForm.get('password')?.hasError('required') && loginForm.get('password')?.touched">
                Password is required
              </div>
            </div>

            <!-- Login Button -->
            <button
              type="submit"
              class="login-btn"
              [disabled]="loginForm.invalid || isLoading"
              [class.loading]="isLoading">
              <div class="btn-content" *ngIf="!isLoading">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M10 17l5-5-5-5v10z"/>
                </svg>
                <span>Sign In</span>
              </div>
              <div class="btn-loading" *ngIf="isLoading">
                <div class="spinner"></div>
                <span>Signing In...</span>
              </div>
            </button>
          </form>

          <!-- Divider -->
          <div class="divider">
            <span>or continue with</span>
          </div>

          <!-- Demo Buttons -->
          <div class="demo-section">
            <h3>Quick Demo Access</h3>
            <div class="demo-buttons">
              <button
                class="demo-btn admin-demo"
                (click)="demoLoginAsAdmin()"
                [disabled]="isLoading">
                <div class="demo-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                  </svg>
                </div>
                <div class="demo-content">
                  <span class="demo-title">Admin Access</span>
                  <span class="demo-subtitle">Full system control</span>
                </div>
                <div class="demo-arrow">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 17l5-5-5-5v10z"/>
                  </svg>
                </div>
              </button>

              <button
                class="demo-btn user-demo"
                (click)="demoLoginAsUser()"
                [disabled]="isLoading">
                <div class="demo-icon">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </div>
                <div class="demo-content">
                  <span class="demo-title">User Access</span>
                  <span class="demo-subtitle">Standard features</span>
                </div>
                <div class="demo-arrow">
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 17l5-5-5-5v10z"/>
                  </svg>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    /* Modern Login Container with Animated Background */
    .login-container {
      position: relative;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      overflow: hidden;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    /* Animated Background Shapes */
    .background-animation {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 1;
    }

    .floating-shapes {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      animation: float 20s infinite linear;
    }

    .shape-1 {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape-2 {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 10%;
      animation-delay: -5s;
    }

    .shape-3 {
      width: 60px;
      height: 60px;
      top: 80%;
      left: 20%;
      animation-delay: -10s;
    }

    .shape-4 {
      width: 100px;
      height: 100px;
      top: 10%;
      right: 30%;
      animation-delay: -15s;
    }

    .shape-5 {
      width: 140px;
      height: 140px;
      top: 40%;
      left: 50%;
      animation-delay: -7s;
    }

    @keyframes float {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
      }
    }

    /* Main Content */
    .login-content {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 40px;
      width: 100%;
      max-width: 480px;
    }

    /* Brand Section */
    .brand-section {
      text-align: center;
      color: white;
    }

    .brand-logo {
      margin-bottom: 20px;
    }

    .logo-icon {
      width: 80px;
      height: 80px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .logo-icon svg {
      width: 40px;
      height: 40px;
      color: white;
    }

    .brand-title {
      font-size: 3rem;
      font-weight: 700;
      margin: 0;
      background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .brand-subtitle {
      font-size: 1.1rem;
      margin: 8px 0 0 0;
      opacity: 0.9;
      font-weight: 400;
    }

    /* Login Card with Glassmorphism */
    .login-card {
      width: 100%;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      position: relative;
      overflow: hidden;
    }

    .login-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    }

    .card-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .card-header h2 {
      font-size: 2rem;
      font-weight: 700;
      color: #1a1a1a;
      margin: 0 0 8px 0;
    }

    .card-header p {
      color: #666;
      font-size: 1rem;
      margin: 0;
    }

    /* Modern Form Styling */
    .login-form {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .form-label {
      font-size: 0.875rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 4px;
    }

    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
    }

    .input-icon {
      position: absolute;
      left: 16px;
      z-index: 2;
      color: #9CA3AF;
      width: 20px;
      height: 20px;
    }

    .input-icon svg {
      width: 100%;
      height: 100%;
    }

    .form-input {
      width: 100%;
      height: 56px;
      padding: 0 16px 0 48px;
      border: 2px solid #E5E7EB;
      border-radius: 16px;
      font-size: 1rem;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      outline: none;
    }

    .form-input:focus {
      border-color: #667eea;
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    }

    .form-input::placeholder {
      color: #9CA3AF;
    }

    .password-toggle {
      position: absolute;
      right: 16px;
      background: none;
      border: none;
      color: #9CA3AF;
      cursor: pointer;
      padding: 4px;
      border-radius: 8px;
      transition: all 0.2s ease;
      width: 24px;
      height: 24px;
    }

    .password-toggle:hover {
      color: #667eea;
      background: rgba(102, 126, 234, 0.1);
    }

    .password-toggle svg {
      width: 100%;
      height: 100%;
    }

    .error-message {
      font-size: 0.875rem;
      color: #EF4444;
      margin-top: 4px;
    }

    /* Modern Login Button */
    .login-btn {
      height: 56px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 16px;
      color: white;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      margin-top: 8px;
    }

    .login-btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 12px 24px rgba(102, 126, 234, 0.3);
    }

    .login-btn:active:not(:disabled) {
      transform: translateY(0);
    }

    .login-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .btn-content, .btn-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .btn-content svg {
      width: 20px;
      height: 20px;
    }

    .spinner {
      width: 20px;
      height: 20px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Divider */
    .divider {
      position: relative;
      text-align: center;
      margin: 32px 0;
    }

    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, #E5E7EB, transparent);
    }

    .divider span {
      background: rgba(255, 255, 255, 0.95);
      padding: 0 16px;
      color: #6B7280;
      font-size: 0.875rem;
      font-weight: 500;
    }

    /* Demo Section */
    .demo-section h3 {
      text-align: center;
      font-size: 1.125rem;
      font-weight: 600;
      color: #374151;
      margin: 0 0 20px 0;
    }

    .demo-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .demo-btn {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border: 2px solid #E5E7EB;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;
      width: 100%;
    }

    .demo-btn:hover:not(:disabled) {
      border-color: #667eea;
      background: rgba(255, 255, 255, 0.95);
      transform: translateY(-2px);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    }

    .demo-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .demo-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex-shrink: 0;
    }

    .admin-demo .demo-icon {
      background: linear-gradient(135deg, #EF4444, #DC2626);
      color: white;
    }

    .user-demo .demo-icon {
      background: linear-gradient(135deg, #10B981, #059669);
      color: white;
    }

    .demo-icon svg {
      width: 20px;
      height: 20px;
    }

    .demo-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .demo-title {
      font-size: 1rem;
      font-weight: 600;
      color: #1F2937;
    }

    .demo-subtitle {
      font-size: 0.875rem;
      color: #6B7280;
    }

    .demo-arrow {
      width: 20px;
      height: 20px;
      color: #9CA3AF;
      transition: all 0.2s ease;
    }

    .demo-btn:hover .demo-arrow {
      color: #667eea;
      transform: translateX(4px);
    }

    .demo-arrow svg {
      width: 100%;
      height: 100%;
    }

    /* Responsive Design */
    @media (max-width: 640px) {
      .login-container {
        padding: 16px;
      }

      .login-content {
        gap: 24px;
      }

      .brand-title {
        font-size: 2.5rem;
      }

      .login-card {
        padding: 24px;
      }

      .card-header h2 {
        font-size: 1.75rem;
      }

      .demo-buttons {
        gap: 8px;
      }

      .demo-btn {
        padding: 12px 16px;
      }

      .demo-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
      }
    }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  hidePassword = true;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      const { email, password } = this.loginForm.value;
      
      this.authService.login({ email, password }).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.error?.message || 'Login failed', 'Close', { duration: 5000 });
        }
      });
    }
  }

  demoLoginAsAdmin(): void {
    this.isLoading = true;
    this.authService.demoLoginAsAdmin().subscribe({
      next: (response) => {
        this.isLoading = false;
        this.snackBar.open('Logged in as Admin!', 'Close', { duration: 3000 });
        this.router.navigate(['/dashboard']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Demo login failed', 'Close', { duration: 5000 });
      }
    });
  }

  demoLoginAsUser(): void {
    this.isLoading = true;
    this.authService.demoLoginAsUser().subscribe({
      next: (response) => {
        this.isLoading = false;
        this.snackBar.open('Logged in as User!', 'Close', { duration: 3000 });
        this.router.navigate(['/dashboard']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Demo login failed', 'Close', { duration: 5000 });
      }
    });
  }
}
