import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="login-container">
      <div class="login-card-wrapper">
        <mat-card class="login-card">
          <mat-card-header class="login-header">
            <div class="logo-section">
              <mat-icon class="logo-icon">assignment</mat-icon>
              <div class="title-section">
                <mat-card-title>Task Management</mat-card-title>
                <mat-card-subtitle>Professional Task Management System</mat-card-subtitle>
              </div>
            </div>
          </mat-card-header>

          <mat-card-content class="login-content">
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
              <div class="form-section">
                <h3 class="form-title">Sign In</h3>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Email Address</mat-label>
                  <input matInput
                         type="email"
                         formControlName="email"
                         placeholder="Enter your email"
                         autocomplete="email">
                  <mat-icon matSuffix color="primary">email</mat-icon>
                  <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                    Email is required
                  </mat-error>
                  <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                    Please enter a valid email address
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Password</mat-label>
                  <input matInput
                         [type]="hidePassword ? 'password' : 'text'"
                         formControlName="password"
                         placeholder="Enter your password"
                         autocomplete="current-password">
                  <button mat-icon-button matSuffix (click)="hidePassword = !hidePassword" type="button">
                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                    Password is required
                  </mat-error>
                </mat-form-field>

                <button mat-raised-button color="primary" type="submit"
                        [disabled]="loginForm.invalid || isLoading" class="full-width login-button">
                  <mat-spinner diameter="20" *ngIf="isLoading" class="button-spinner"></mat-spinner>
                  <mat-icon *ngIf="!isLoading">login</mat-icon>
                  <span *ngIf="!isLoading">Sign In</span>
                </button>
              </div>
            </form>

            <div class="divider">
              <span>OR</span>
            </div>

            <div class="demo-section">
              <h3 class="demo-title">Quick Demo Access</h3>
              <p class="demo-subtitle">Try the system with pre-configured accounts</p>

              <div class="demo-buttons">
                <button mat-raised-button
                        class="demo-button admin-demo"
                        (click)="demoLoginAsAdmin()"
                        [disabled]="isLoading">
                  <mat-icon>admin_panel_settings</mat-icon>
                  <div class="button-content">
                    <span class="button-title">Admin Demo</span>
                    <span class="button-subtitle">Full system access</span>
                  </div>
                </button>

                <button mat-raised-button
                        class="demo-button user-demo"
                        (click)="demoLoginAsUser()"
                        [disabled]="isLoading">
                  <mat-icon>person</mat-icon>
                  <div class="button-content">
                    <span class="button-title">User Demo</span>
                    <span class="button-subtitle">Standard user access</span>
                  </div>
                </button>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
      font-family: 'Roboto', sans-serif;
    }

    .login-card-wrapper {
      width: 100%;
      max-width: 480px;
      position: relative;
    }

    .login-card {
      width: 100%;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      background: white;
    }

    .login-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 32px 24px;
      text-align: center;
    }

    .logo-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .logo-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: white;
    }

    .title-section {
      text-align: center;
    }

    .login-header mat-card-title {
      font-size: 28px;
      font-weight: 600;
      margin: 0;
      color: white;
    }

    .login-header mat-card-subtitle {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      margin: 8px 0 0 0;
    }

    .login-content {
      padding: 32px 24px;
    }

    .form-section {
      margin-bottom: 32px;
    }

    .form-title {
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0 0 24px 0;
      text-align: center;
    }

    .login-form {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .full-width {
      width: 100%;
    }

    .mat-mdc-form-field {
      width: 100%;
    }

    .mat-mdc-form-field .mat-mdc-form-field-focus-overlay {
      background-color: rgba(102, 126, 234, 0.04);
    }

    .login-button {
      height: 56px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 8px;
      margin-top: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      position: relative;
      overflow: hidden;
    }

    .login-button:hover:not([disabled]) {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-1px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .login-button:disabled {
      background: #ccc;
    }

    .button-spinner {
      margin-right: 8px;
    }

    .divider {
      text-align: center;
      margin: 32px 0;
      position: relative;
    }

    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e0e0e0;
    }

    .divider span {
      background: white;
      padding: 0 16px;
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }

    .demo-section {
      text-align: center;
    }

    .demo-title {
      font-size: 20px;
      font-weight: 500;
      color: #333;
      margin: 0 0 8px 0;
    }

    .demo-subtitle {
      font-size: 14px;
      color: #666;
      margin: 0 0 24px 0;
    }

    .demo-buttons {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .demo-button {
      height: 64px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0 20px;
      text-align: left;
      border: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .demo-button mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
      margin-right: 16px;
    }

    .button-content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .button-title {
      font-size: 16px;
      font-weight: 600;
      line-height: 1.2;
    }

    .button-subtitle {
      font-size: 12px;
      opacity: 0.8;
      line-height: 1.2;
    }

    .admin-demo {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      color: white;
    }

    .admin-demo:hover:not([disabled]) {
      background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    }

    .user-demo {
      background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
      color: white;
    }

    .user-demo:hover:not([disabled]) {
      background: linear-gradient(135deg, #26a69a 0%, #00695c 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
    }

    .demo-button:disabled {
      background: #f5f5f5;
      color: #999;
      transform: none;
      box-shadow: none;
    }

    @media (max-width: 600px) {
      .login-container {
        padding: 16px;
      }

      .login-header {
        padding: 24px 16px;
      }

      .login-content {
        padding: 24px 16px;
      }

      .demo-buttons {
        gap: 12px;
      }

      .demo-button {
        height: 56px;
        padding: 0 16px;
      }
    }

    /* Material Design Form Field Fixes */
    ::ng-deep .mat-mdc-form-field-label {
      color: #666 !important;
    }

    ::ng-deep .mat-mdc-form-field-label.mdc-floating-label--float-above {
      color: #667eea !important;
    }

    ::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
      color: #667eea !important;
    }

    ::ng-deep .mat-mdc-form-field-outline .mat-mdc-notched-outline__notch {
      border-right: none !important;
    }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  hidePassword = true;
  isLoading = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      const { email, password } = this.loginForm.value;
      
      this.authService.login({ email, password }).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.error?.message || 'Login failed', 'Close', { duration: 5000 });
        }
      });
    }
  }

  demoLoginAsAdmin(): void {
    this.isLoading = true;
    this.authService.demoLoginAsAdmin().subscribe({
      next: (response) => {
        this.isLoading = false;
        this.snackBar.open('Logged in as Admin!', 'Close', { duration: 3000 });
        this.router.navigate(['/dashboard']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Demo login failed', 'Close', { duration: 5000 });
      }
    });
  }

  demoLoginAsUser(): void {
    this.isLoading = true;
    this.authService.demoLoginAsUser().subscribe({
      next: (response) => {
        this.isLoading = false;
        this.snackBar.open('Logged in as User!', 'Close', { duration: 3000 });
        this.router.navigate(['/dashboard']);
      },
      error: (error) => {
        this.isLoading = false;
        this.snackBar.open('Demo login failed', 'Close', { duration: 5000 });
      }
    });
  }
}
