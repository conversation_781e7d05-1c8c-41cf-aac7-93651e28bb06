<div class="app-container" *ngIf="showNavigation && isAuthenticated">
  <mat-sidenav-container class="sidenav-container">
    <mat-sidenav #drawer class="sidenav" fixedInViewport
        [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'"
        [mode]="(isHandset$ | async) ? 'over' : 'side'"
        [opened]="(isHandset$ | async) === false">
      <mat-toolbar>Menu</mat-toolbar>
      <mat-nav-list>
        <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
          <mat-icon>dashboard</mat-icon>
          <span>Dashboard</span>
        </a>
        <a mat-list-item routerLink="/tasks" routerLinkActive="active">
          <mat-icon>assignment</mat-icon>
          <span>Tasks</span>
        </a>
        <a mat-list-item routerLink="/tasks/create" routerLinkActive="active">
          <mat-icon>add_task</mat-icon>
          <span>Create Task</span>
        </a>
        <a mat-list-item routerLink="/users" routerLinkActive="active" *ngIf="isAdmin">
          <mat-icon>people</mat-icon>
          <span>Users</span>
        </a>
        <a mat-list-item routerLink="/profile" routerLinkActive="active">
          <mat-icon>person</mat-icon>
          <span>Profile</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <mat-sidenav-content>
      <mat-toolbar color="primary">
        <button
          type="button"
          aria-label="Toggle sidenav"
          mat-icon-button
          (click)="drawer.toggle()"
          *ngIf="isHandset$ | async">
          <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
        </button>
        <span>{{ title }}</span>
        <span class="spacer"></span>

        <span class="user-info" *ngIf="currentUser">
          Welcome, {{ currentUser.name }}
          <span class="role-badge" [class]="currentUser.role.toLowerCase()">
            {{ currentUser.role }}
          </span>
        </span>

        <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="User menu">
          <mat-icon>account_circle</mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <button mat-menu-item routerLink="/profile">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </mat-toolbar>

      <div class="content">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>

<div class="auth-container" *ngIf="!showNavigation || !isAuthenticated">
  <router-outlet></router-outlet>
</div>

