<div class="app-container">
  <header class="app-header">
    <h1>Task Management System</h1>
    <nav>
      <button class="demo-btn" (click)="loginAsAdmin()">Demo as Admin</button>
      <button class="demo-btn" (click)="loginAsUser()">Demo as User</button>
      <button class="api-btn" (click)="openApiDemo()">API Demo</button>
    </nav>
  </header>

  <main class="app-main">
    <router-outlet></router-outlet>

    <!-- Demo welcome section -->
    <div class="welcome-section">
      <h2>Task Management System</h2>
      <p class="subtitle">Complete Full-Stack Application with Angular + Node.js + MySQL</p>

      <div class="demo-info">
        <h3>🚀 Live Demo Available</h3>
        <p>Experience the complete task management system with sample data and all features enabled.</p>

        <div class="demo-credentials">
          <div class="credential-card admin">
            <h4>👑 Admin Access</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> password123</p>
            <p>Full access including user management</p>
            <button class="login-btn admin-btn" (click)="loginAsAdmin()">Login as Admin</button>
          </div>

          <div class="credential-card user">
            <h4>👤 User Access</h4>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> password123</p>
            <p>Standard user with task management</p>
            <button class="login-btn user-btn" (click)="loginAsUser()">Login as User</button>
          </div>
        </div>
      </div>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">👥</div>
          <h3>User Management</h3>
          <p>Role-based access control with Admin and User roles</p>
          <ul>
            <li>User CRUD operations (Admin only)</li>
            <li>Login history tracking</li>
            <li>Profile management</li>
          </ul>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📋</div>
          <h3>Task Management</h3>
          <p>Complete task lifecycle with status workflow</p>
          <ul>
            <li>Status: New → ToDo → In Progress → Blocked/Closed</li>
            <li>Assignment and due dates</li>
            <li>Advanced filtering and search</li>
          </ul>
        </div>

        <div class="feature-card">
          <div class="feature-icon">💬</div>
          <h3>Comments & Links</h3>
          <p>Collaboration features for team communication</p>
          <ul>
            <li>Task comments with timestamps</li>
            <li>Linked items and references</li>
            <li>Activity tracking</li>
          </ul>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🔒</div>
          <h3>Security & Auth</h3>
          <p>Enterprise-grade security features</p>
          <ul>
            <li>JWT token authentication</li>
            <li>Password hashing with bcrypt</li>
            <li>CORS and rate limiting</li>
          </ul>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <h3>Analytics & Reports</h3>
          <p>Comprehensive statistics and insights</p>
          <ul>
            <li>Task status distribution</li>
            <li>User activity logs</li>
            <li>Performance metrics</li>
          </ul>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <h3>Modern UI/UX</h3>
          <p>Beautiful and responsive design</p>
          <ul>
            <li>Angular Material components</li>
            <li>Responsive grid layouts</li>
            <li>Intuitive user interface</li>
          </ul>
        </div>
      </div>

      <div class="tech-stack">
        <h3>🛠 Technology Stack</h3>
        <div class="tech-categories">
          <div class="tech-category">
            <h4>Frontend</h4>
            <div class="tech-tags">
              <span class="tech-tag">Angular 17</span>
              <span class="tech-tag">TypeScript</span>
              <span class="tech-tag">Angular Material</span>
              <span class="tech-tag">SCSS</span>
            </div>
          </div>

          <div class="tech-category">
            <h4>Backend</h4>
            <div class="tech-tags">
              <span class="tech-tag">Node.js</span>
              <span class="tech-tag">Express.js</span>
              <span class="tech-tag">JWT</span>
              <span class="tech-tag">bcrypt</span>
            </div>
          </div>

          <div class="tech-category">
            <h4>Database</h4>
            <div class="tech-tags">
              <span class="tech-tag">MySQL 8.0</span>
              <span class="tech-tag">Connection Pooling</span>
              <span class="tech-tag">Indexed Queries</span>
            </div>
          </div>
        </div>
      </div>

      <div class="api-section">
        <h3>🔌 API Endpoints</h3>
        <p>RESTful API with comprehensive documentation</p>
        <button class="api-demo-btn" (click)="openApiDemo()">
          <span>🚀</span>
          Explore API Demo
        </button>
      </div>
    </div>
  </main>
</div>