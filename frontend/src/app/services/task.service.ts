import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Task {
  id: number;
  title: string;
  description: string;
  assignee_id: number | null;
  assignee_name?: string;
  assignee_email?: string;
  status: 'New' | 'ToDo' | 'In Progress' | 'Blocked' | 'Closed';
  due_date: string | null;
  created_by: number;
  creator_name?: string;
  creator_email?: string;
  created_at: string;
  updated_at: string;
  comments?: TaskComment[];
  linkedItems?: LinkedItem[];
}

export interface TaskFilters {
  status?: string;
  assigneeId?: number;
  createdBy?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
  dueDateFrom?: string;
  dueDateTo?: string;
}

export interface TaskStats {
  total: number;
  new_tasks: number;
  todo: number;
  in_progress: number;
  blocked: number;
  closed: number;
  overdue: number;
}

export interface CreateTaskRequest {
  title: string;
  description?: string;
  assigneeId?: number;
  status?: 'New' | 'ToDo' | 'In Progress' | 'Blocked' | 'Closed';
  dueDate?: string;
}

export interface UpdateTaskRequest {
  title: string;
  description?: string;
  assigneeId?: number;
  status: 'New' | 'ToDo' | 'In Progress' | 'Blocked' | 'Closed';
  dueDate?: string;
}

export interface TaskComment {
  id: number;
  task_id: number;
  user_id: number;
  author_name: string;
  author_email: string;
  content: string;
  timestamp: string;
}

export interface LinkedItem {
  id: number;
  task_id: number;
  link: string;
  title: string;
  created_at: string;
}

export interface CreateCommentRequest {
  content: string;
}

export interface CreateLinkedItemRequest {
  link: string;
  title: string;
}

@Injectable({
  providedIn: 'root'
})
export class TaskService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getTasks(filters?: TaskFilters): Observable<any> {
    let params = new HttpParams();
    
    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = (filters as any)[key];
        if (value !== undefined && value !== null && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get(`${this.apiUrl}/tasks`, { params });
  }

  getTask(id: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/tasks/${id}`);
  }

  createTask(task: CreateTaskRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/tasks`, task);
  }

  updateTask(id: number, task: UpdateTaskRequest): Observable<any> {
    return this.http.put(`${this.apiUrl}/tasks/${id}`, task);
  }

  updateTaskStatus(id: number, status: string): Observable<any> {
    return this.http.patch(`${this.apiUrl}/tasks/${id}/status`, { status });
  }

  deleteTask(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/tasks/${id}`);
  }

  getTaskStats(): Observable<{ stats: TaskStats }> {
    return this.http.get<{ stats: TaskStats }>(`${this.apiUrl}/tasks/stats`);
  }

  // Comment methods
  addComment(taskId: number, content: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/tasks/${taskId}/comments`, { content });
  }

  // Linked item methods
  addLinkedItem(taskId: number, link: string, title: string): Observable<any> {
    return this.http.post(`${this.apiUrl}/tasks/${taskId}/linked-items`, { link, title });
  }

  deleteLinkedItem(taskId: number, itemId: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/tasks/${taskId}/linked-items/${itemId}`);
  }
}
