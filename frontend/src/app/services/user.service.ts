import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { User, LoginLog } from './auth.service';

export interface UserFilters {
  role?: 'Admin' | 'User';
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  name: string;
  role: 'Admin' | 'User';
}

export interface UpdateUserRequest {
  username?: string;
  email: string;
  name: string;
  role?: 'Admin' | 'User';
}

export interface ChangePasswordRequest {
  currentPassword?: string;
  newPassword: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  getUsers(filters?: UserFilters): Observable<any> {
    let params = new HttpParams();
    
    if (filters) {
      Object.keys(filters).forEach(key => {
        const value = (filters as any)[key];
        if (value !== undefined && value !== null && value !== '') {
          params = params.set(key, value.toString());
        }
      });
    }

    return this.http.get(`${this.apiUrl}/users`, { params });
  }

  getUser(id: number): Observable<{ user: User }> {
    return this.http.get<{ user: User }>(`${this.apiUrl}/users/${id}`);
  }

  createUser(user: CreateUserRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/users`, user);
  }

  updateUser(id: number, user: UpdateUserRequest): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/${id}`, user);
  }

  changePassword(id: number, passwordData: ChangePasswordRequest): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/${id}/password`, passwordData);
  }

  deleteUser(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/users/${id}`);
  }

  getUserLoginLogs(id: number, limit = 50): Observable<{ logs: LoginLog[] }> {
    const params = new HttpParams().set('limit', limit.toString());
    return this.http.get<{ logs: LoginLog[] }>(`${this.apiUrl}/users/${id}/login-logs`, { params });
  }
}
