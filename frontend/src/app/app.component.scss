.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.sidenav-container {
  flex: 1;
}

.sidenav {
  width: 250px;
}

.sidenav .mat-toolbar {
  background: inherit;
}

.mat-toolbar.mat-primary {
  position: sticky;
  top: 0;
  z-index: 1;
}

.spacer {
  flex: 1 1 auto;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 16px;
  font-size: 14px;
}

.role-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-badge.admin {
  background-color: #ff5722;
}

.role-badge.user {
  background-color: #2196f3;
}

.content {
  padding: 0;
  height: calc(100vh - 64px);
  overflow-y: auto;
}

.auth-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Navigation active state
.mat-list-item.active {
  background-color: rgba(0, 0, 0, 0.04);
}

// Mobile responsive
@media (max-width: 768px) {
  .user-info {
    display: none;
  }
}


