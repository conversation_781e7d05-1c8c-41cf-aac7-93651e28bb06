.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);

  h1 {
    color: white;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
  }

  nav {
    display: flex;
    gap: 0.5rem;

    button {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
      }

      &.demo-btn {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        border: none;

        &:hover {
          background: linear-gradient(45deg, #45a049, #4CAF50);
        }
      }

      &.api-btn {
        background: linear-gradient(45deg, #2196F3, #1976D2);
        border: none;

        &:hover {
          background: linear-gradient(45deg, #1976D2, #2196F3);
        }
      }
    }
  }
}

.app-main {
  padding: 2rem;
}

.welcome-section {
  max-width: 1400px;
  margin: 0 auto;
  color: white;

  h2 {
    font-size: 3.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .subtitle {
    font-size: 1.3rem;
    text-align: center;
    margin-bottom: 3rem;
    opacity: 0.9;
    font-weight: 300;
  }
}

.demo-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 3rem;
  text-align: center;

  h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #fff;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }
}

.demo-credentials {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.credential-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  text-align: left;

  h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #fff;
  }

  p {
    margin: 0.5rem 0;
    font-size: 0.95rem;
    opacity: 0.9;

    strong {
      color: #fff;
    }
  }

  &.admin {
    border-left: 4px solid #ff6b6b;
  }

  &.user {
    border-left: 4px solid #4ecdc4;
  }
}

.login-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border: none;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 1rem;
  width: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
  }

  &.admin-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);

    &:hover {
      box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    }
  }

  &.user-btn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);

    &:hover {
      box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
    }
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  text-align: left;

  &:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
  }

  .feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
  }

  p {
    opacity: 0.9;
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding: 0.3rem 0;
      opacity: 0.8;
      position: relative;
      padding-left: 1.5rem;

      &:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #4CAF50;
        font-weight: bold;
      }
    }
  }
}
.tech-stack {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin: 3rem 0;

  h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #fff;
    text-align: center;
  }
}

.tech-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.tech-category {
  text-align: center;

  h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #fff;
  }
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.tech-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
  }
}

.api-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  margin: 3rem 0;

  h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #fff;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
  }
}

.api-demo-btn {
  background: linear-gradient(45deg, #2196F3, #1976D2);
  border: none;
  color: white;
  padding: 1rem 2rem;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
  }

  span {
    font-size: 1.3rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;

    nav {
      flex-direction: column;
      width: 100%;

      button {
        width: 100%;
      }
    }
  }

  .app-main {
    padding: 1rem;
  }

  .welcome-section h2 {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .demo-credentials {
    grid-template-columns: 1fr;
  }

  .tech-categories {
    grid-template-columns: 1fr;
  }
}