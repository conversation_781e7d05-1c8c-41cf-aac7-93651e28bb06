import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { AuthService } from './services/auth.service';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  title = 'task-management-frontend';

  constructor(
    private authService: AuthService,
    private http: HttpClient
  ) {}

  get isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  async loginAsAdmin() {
    try {
      const response = await this.http.post<any>(`${environment.apiUrl}/demo/login`, {
        email: '<EMAIL>',
        password: 'password123'
      }).toPromise();

      if (response?.token) {
        localStorage.setItem('token', response.token);
        this.authService['currentUserSubject'].next(response.user);
        alert('✅ Logged in as Admin! You now have full access to all features including user management.');
      }
    } catch (error) {
      console.error('Admin login failed:', error);
      alert('❌ Admin login failed. Please check the console for details.');
    }
  }

  async loginAsUser() {
    try {
      const response = await this.http.post<any>(`${environment.apiUrl}/demo/login`, {
        email: '<EMAIL>',
        password: 'password123'
      }).toPromise();

      if (response?.token) {
        localStorage.setItem('token', response.token);
        this.authService['currentUserSubject'].next(response.user);
        alert('✅ Logged in as User! You can manage tasks, add comments, and view your profile.');
      }
    } catch (error) {
      console.error('User login failed:', error);
      alert('❌ User login failed. Please check the console for details.');
    }
  }

  openApiDemo() {
    window.open(`${environment.apiUrl}/demo/info`, '_blank');
  }
}
