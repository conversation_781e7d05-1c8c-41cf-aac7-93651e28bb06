/* Global Styles for Task Management System */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for Consistent Theming */
:root {
  --primary-color: #667eea;
  --primary-dark: #5a6fd8;
  --secondary-color: #764ba2;
  --accent-color: #4ecdc4;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  --border-color: #e2e8f0;
  --border-light: #f1f5f9;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* Global Reset and Base Styles */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'Roboto', "Helvetica Neue", sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.4;
  margin: 0;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Material Design Overrides */
.mat-mdc-card {
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--border-color) !important;
}

.mat-mdc-button {
  border-radius: var(--radius-lg) !important;
  font-weight: 600 !important;
  text-transform: none !important;
}

.mat-mdc-raised-button {
  box-shadow: var(--shadow-md) !important;
}

.mat-mdc-raised-button:hover {
  box-shadow: var(--shadow-lg) !important;
}

/* Form Field Improvements */
.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field .mat-mdc-form-field-outline {
  border-radius: var(--radius-md) !important;
}

.mat-mdc-form-field-label {
  color: var(--text-secondary) !important;
}

.mat-mdc-form-field-label.mdc-floating-label--float-above {
  color: var(--primary-color) !important;
}

.mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
  color: var(--primary-color) !important;
}

/* Select Panel */
.mat-mdc-select-panel {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-xl) !important;
  border: 1px solid var(--border-color) !important;
}

.mat-mdc-option {
  border-radius: var(--radius-sm) !important;
  margin: 2px 8px !important;
}

.mat-mdc-option:hover {
  background-color: rgba(102, 126, 234, 0.08) !important;
}
